# Figma vs GitHub OAuth Implementation Mismatch Analysis

## Overview
This document analyzes the mismatches between the Figma OAuth implementation guide and the GitHub OAuth implementation guide, identifying inconsistencies that could cause integration issues with the existing Alpine Intellect codebase. The analysis ensures both implementations follow scalable architecture patterns that preserve existing infrastructure.

## Critical Requirements for Implementation
1. **Preserve Existing Infrastructure**: Do not modify or break any existing authentication systems
2. **Extend, Don't Replace**: Build upon existing `handleDeepLink` function without altering current logic
3. **Maintain Reusability**: Design modular components for future OAuth providers (Slack, Discord, etc.)
4. **Independent Scalability**: Enable/disable each OAuth service independently
5. **Follow Established Patterns**: Use same keytar service, IPC events, and protocol schemes

## Critical Mismatches Found and Corrected

### 🚨 **Mismatch 1: Protocol Scheme Inconsistency**

**❌ Original Figma Implementation:**
```yaml
protocol_scheme: "alpine"
redirect_uri: "alpine://mcp-auth/figma"
```

**✅ Corrected to Match GitHub Pattern:**
```yaml
protocol_scheme: "thealpinecode.alpineintellect"
redirect_uri: "thealpinecode.alpineintellect://auth-callback/figma"
```

**Impact:** The original Figma implementation would have required registering a new protocol handler, while the corrected version uses the existing protocol handler already registered in `src/electron/main.ts`.

### 🚨 **Mismatch 2: Deep Link Handler Integration**

**❌ Original Figma Implementation:**
```yaml
implementation:
  protocol_handler: "src/electron/protocol/oauth-protocol-handler.ts"
  callback_processor: "src/mcp/auth/oauth-callback-processor.ts"
```

**✅ Corrected to Match GitHub Pattern:**
```yaml
implementation:
  existing_handler_extension: "src/electron/main.ts:handleDeepLink"
  callback_processor: "src/mcp/auth/figma-oauth-callback-processor.ts"
```

**Impact:** The original would have created new protocol handling infrastructure, while the corrected version extends the existing `handleDeepLink` function.

### 🚨 **Mismatch 3: Keytar Service Name Inconsistency**

**❌ Original Figma Implementation:**
```yaml
keytar_integration:
  service_name: "alpine-mcp-figma"
  client_secret: "${KEYTAR:figma-client-secret}"
```

**✅ Corrected to Match GitHub Pattern:**
```yaml
keytar_integration:
  service_name: "alpine-app"  # Same as existing Alpine auth
  client_secret: "${KEYTAR:alpine-app:figma-client-secret}"
```

**Impact:** The original would have created a separate keytar service, while the corrected version uses the existing "alpine-app" service for consistency.

### 🚨 **Mismatch 4: URL Pattern Inconsistency**

**❌ Original Figma Implementation:**
```yaml
url_pattern: "alpine://mcp-auth/figma*"
redirect_capture: "alpine://mcp-auth/figma?code=...&state=..."
```

**✅ Corrected to Match GitHub Pattern:**
```yaml
url_pattern: "thealpinecode.alpineintellect://auth-callback/figma*"
redirect_capture: "thealpinecode.alpineintellect://auth-callback/figma?code=...&state=..."
```

**Impact:** Ensures consistent URL pattern matching in the existing deep link handler.

## Additional Corrections Made

### 1. **Service Detection Enhancement**
Added service detection capability to distinguish between different OAuth providers:
```yaml
validation_checks:
  - service_detection: "Detect Figma OAuth vs existing Alpine auth"
```

### 2. **Integration Requirements Alignment**
Added missing integration requirements to match GitHub implementation:
```yaml
integration_requirements:
  - [ ] Uses existing thealpinecode.alpineintellect:// protocol handler
  - [ ] Integrates with existing handleDeepLink function
  - [ ] Uses same keytar service name (alpine-app) as existing auth
  - [ ] Follows same IPC event patterns as existing authentication
```

### 3. **Security Validation Alignment**
Updated redirect URI validation to match the corrected scheme:
```yaml
redirect_uri_validation:
  - exact_match_required: "thealpinecode.alpineintellect://auth-callback/figma only"
  - protocol_scheme_validation: "Only thealpinecode.alpineintellect:// scheme allowed"
```

## Implementation Files Affected

### Files That Need Updates:
1. **`src/electron/main.ts`** - Extend existing `handleDeepLink` function
2. **`src/mcp/auth/figma-oauth-service.ts`** - Use correct redirect URI
3. **`src/mcp/auth/figma-oauth-callback-processor.ts`** - Handle Figma-specific callbacks
4. **UI Components** - Use consistent IPC event patterns

### Files That Don't Need Changes:
- No new protocol handler files needed
- No new keytar service setup required
- Existing deep link infrastructure can be reused

## Benefits of Corrections

1. **Consistency**: Both GitHub and Figma OAuth now use the same protocol scheme and infrastructure
2. **Maintainability**: Single protocol handler to maintain instead of multiple
3. **Security**: Consistent security patterns across all OAuth implementations
4. **User Experience**: Unified deep link handling behavior
5. **Development Efficiency**: Reuse existing, tested infrastructure
6. **Scalability**: Architecture supports unlimited OAuth providers without breaking changes
7. **Independence**: Each OAuth service can be enabled/disabled independently
8. **Future-Proof**: Established patterns for adding new OAuth providers (Slack, Discord, etc.)
9. **Zero Breaking Changes**: All existing functionality preserved and unchanged

## Next Steps

1. **Implement OAuth Service Registry**: Create centralized service management system
2. **Create Callback Router**: Implement intelligent routing for OAuth callbacks
3. **Extend Deep Link Handler**: Add OAuth callback routing WITHOUT modifying existing logic
4. **Implement Service Classes**: Create GitHub and Figma OAuth service implementations
5. **Add Service Detection**: Implement logic to distinguish between different OAuth providers
6. **Create UI Components**: Build service-specific UI components following established patterns
7. **Test Independent Operation**: Verify each OAuth service can be enabled/disabled independently
8. **Verify Backward Compatibility**: Ensure all existing functionality remains unchanged

## Verification Checklist

### Infrastructure Preservation
- [ ] All existing Alpine auth functionality remains unchanged
- [ ] Existing `handleDeepLink` function logic preserved
- [ ] Current protocol handler registration unchanged
- [ ] All existing IPC event patterns maintained

### OAuth Implementation
- [ ] Figma OAuth uses `thealpinecode.alpineintellect://auth-callback/figma` redirect URI
- [ ] GitHub OAuth uses `thealpinecode.alpineintellect://auth-callback/github` redirect URI
- [ ] Both services use "alpine-app" keytar service name
- [ ] Service detection distinguishes between Alpine auth, GitHub, and Figma
- [ ] Callback routing delegates to appropriate service processors

### Scalability Features
- [ ] OAuth service registry implemented for centralized management
- [ ] Callback router handles multiple OAuth providers
- [ ] Service interface defined for future OAuth providers
- [ ] Independent enable/disable functionality for each service
- [ ] Modular architecture supports unlimited OAuth providers

### Backward Compatibility
- [ ] No breaking changes to existing authentication systems
- [ ] All current deep link functionality preserved
- [ ] Existing token storage patterns maintained
- [ ] Current UI components continue to work unchanged
