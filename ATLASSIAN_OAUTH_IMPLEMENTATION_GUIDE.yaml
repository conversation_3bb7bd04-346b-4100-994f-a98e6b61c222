feature:
  name: Atlassian OAuth Authentication Flow - HTTP/SSE Implementation Guide
  description: |
    Detailed stage-by-stage implementation guide for OAuth 2.0 with PKCE authentication flow
    for Atlassian integration in Alpine Intellect MCP system using HTTP/SSE transport. This
    implementation follows the established OAuth + HTTP/SSE pattern created for GitHub,
    supporting Jira, Confluence, and Bitbucket services through unified Atlassian API.

  owner: developer-platform
  status: implementation-ready
  target_release: v1.4.0

  design_principles:
    follow_github_pattern: "Use same OAuth + HTTP/SSE pattern established for GitHub"
    http_sse_transport: "HTTP/SSE transport as primary communication mechanism"
    oauth_consistency: "OAuth 2.0 + PKCE authentication matching GitHub implementation"
    remote_service: "Atlassian as remote HTTP/SSE service (consistent with GitHub)"
    template_reuse: "Demonstrate reusable OAuth + HTTP/SSE template for future services"
    preserve_existing: "Do not modify or break existing Alpine auth or protocol handlers"
    extend_not_replace: "Build upon existing handleDeepLink function without altering current logic"
    independent_scalability: "Enable/disable Atlassian OAuth independently of other services"
    unified_atlassian_api: "Single integration point for Jira, Confluence, and Bitbucket"

  atlassian_services:
    jira:
      description: "Issue tracking and project management"
      scopes: ["read:jira-work", "write:jira-work", "read:jira-user"]
      tools: ["create_jira_issue", "update_jira_issue", "search_jira_issues", "get_jira_issue"]
    confluence:
      description: "Team collaboration and documentation"
      scopes: ["read:confluence-content.all", "write:confluence-content", "read:confluence-space.summary"]
      tools: ["create_confluence_page", "update_confluence_page", "search_confluence_content", "get_confluence_page"]
    bitbucket:
      description: "Git repository hosting and code collaboration"
      scopes: ["repository:read", "repository:write", "pullrequest:read", "pullrequest:write", "issue:read", "issue:write"]
      tools: ["create_bitbucket_repository", "create_bitbucket_pull_request", "search_bitbucket_code", "get_bitbucket_file_content"]

  implementation_stages:
    stage_1_mcp_configuration:
      description: "Configure Atlassian server in MCP configuration system"
      implementation_file: "mcp-config.json"
      configuration:
        server_id: "atlassian"
        transport: "http-sse"
        endpoints:
          http: "https://api.atlassian.com/mcp/v1"
          sse: "https://api.atlassian.com/mcp/v1/events"
          discovery: "https://api.atlassian.com/mcp/.well-known/oauth-authorization-server"
        auth:
          type: "oauth"
          client_id: "${ENV:ATLASSIAN_CLIENT_ID}"
          redirect_uri: "thealpinecode.alpineintellect://auth-callback/atlassian"
          scopes: [
            "read:jira-work", "write:jira-work", "read:jira-user",
            "read:confluence-content.all", "write:confluence-content", "read:confluence-space.summary",
            "repository:read", "repository:write", "pullrequest:read", "pullrequest:write",
            "issue:read", "issue:write"
          ]
          pkce: true
          authorization_endpoint: "https://auth.atlassian.com/authorize"
          token_endpoint: "https://auth.atlassian.com/oauth/token"
        capabilities: ["tools", "resources", "notifications"]
        tools: [
          "create_jira_issue", "update_jira_issue", "search_jira_issues", "get_jira_issue",
          "create_confluence_page", "update_confluence_page", "search_confluence_content", "get_confluence_page",
          "create_bitbucket_repository", "create_bitbucket_pull_request", "search_bitbucket_code", "get_bitbucket_file_content",
          "list_accessible_resources"
        ]

    stage_2_oauth_flow_implementation:
      description: "Complete PKCE OAuth 2.0 flow with Atlassian"
      substages:
        substage_2a_authorization_url_generation:
          description: "Generate secure authorization URL with PKCE parameters"
          implementation_file: "src/mcp/auth/atlassian-oauth-service.ts"
          security_measures:
            - pkce_code_verifier_generation: "Cryptographically secure random string (128 chars)"
            - pkce_code_challenge: "SHA256 hash of code verifier, base64url encoded"
            - state_parameter: "Cryptographically secure random string (32 chars)"
            - nonce_parameter: "Additional security for token validation"
          parameters:
            client_id: "${ENV:ATLASSIAN_CLIENT_ID}"
            redirect_uri: "thealpinecode.alpineintellect://auth-callback/atlassian"
            scope: "read:jira-work write:jira-work read:jira-user read:confluence-content.all write:confluence-content read:confluence-space.summary repository:read repository:write pullrequest:read pullrequest:write issue:read issue:write"
            response_type: "code"
            code_challenge_method: "S256"
            code_challenge: "${GENERATED_CODE_CHALLENGE}"
            state: "${GENERATED_STATE}"
            audience: "api.atlassian.com"
            prompt: "consent"

        substage_2b_deep_link_callback_handling:
          description: "Handle OAuth callback via existing deep link infrastructure"
          implementation:
            existing_handler_extension: "src/electron/main.ts:handleDeepLink"
            callback_processor: "src/mcp/auth/atlassian-oauth-callback-processor.ts"
          callback_url_pattern: "thealpinecode.alpineintellect://auth-callback/atlassian?code=...&state=..."
          validation_steps:
            - state_parameter_verification: "Verify state matches stored value"
            - authorization_code_extraction: "Extract code from callback URL"
            - error_handling: "Handle OAuth error responses (access_denied, invalid_request, etc.)"

        substage_2c_authorization_code_exchange:
          description: "Exchange authorization code for access tokens via HTTP"
          implementation_file: "src/mcp/auth/token-exchange-service.ts"
          callback_handling:
            redirect_capture: "thealpinecode.alpineintellect://auth-callback/atlassian?code=...&state=..."
            state_validation: "Verify state parameter matches stored value"
            code_extraction: "Extract authorization code from callback URL"
          token_exchange_request:
            endpoint: "https://auth.atlassian.com/oauth/token"
            method: "POST"
            headers:
              content_type: "application/json"
              accept: "application/json"
            body_parameters:
              grant_type: "authorization_code"
              client_id: "${ENV:ATLASSIAN_CLIENT_ID}"
              client_secret: "${KEYTAR:alpine-app:atlassian-client-secret}"
              code: "${AUTHORIZATION_CODE}"
              redirect_uri: "thealpinecode.alpineintellect://auth-callback/atlassian"
              code_verifier: "${STORED_CODE_VERIFIER}"
          token_response:
            access_token: "Bearer token for API access"
            refresh_token: "Token for refreshing access"
            expires_in: "Token expiration time in seconds"
            token_type: "Bearer"
            scope: "Granted scopes"

        substage_2d_token_storage_and_refresh:
          description: "Secure token storage and automatic refresh mechanism"
          implementation_file: "src/mcp/auth/token-manager.ts"
          keytar_integration:
            service_name: "alpine-app"  # Same as existing Alpine auth
            access_token: "${KEYTAR:alpine-app:atlassian-access-token}"
            refresh_token: "${KEYTAR:alpine-app:atlassian-refresh-token}"
            client_secret: "${KEYTAR:alpine-app:atlassian-client-secret}"
          refresh_mechanism:
            trigger: "Automatic refresh 5 minutes before expiration"
            endpoint: "https://auth.atlassian.com/oauth/token"
            refresh_request:
              grant_type: "refresh_token"
              client_id: "${ENV:ATLASSIAN_CLIENT_ID}"
              client_secret: "${KEYTAR:alpine-app:atlassian-client-secret}"
              refresh_token: "${STORED_REFRESH_TOKEN}"

    stage_3_http_sse_transport:
      description: "HTTP/SSE transport implementation for Atlassian API communication"
      implementation_file: "src/mcp/transport/atlassian-http-sse-transport.ts"
      http_client:
        base_url: "https://api.atlassian.com/mcp/v1"
        authentication: "Authorization: Bearer ${ACCESS_TOKEN}"
        headers:
          content_type: "application/json"
          accept: "application/json"
          user_agent: "Alpine-Intellect-MCP/1.0"
        timeout: 30000
        retry_policy:
          max_attempts: 3
          backoff_strategy: "exponential"
          retry_delay: 1000
      sse_client:
        endpoint: "https://api.atlassian.com/mcp/v1/events"
        authentication: "Authorization: Bearer ${ACCESS_TOKEN}"
        reconnection_strategy:
          auto_reconnect: true
          max_reconnect_attempts: 5
          reconnect_delay: 2000
        event_handling:
          - jira_issue_updated: "Real-time Jira issue notifications"
          - confluence_page_updated: "Real-time Confluence page notifications"
          - bitbucket_push: "Real-time Bitbucket repository notifications"
          - pullrequest_updated: "Real-time pull request notifications"

    stage_4_service_client_implementation:
      description: "Atlassian service client with unified API access"
      implementation_file: "src/mcp/clients/atlassian-service.ts"
      base_class: "RemoteServiceClient"
      service_methods:
        resource_discovery:
          list_accessible_resources: "Get user's accessible Atlassian sites and resources"
        jira_operations:
          create_jira_issue: "Create new Jira issue"
          update_jira_issue: "Update existing Jira issue"
          search_jira_issues: "Search Jira issues with JQL"
          get_jira_issue: "Get specific Jira issue details"
        confluence_operations:
          create_confluence_page: "Create new Confluence page"
          update_confluence_page: "Update existing Confluence page"
          search_confluence_content: "Search Confluence content"
          get_confluence_page: "Get specific Confluence page"
        bitbucket_operations:
          create_bitbucket_repository: "Create new Bitbucket repository"
          create_bitbucket_pull_request: "Create new pull request"
          search_bitbucket_code: "Search code in Bitbucket repositories"
          get_bitbucket_file_content: "Get file content from Bitbucket repository"

    stage_5_service_integration:
      description: "Enable Code Assistant Agent to use authenticated Atlassian tools"
      implementation:
        agent_integration: "src/agents/code-assistant-agent.ts"
        atlassian_client: "src/mcp/clients/atlassian-remote-client.ts"
        tool_executor: "src/mcp/execution/atlassian-tool-executor.ts"
      agent_workflow:
        tool_discovery:
          - available_tools: ["create_jira_issue", "create_confluence_page", "create_bitbucket_repository", "search_jira_issues", "search_confluence_content", "search_bitbucket_code"]
          - capability_check: "Verify authentication status before tool execution"
          - permission_validation: "Check token scopes match tool requirements"
        tool_execution:
          - authentication_header: "Authorization: Bearer ${ACCESS_TOKEN}"
          - api_endpoint_mapping: "Map MCP tools to Atlassian API endpoints"
          - resource_context: "Include accessible resource IDs in API calls"
          - error_handling: "Handle rate limits, permissions, and service-specific errors"

  security_considerations:
    oauth_security:
      - pkce_implementation: "PKCE prevents authorization code interception attacks"
      - state_parameter: "State parameter prevents CSRF attacks"
      - secure_storage: "Tokens stored in OS-level secure storage (Keytar)"
      - token_rotation: "Automatic token refresh maintains security"
    api_security:
      - scope_validation: "Verify token scopes before tool execution"
      - resource_isolation: "Tools operate only on accessible resources"
      - rate_limiting: "Respect Atlassian API rate limits"
      - audit_logging: "Log all API operations for security audit"

  error_handling:
    oauth_errors:
      - access_denied: "User denied authorization - show retry option"
      - invalid_client: "Invalid client configuration - check environment variables"
      - invalid_grant: "Invalid authorization code - restart OAuth flow"
      - invalid_scope: "Requested scope not available - adjust scope requirements"
    api_errors:
      - unauthorized: "Token expired or invalid - trigger token refresh"
      - forbidden: "Insufficient permissions - show scope requirements"
      - rate_limited: "API rate limit exceeded - implement backoff strategy"
      - service_unavailable: "Atlassian service down - show service status"

  testing_strategy:
    unit_tests:
      - oauth_flow_components: "Test PKCE generation, token exchange, refresh"
      - http_sse_transport: "Test HTTP requests and SSE event handling"
      - service_client_methods: "Test all Atlassian API tool implementations"
    integration_tests:
      - end_to_end_oauth: "Complete OAuth flow with test credentials"
      - api_tool_execution: "Execute tools against Atlassian test environment"
      - error_scenario_handling: "Test error conditions and recovery"
    manual_testing:
      - user_oauth_flow: "Manual OAuth flow testing with real Atlassian account"
      - agent_tool_usage: "Test Code Assistant Agent using Atlassian tools"
      - multi_service_integration: "Test Jira, Confluence, and Bitbucket together"

  success_criteria:
    functional_requirements:
      - [ ] User can initiate Atlassian OAuth flow from multiple UI entry points
      - [ ] OAuth flow completes successfully with PKCE security
      - [ ] Tokens are stored securely and refreshed automatically
      - [ ] Code Assistant Agent can use Atlassian tools after authentication
      - [ ] All three services (Jira, Confluence, Bitbucket) work through unified API
      - [ ] Error handling provides clear user feedback and recovery options
    security_requirements:
      - [ ] PKCE implementation follows OAuth 2.1 security best practices
      - [ ] All tokens stored in OS-level secure storage (Keytar)
      - [ ] State parameter prevents CSRF attacks
      - [ ] Redirect URI validation prevents authorization code interception
    user_experience_requirements:
      - [ ] OAuth flow completes in under 60 seconds for typical user
      - [ ] Clear progress indicators during authentication process
      - [ ] Intuitive error messages with actionable recovery steps
      - [ ] Seamless integration with existing Alpine Intellect workflow

  deployment_checklist:
    environment_setup:
      - [ ] ATLASSIAN_CLIENT_ID environment variable configured
      - [ ] Atlassian OAuth app registered with correct redirect URI
      - [ ] Client secret stored securely in Keytar
      - [ ] MCP configuration updated with Atlassian server
    code_deployment:
      - [ ] All implementation files created and tested
      - [ ] OAuth service integrated with existing auth infrastructure
      - [ ] HTTP/SSE transport configured and tested
      - [ ] Service client methods implemented and validated
      - [ ] Agent integration completed and tested
    verification:
      - [ ] End-to-end OAuth flow works with real Atlassian account
      - [ ] All Atlassian tools execute successfully
      - [ ] Error handling works for common failure scenarios
      - [ ] Performance meets acceptable thresholds
      - [ ] Security audit completed and passed
