feature:
  name: Figma OAuth Authentication Flow - HTTP/SSE Implementation Guide
  description: |
    Detailed stage-by-stage implementation guide for OAuth 2.0 with PKCE authentication flow
    for Figma integration in Alpine Intellect MCP system using HTTP/SSE transport. This
    implementation follows the established OAuth + HTTP/SSE pattern created for GitHub,
    demonstrating the reusable template for all remote service integrations.

  owner: developer-platform
  status: implementation-ready
  target_release: v1.3.0

  design_principles:
    follow_github_pattern: "Use same OAuth + HTTP/SSE pattern established for GitHub"
    http_sse_transport: "HTTP/SSE transport as primary communication mechanism"
    oauth_consistency: "OAuth 2.0 + PKCE authentication matching GitHub implementation"
    remote_service: "Figma as remote HTTP/SSE service (consistent with GitHub)"
    template_reuse: "Demonstrate reusable OAuth + HTTP/SSE template for future services"
    preserve_existing: "Do not modify or break existing Alpine auth or protocol handlers"
    extend_not_replace: "Build upon existing handleDeepLink function without altering current logic"
    independent_scalability: "Enable/disable Figma OAuth independently of other services"

  oauth_flow_stages:
    stage_1_authentication_trigger:
      description: "User initiates Figma connection from Alpine Intellect IDE"
      user_experience:
        trigger_points:
          - mcp_dashboard_connect_button: "User clicks 'Connect to Figma' in MCP Dashboard"
          - agent_workflow_prompt: "Design Assistant Agent requests Figma access during workflow"
          - service_configuration: "User configures Figma service in settings"
          - first_tool_usage: "User attempts to use Figma tool without authentication"
        ui_components:
          - src/ui/pages/MCPDashboard.tsx
          - src/ui/components/FigmaServiceCard.tsx
          - src/ui/components/ServiceAuthDialog.tsx
      implementation:
        trigger_handler: "src/mcp/auth/oauth-flow-manager.ts"
        auth_service: "src/mcp/auth/figma-oauth-service.ts"
        ui_controller: "src/ui/controllers/auth-controller.ts"
      flow_initiation:
        user_action: "Click 'Connect to Figma' button"
        system_response: "Display OAuth consent dialog with Figma permissions"
        next_stage: "stage_2_oauth_flow_implementation"

    stage_2_oauth_flow_implementation:
      description: "Complete PKCE OAuth 2.0 flow with Figma"
      substages:
        substage_2a_authorization_url_generation:
          description: "Generate secure authorization URL with PKCE parameters"
          implementation_file: "src/mcp/auth/figma-oauth-service.ts"
          security_measures:
            - pkce_code_verifier_generation: "Cryptographically secure random string (128 chars)"
            - pkce_code_challenge: "SHA256 hash of code verifier, base64url encoded"
            - state_parameter: "Cryptographically secure random string (32 chars)"
            - nonce_parameter: "Additional security for token validation"
          parameters:
            client_id: "${ENV:FIGMA_CLIENT_ID}"
            redirect_uri: "thealpinecode.alpineintellect://auth-callback/figma"
            scope: "file:read file:write team:read"
            response_type: "code"
            state: "${GENERATED_STATE}"
            code_challenge: "${GENERATED_CODE_CHALLENGE}"
            code_challenge_method: "S256"
          storage_requirements:
            - store_code_verifier: "keytar:figma-pkce-verifier"
            - store_state: "keytar:figma-oauth-state"
            - store_nonce: "keytar:figma-oauth-nonce"
          
        substage_2b_browser_redirect:
          description: "Open system browser with authorization URL"
          implementation_file: "src/mcp/auth/browser-launcher.ts"
          browser_handling:
            - open_system_browser: "Use electron.shell.openExternal()"
            - authorization_url: "https://www.figma.com/oauth?[parameters]"
            - user_consent: "User sees Figma OAuth consent screen"
            - permission_scopes: "Display requested permissions clearly"
          user_experience:
            - browser_opens: "System default browser opens to Figma OAuth page"
            - figma_login: "User logs into Figma (if not already logged in)"
            - permission_review: "User reviews requested permissions"
            - consent_action: "User clicks 'Allow' or 'Deny'"
            
        substage_2c_authorization_code_exchange:
          description: "Exchange authorization code for access tokens via HTTP (following GitHub pattern)"
          implementation_file: "src/mcp/auth/token-exchange-service.ts"
          callback_handling:
            redirect_capture: "thealpinecode.alpineintellect://auth-callback/figma?code=...&state=..."
            state_validation: "Verify state parameter matches stored value"
            code_extraction: "Extract authorization code from callback URL"
          token_exchange_request:
            endpoint: "https://www.figma.com/api/oauth/token"
            method: "POST"
            headers:
              content_type: "application/x-www-form-urlencoded"
            body_parameters:
              grant_type: "authorization_code"
              client_id: "${ENV:FIGMA_CLIENT_ID}"
              client_secret: "${KEYTAR:alpine-app:figma-client-secret}"
              code: "${AUTHORIZATION_CODE}"
              redirect_uri: "thealpinecode.alpineintellect://auth-callback/figma"
              code_verifier: "${STORED_CODE_VERIFIER}"
          token_response:
            access_token: "Bearer token for Figma API access"
            refresh_token: "Token for refreshing access token"
            expires_in: "Token expiration time in seconds"
            token_type: "Bearer"
            scope: "Granted permissions"

        substage_2d_oauth_server_discovery:
          description: "Discover Figma OAuth server metadata and MCP capabilities (following GitHub pattern)"
          implementation_file: "src/mcp/discovery/oauth-discovery-client.ts"
          discovery_process:
            discovery_endpoint: "https://api.figma.com/mcp/.well-known/oauth-authorization-server"
            metadata_validation: "Validate discovered OAuth endpoints and PKCE support"
            capability_verification: "Verify MCP tools, resources, and notification capabilities"
            fallback_strategy: "Use static configuration if discovery fails"
            pattern_reuse: "Reuse discovery client from GitHub implementation"

        substage_2e_http_sse_connection_setup:
          description: "Establish HTTP/SSE connection to Figma MCP service (following GitHub pattern)"
          implementation_file: "src/mcp/clients/figma-http-sse-client.ts"
          connection_setup:
            http_endpoint: "https://api.figma.com/mcp/v1"
            sse_endpoint: "https://api.figma.com/mcp/v1/events"
            authentication: "Bearer ${FIGMA_ACCESS_TOKEN}"
            connection_validation: "Verify MCP capabilities and tool availability"
            discovery_integration: "Use discovered endpoints if available"

    stage_3_ide_redirect_handling:
      description: "Capture OAuth callback in Alpine Intellect IDE using existing deep link handler"
      implementation:
        existing_handler_extension: "src/electron/main.ts:handleDeepLink"
        callback_processor: "src/mcp/auth/figma-oauth-callback-processor.ts"
        url_parser: "src/mcp/auth/callback-url-parser.ts"
        service_registry: "src/mcp/auth/oauth-service-registry.ts"
      electron_setup:
        protocol_registration:
          - protocol_scheme: "thealpinecode.alpineintellect"
          - handler_registration: "REUSE existing app.setAsDefaultProtocolClient registration"
          - existing_integration: "Extend existing handleDeepLink function WITHOUT modification"
          - preservation_note: "Maintain all existing Alpine auth and GitHub OAuth functionality"
        callback_interception:
          - url_pattern: "thealpinecode.alpineintellect://auth-callback/figma*"
          - parameter_extraction: "Extract code, state, and error parameters"
          - validation_checks: "Verify state, check for error parameters"
          - service_detection: "Detect Figma OAuth vs Alpine auth vs GitHub OAuth"
          - routing_logic: "Route to appropriate service-specific callback processor"
      user_experience:
        browser_redirect: "Browser redirects to thealpinecode.alpineintellect://auth-callback/figma"
        ide_activation: "Alpine Intellect IDE comes to foreground"
        processing_indicator: "Show 'Completing Figma authentication...' message"
        success_notification: "Display 'Successfully connected to Figma' message"
        error_handling: "Show specific error message if authentication fails"

    stage_4_token_storage_management:
      description: "Secure token storage and automatic refresh mechanisms"
      implementation:
        token_storage: "src/mcp/auth/secure-token-storage.ts"
        refresh_manager: "src/mcp/auth/token-refresh-manager.ts"
        expiry_monitor: "src/mcp/auth/token-expiry-monitor.ts"
      storage_strategy:
        keytar_integration:
          service_name: "alpine-app"  # Same as existing Alpine auth
          account_mappings:
            access_token: "figma-access-token"
            refresh_token: "figma-refresh-token"
            expires_at: "figma-token-expires-at"
            token_metadata: "figma-token-metadata"
        encryption_at_rest: "Keytar provides OS-level encryption"
        access_control: "Only Alpine Intellect process can access tokens"
      automatic_refresh:
        refresh_trigger: "5 minutes before token expiration"
        refresh_endpoint: "https://www.figma.com/api/oauth/token"
        refresh_parameters:
          grant_type: "refresh_token"
          client_id: "${ENV:FIGMA_CLIENT_ID}"
          client_secret: "${KEYTAR:figma-client-secret}"
          refresh_token: "${STORED_REFRESH_TOKEN}"
        failure_handling:
          - retry_attempts: 3
          - exponential_backoff: "1s, 2s, 4s"
          - fallback_action: "Prompt user for re-authentication"
      token_validation:
        access_token_validation: "Test token with Figma API /v1/me endpoint"
        scope_verification: "Ensure token has required permissions"
        expiry_checking: "Monitor token expiration timestamps"

    stage_5_service_integration:
      description: "Enable Design Assistant Agent to use authenticated Figma tools"
      implementation:
        agent_integration: "src/agents/design-assistant-agent.ts"
        figma_client: "src/mcp/clients/figma-remote-client.ts"
        tool_executor: "src/mcp/execution/figma-tool-executor.ts"
      agent_workflow:
        tool_discovery:
          - available_tools: ["get_file_content", "export_assets", "create_component", "manage_styles"]
          - capability_check: "Verify authentication status before tool execution"
          - permission_validation: "Check token scopes match tool requirements"
        tool_execution:
          - authentication_header: "Authorization: Bearer ${ACCESS_TOKEN}"
          - api_endpoint_mapping: "Map MCP tools to Figma API endpoints"
          - response_processing: "Convert Figma API responses to MCP tool results"
        workflow_examples:
          design_system_audit:
            - get_file_content: "Retrieve design file structure"
            - export_assets: "Export components for analysis"
            - analysis_report: "Generate design system compliance report"
          component_standardization:
            - get_file_content: "Analyze existing components"
            - create_component: "Create standardized component"
            - manage_styles: "Apply consistent styling"
      user_experience:
        seamless_integration: "Tools work transparently once authenticated"
        real_time_feedback: "Show tool execution progress and results"
        error_transparency: "Clear error messages for authentication issues"

    stage_6_error_handling:
      description: "Comprehensive error handling for authentication failures and token issues"
      error_categories:
        authentication_errors:
          user_denied_access:
            error_code: "access_denied"
            user_message: "Figma access was denied. Please try connecting again."
            recovery_action: "Restart OAuth flow"
            implementation: "src/mcp/error/oauth-error-handler.ts"
          invalid_client:
            error_code: "invalid_client"
            user_message: "Configuration error. Please contact support."
            recovery_action: "Check client credentials"
            implementation: "src/mcp/error/config-error-handler.ts"
          server_error:
            error_code: "server_error"
            user_message: "Figma service temporarily unavailable. Please try again later."
            recovery_action: "Retry with exponential backoff"
            implementation: "src/mcp/error/service-error-handler.ts"
        token_errors:
          token_expired:
            detection: "API returns 401 Unauthorized"
            automatic_action: "Attempt token refresh"
            user_notification: "Refreshing Figma connection..."
            fallback: "Prompt for re-authentication if refresh fails"
          token_revoked:
            detection: "Refresh token returns invalid_grant"
            user_message: "Figma access has been revoked. Please reconnect."
            recovery_action: "Clear stored tokens and restart OAuth flow"
          insufficient_scope:
            detection: "API returns 403 Forbidden with scope error"
            user_message: "Additional Figma permissions required."
            recovery_action: "Restart OAuth flow with updated scopes"
        network_errors:
          connection_timeout:
            timeout_duration: "30 seconds"
            retry_strategy: "3 attempts with exponential backoff"
            user_message: "Connection to Figma timed out. Retrying..."
          network_unavailable:
            detection: "DNS resolution failure or network unreachable"
            user_message: "Network connection required for Figma integration."
            recovery_action: "Monitor network status and retry when available"

  implementation_files:
    authentication_core:
      - src/mcp/auth/figma-oauth-service.ts  # Figma OAuth 2.0 + PKCE implementation (follows GitHub pattern)
      - src/mcp/auth/oauth-flow-manager.ts  # REUSE existing shared OAuth flow manager
      - src/mcp/auth/token-exchange-service.ts  # EXTEND existing token exchange service
      - src/mcp/auth/secure-token-storage.ts  # REUSE existing secure token storage
      - src/mcp/auth/token-refresh-manager.ts  # EXTEND existing refresh manager
      - src/mcp/auth/oauth-service-registry.ts  # REUSE service registry from GitHub implementation
    oauth_discovery:
      - src/mcp/discovery/oauth-discovery-client.ts  # REUSE OAuth discovery from GitHub
      - src/mcp/discovery/discovery-cache.ts  # REUSE discovery caching from GitHub
      - src/mcp/config/mcp-configuration-manager.ts  # REUSE configuration manager from GitHub
    http_sse_transport:
      - src/mcp/transports/http-sse-transport.ts  # REUSE HTTP/SSE transport from GitHub
      - src/mcp/clients/figma-http-sse-client.ts  # Figma HTTP/SSE MCP client (follows GitHub pattern)
      - src/mcp/clients/remote-service-client.ts  # REUSE base class from GitHub implementation
    protocol_handling:
      - src/electron/main.ts  # EXTEND existing handleDeepLink function (NO MODIFICATION)
      - src/mcp/auth/figma-oauth-callback-processor.ts  # Figma-specific callback processor
      - src/mcp/auth/callback-url-parser.ts  # REUSE existing URL parser
      - src/mcp/auth/oauth-callback-router.ts  # REUSE callback router from GitHub implementation
    ui_components:
      - src/ui/pages/MCPDashboard.tsx
      - src/ui/components/FigmaServiceCard.tsx
      - src/ui/components/ServiceAuthDialog.tsx
      - src/ui/components/AuthProgressIndicator.tsx
    error_handling:
      - src/mcp/error/oauth-error-handler.ts
      - src/mcp/error/config-error-handler.ts
      - src/mcp/error/service-error-handler.ts
    integration:
      - src/agents/design-assistant-agent.ts
      - src/mcp/clients/figma-remote-client.ts
      - src/mcp/execution/figma-tool-executor.ts

  security_considerations:
    pkce_implementation:
      - code_verifier_entropy: "Minimum 128 characters, cryptographically secure"
      - code_challenge_method: "S256 (SHA256) only, no plain text"
      - state_parameter_validation: "Prevent CSRF attacks"
    token_security:
      - storage_encryption: "OS-level keychain/credential manager"
      - memory_protection: "Clear sensitive data from memory after use"
      - transmission_security: "HTTPS only, certificate pinning"
    redirect_uri_validation:
      - exact_match_required: "thealpinecode.alpineintellect://auth-callback/figma only"
      - protocol_scheme_validation: "Only thealpinecode.alpineintellect:// scheme allowed"
      - parameter_sanitization: "Validate all callback parameters"

  testing_strategy:
    unit_tests:
      - oauth_flow_manager_test: "Test OAuth flow initiation and state management"
      - token_storage_test: "Test secure token storage and retrieval"
      - callback_processor_test: "Test OAuth callback processing and validation"
    integration_tests:
      - end_to_end_oauth_flow: "Complete OAuth flow from trigger to token storage"
      - token_refresh_flow: "Automatic token refresh functionality"
      - error_recovery_scenarios: "Various error conditions and recovery"
    security_tests:
      - pkce_parameter_validation: "Verify PKCE implementation security"
      - state_parameter_protection: "Test CSRF protection"
      - token_storage_security: "Verify secure token storage"

  success_criteria:
    functional_requirements:
      - [ ] User can initiate Figma OAuth flow from multiple UI entry points
      - [ ] OAuth flow completes successfully with PKCE security
      - [ ] Tokens are stored securely and refreshed automatically
      - [ ] Design Assistant Agent can use Figma tools after authentication
      - [ ] Error handling provides clear user feedback and recovery options
    security_requirements:
      - [ ] PKCE implementation follows OAuth 2.1 security best practices
      - [ ] All tokens stored in OS-level secure storage (Keytar)
      - [ ] State parameter prevents CSRF attacks
      - [ ] Redirect URI validation prevents authorization code interception
    user_experience_requirements:
      - [ ] OAuth flow completes in under 60 seconds for typical user
      - [ ] Clear progress indicators throughout authentication process
      - [ ] Graceful error handling with actionable user messages
      - [ ] Seamless tool usage after successful authentication
    integration_requirements:
      - [ ] PRESERVES all existing Alpine auth and GitHub OAuth functionality
      - [ ] EXTENDS existing handleDeepLink function without modification
      - [ ] REUSES existing thealpinecode.alpineintellect:// protocol handler
      - [ ] Uses same keytar service name (alpine-app) as existing auth
      - [ ] Follows same IPC event patterns as existing authentication
      - [ ] Implements service detection to route callbacks appropriately
      - [ ] Enables independent enable/disable of Figma OAuth
      - [ ] Provides modular architecture for future OAuth providers
      - [ ] Maintains backward compatibility with all existing systems

  scalable_oauth_architecture:
    service_registry_pattern:
      description: "Centralized registry for OAuth service management"
      implementation_file: "src/mcp/auth/oauth-service-registry.ts"
      capabilities:
        - service_registration: "Register new OAuth providers dynamically"
        - callback_routing: "Route callbacks to appropriate service processors"
        - service_discovery: "Discover available OAuth services"
        - independent_management: "Enable/disable services independently"

    callback_routing_strategy:
      description: "Intelligent routing of OAuth callbacks to service-specific processors"
      implementation_file: "src/mcp/auth/oauth-callback-router.ts"
      routing_logic:
        - url_pattern_matching: "Match callback URLs to registered services"
        - service_identification: "Identify service from URL path or parameters"
        - processor_delegation: "Delegate to service-specific callback processor"
        - error_handling: "Handle unrecognized or malformed callbacks"

    modular_service_design:
      description: "Template for implementing new OAuth providers"
      service_interface: "IOAuthService"
      required_methods:
        - initializeOAuth: "Start OAuth flow for the service"
        - handleCallback: "Process OAuth callback for the service"
        - refreshTokens: "Refresh expired tokens"
        - revokeAccess: "Revoke OAuth access"
        - getAuthStatus: "Check current authentication status"

    future_provider_template:
      description: "Pattern for adding new OAuth providers (Slack, Discord, etc.)"
      implementation_steps:
        - create_service_class: "Implement IOAuthService interface"
        - register_service: "Register with OAuth service registry"
        - add_ui_components: "Create service-specific UI components"
        - configure_callback_route: "Add callback URL pattern to router"
        - implement_token_management: "Add service-specific token handling"
