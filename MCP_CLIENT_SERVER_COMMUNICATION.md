# MCP Client-Server Communication Architecture

## Overview
This document outlines the Model Context Protocol (MCP) client-server communication architecture, implementation strategy, and scalability approach for Alpine Intellect's integration with external services. The architecture focuses on HTTP/SSE transport with OAuth authentication as the primary pattern for all remote service integrations (GitHub, Figma, and future services).

## MCP Protocol Fundamentals

### 1. **Protocol Structure**
MCP is a JSON-RPC based protocol that enables standardized communication between AI applications and external services through tools, resources, and prompts.

```typescript
interface MCPMessage {
  jsonrpc: "2.0";
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

interface MCPRequest extends MCPMessage {
  method: string;
  params?: any;
}

interface MCPResponse extends MCPMessage {
  result?: any;
  error?: MCPError;
}
```

### 2. **Communication Patterns**

#### Request-Response Pattern
```typescript
// Client sends request
{
  "jsonrpc": "2.0",
  "id": "req-123",
  "method": "tools/call",
  "params": {
    "name": "create_repository",
    "arguments": {
      "name": "my-repo",
      "description": "A new repository"
    }
  }
}

// Server responds
{
  "jsonrpc": "2.0",
  "id": "req-123",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Repository 'my-repo' created successfully"
      }
    ]
  }
}
```

#### Notification Pattern
```typescript
// Server sends notification (no response expected)
{
  "jsonrpc": "2.0",
  "method": "notifications/tools/list_changed",
  "params": {
    "tools": ["new_tool_1", "new_tool_2"]
  }
}
```

## Transport Mechanisms

### 1. **HTTP/SSE Transport (Primary - All Current Integrations)**
Used for all remote MCP servers including GitHub and Figma integrations with OAuth authentication.

```typescript
class HTTPSSETransport {
  private httpClient: HTTPClient;
  private eventSource: EventSource;
  private oauthManager: OAuthManager;
  private messageQueue = new Map<string, PendingRequest>();

  async initialize(): Promise<void> {
    // Ensure OAuth authentication
    await this.oauthManager.ensureValidToken();

    // Initialize HTTP client with OAuth headers
    this.httpClient = new HTTPClient({
      baseURL: this.config.httpEndpoint,
      headers: {
        'Authorization': `Bearer ${await this.oauthManager.getAccessToken()}`
      }
    });

    // Establish SSE connection for real-time events
    this.eventSource = new EventSource(this.config.sseEndpoint, {
      headers: {
        'Authorization': `Bearer ${await this.oauthManager.getAccessToken()}`
      }
    });

    this.eventSource.onmessage = this.handleSSEMessage.bind(this);
    this.eventSource.onerror = this.handleSSEError.bind(this);
  }

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    // Use HTTP for requests, SSE for notifications/events
    const response = await this.httpClient.post('/mcp/v1/tools', request);
    return response.data;
  }

  private handleSSEMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data) as MCPMessage;
      this.processNotification(message);
    } catch (error) {
      console.error('Failed to parse SSE message:', event.data, error);
    }
  }
}
```

### 2. **STDIO Transport (Future Extensibility)**
For future local MCP server implementations.

```typescript
class SSETransport {
  private eventSource: EventSource;
  private httpClient: HTTPClient;
  
  async initialize(): Promise<void> {
    // Establish SSE connection for real-time events
    this.eventSource = new EventSource(this.config.sseEndpoint, {
      headers: {
        'Authorization': `Bearer ${await this.authService.getAccessToken()}`
      }
    });
    
    this.eventSource.onmessage = this.handleSSEMessage.bind(this);
    this.eventSource.onerror = this.handleSSEError.bind(this);
  }
  
  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    // Use HTTP for requests, SSE for notifications/events
    const response = await this.httpClient.post('/mcp/v1/tools', request, {
      headers: {
        'Authorization': `Bearer ${await this.authService.getAccessToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  }
  
  private handleSSEMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data) as MCPMessage;
      this.processNotification(message);
    } catch (error) {
      console.error('Failed to parse SSE message:', event.data, error);
    }
  }
}
```

### 3. **WebSocket Transport (Future)**
For bidirectional real-time communication.

```typescript
class WebSocketTransport {
  private ws: WebSocket;
  private messageQueue = new Map<string, PendingRequest>();
  
  async initialize(): Promise<void> {
    this.ws = new WebSocket(this.config.wsEndpoint);
    this.ws.onmessage = this.handleMessage.bind(this);
    this.ws.onclose = this.handleClose.bind(this);
    this.ws.onerror = this.handleError.bind(this);
    
    await this.performHandshake();
  }
  
  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(request.id);
        reject(new Error(`Request timeout: ${request.id}`));
      }, 30000);
      
      this.messageQueue.set(request.id, { resolve, reject, timeout });
      this.ws.send(JSON.stringify(request));
    });
  }
}
```

## Implementation Strategy

### 1. **Multi-Transport Architecture**

```typescript
interface Transport {
  initialize(): Promise<void>;
  sendRequest(request: MCPRequest): Promise<MCPResponse>;
  sendNotification(notification: MCPNotification): Promise<void>;
  close(): Promise<void>;
}

class MCPClient {
  private transport: Transport;
  private capabilities: ClientCapabilities;
  private tools = new Map<string, ToolDefinition>();
  
  constructor(config: MCPClientConfig) {
    // Select transport based on configuration
    switch (config.transport.type) {
      case 'stdio':
        this.transport = new StdioTransport(config.transport);
        break;
      case 'sse':
        this.transport = new SSETransport(config.transport);
        break;
      case 'websocket':
        this.transport = new WebSocketTransport(config.transport);
        break;
      default:
        throw new Error(`Unsupported transport: ${config.transport.type}`);
    }
  }
  
  async initialize(): Promise<void> {
    await this.transport.initialize();
    await this.performHandshake();
    await this.discoverCapabilities();
  }
}
```

### 2. **Connection Management**

```typescript
class ConnectionManager {
  private connections = new Map<string, MCPClient>();
  private healthChecker: HealthChecker;
  private reconnectionManager: ReconnectionManager;
  
  async createConnection(config: MCPClientConfig): Promise<MCPClient> {
    const client = new MCPClient(config);
    await client.initialize();
    
    this.connections.set(config.id, client);
    this.healthChecker.monitor(client);
    
    return client;
  }
  
  async handleConnectionLoss(clientId: string): Promise<void> {
    const client = this.connections.get(clientId);
    if (client) {
      await this.reconnectionManager.attemptReconnection(client);
    }
  }
}
```

### 3. **Message Routing**

```typescript
class MessageRouter {
  private clients = new Map<string, MCPClient>();
  private agents = new Map<string, AIAgent>();
  
  async routeToolRequest(request: ToolRequest): Promise<ToolResponse> {
    // Determine which client can handle the tool
    const client = this.findClientForTool(request.toolName);
    if (!client) {
      throw new Error(`No client available for tool: ${request.toolName}`);
    }
    
    // Execute tool via appropriate client
    return await client.executeTool(request.toolName, request.parameters);
  }
  
  private findClientForTool(toolName: string): MCPClient | undefined {
    for (const [clientId, client] of this.clients) {
      if (client.hasCapability('tools') && client.supportsTool(toolName)) {
        return client;
      }
    }
    return undefined;
  }
}
```

## Reliability & Robustness Features

### 1. **Error Handling & Recovery**

```typescript
class ErrorHandler {
  async handleError(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    switch (error.code) {
      case -32603: // Internal error
        return await this.handleInternalError(error, context);
      case -32602: // Invalid params
        return await this.handleInvalidParams(error, context);
      case -32601: // Method not found
        return await this.handleMethodNotFound(error, context);
      case -32600: // Invalid request
        return await this.handleInvalidRequest(error, context);
      default:
        return await this.handleUnknownError(error, context);
    }
  }
  
  private async handleConnectionLoss(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const client = context.client as MCPClient;
    
    // Attempt reconnection with exponential backoff
    const maxAttempts = 5;
    let attempt = 1;
    
    while (attempt <= maxAttempts) {
      try {
        await this.delay(Math.pow(2, attempt) * 1000);
        await client.reconnect();
        
        return {
          recovered: true,
          action: 'reconnected',
          retryable: true,
          metadata: { attempts: attempt }
        };
      } catch (reconnectError) {
        attempt++;
      }
    }
    
    return {
      recovered: false,
      action: 'reconnection_failed',
      retryable: false,
      fallbackAction: 'use_cached_data'
    };
  }
}
```

### 2. **Request Timeout & Retry Logic**

```typescript
class RequestManager {
  private defaultTimeout = 30000; // 30 seconds
  private maxRetries = 3;
  
  async sendRequestWithRetry(
    client: MCPClient,
    request: MCPRequest,
    options: RequestOptions = {}
  ): Promise<MCPResponse> {
    const timeout = options.timeout || this.defaultTimeout;
    const maxRetries = options.maxRetries || this.maxRetries;
    
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await Promise.race([
          client.sendRequest(request),
          this.createTimeoutPromise(timeout, request.id)
        ]);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries && this.isRetryableError(error)) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
          continue;
        }
        
        throw error;
      }
    }
    
    throw lastError!;
  }
  
  private isRetryableError(error: any): boolean {
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ENOTFOUND' ||
      (error.status >= 500 && error.status < 600)
    );
  }
}
```

### 3. **Health Monitoring**

```typescript
class HealthChecker {
  private healthChecks = new Map<string, HealthCheck>();
  private monitoringInterval = 30000; // 30 seconds

  monitor(client: MCPClient): void {
    const healthCheck: HealthCheck = {
      clientId: client.id,
      lastPing: Date.now(),
      status: 'healthy',
      consecutiveFailures: 0
    };

    this.healthChecks.set(client.id, healthCheck);

    // Start periodic health checks
    setInterval(async () => {
      await this.performHealthCheck(client);
    }, this.monitoringInterval);
  }

  private async performHealthCheck(client: MCPClient): Promise<void> {
    const healthCheck = this.healthChecks.get(client.id)!;

    try {
      const pingRequest: MCPRequest = {
        jsonrpc: "2.0",
        id: `ping-${Date.now()}`,
        method: "ping",
        params: {}
      };

      const startTime = Date.now();
      await client.sendRequest(pingRequest);
      const responseTime = Date.now() - startTime;

      healthCheck.lastPing = Date.now();
      healthCheck.status = 'healthy';
      healthCheck.consecutiveFailures = 0;
      healthCheck.responseTime = responseTime;

    } catch (error) {
      healthCheck.consecutiveFailures++;

      if (healthCheck.consecutiveFailures >= 3) {
        healthCheck.status = 'unhealthy';
        await this.handleUnhealthyClient(client);
      }
    }
  }
}
```

## Scalability Architecture

### 1. **Concurrent Request Management**

```typescript
class ConcurrencyManager {
  private maxConcurrentRequests = 10;
  private activeRequests = new Map<string, Set<string>>();
  private requestQueue = new Map<string, RequestQueue>();

  async executeRequest(
    clientId: string,
    request: MCPRequest
  ): Promise<MCPResponse> {
    const activeCount = this.getActiveRequestCount(clientId);

    if (activeCount >= this.maxConcurrentRequests) {
      // Queue the request
      return await this.queueRequest(clientId, request);
    }

    // Execute immediately
    return await this.executeImmediately(clientId, request);
  }

  private async executeImmediately(
    clientId: string,
    request: MCPRequest
  ): Promise<MCPResponse> {
    this.trackActiveRequest(clientId, request.id);

    try {
      const client = this.getClient(clientId);
      const response = await client.sendRequest(request);
      return response;
    } finally {
      this.untrackActiveRequest(clientId, request.id);
      await this.processQueue(clientId);
    }
  }
}
```

### 2. **Resource Pool Management**

```typescript
class ResourcePoolManager {
  private clientPools = new Map<string, ClientPool>();

  async getClient(serviceType: string): Promise<MCPClient> {
    let pool = this.clientPools.get(serviceType);

    if (!pool) {
      pool = new ClientPool({
        serviceType,
        minSize: 2,
        maxSize: 10,
        createClient: () => this.createClientForService(serviceType)
      });
      this.clientPools.set(serviceType, pool);
    }

    return await pool.acquire();
  }

  async releaseClient(serviceType: string, client: MCPClient): Promise<void> {
    const pool = this.clientPools.get(serviceType);
    if (pool) {
      await pool.release(client);
    }
  }
}

class ClientPool {
  private available: MCPClient[] = [];
  private inUse = new Set<MCPClient>();
  private config: PoolConfig;

  async acquire(): Promise<MCPClient> {
    if (this.available.length > 0) {
      const client = this.available.pop()!;
      this.inUse.add(client);
      return client;
    }

    if (this.inUse.size < this.config.maxSize) {
      const client = await this.config.createClient();
      this.inUse.add(client);
      return client;
    }

    // Wait for a client to become available
    return await this.waitForAvailableClient();
  }
}
```
