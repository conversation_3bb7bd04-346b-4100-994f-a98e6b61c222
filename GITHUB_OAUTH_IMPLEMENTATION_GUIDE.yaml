feature:
  name: GitHub OAuth Authentication Flow - HTTP/SSE Implementation Guide
  description: |
    Complete stage-by-stage implementation guide for OAuth 2.0 with PKCE authentication flow
    for GitHub integration in Alpine Intellect MCP system using HTTP/SSE transport. This
    implementation establishes the standard OAuth + HTTP/SSE pattern for all remote service
    integrations, providing a template for future services (Figma, Slack, Discord, etc.).

  owner: developer-platform
  status: implementation-ready
  target_release: v1.3.0
  phase: 1

  design_principles:
    http_sse_first: "Use HTTP/SSE transport as primary communication mechanism"
    oauth_standard: "OAuth 2.0 + PKCE authentication for all remote services"
    remote_services: "All integrations as remote HTTP/SSE services (no local processes)"
    template_pattern: "Establish reusable OAuth + HTTP/SSE template for future services"
    preserve_existing: "Do not modify or break existing Alpine auth or protocol handlers"
    extend_not_replace: "Build upon existing handleDeepLink function without altering current logic"
    independent_scalability: "Enable/disable GitHub OAuth independently of other services"

  oauth_flow_stages:
    stage_1_authentication_trigger:
      description: "User initiates GitHub connection from Alpine Intellect IDE"
      user_experience:
        trigger_points:
          - mcp_dashboard_connect_button: "User clicks 'Connect to GitHub' in MCP Dashboard"
          - agent_workflow_prompt: "Code Assistant Agent requests GitHub access during workflow"
          - service_configuration: "User configures GitHub service in settings"
          - first_tool_usage: "User attempts to use GitHub tool without authentication"
        ui_components:
          - src/ui/pages/MCPDashboard.tsx
          - src/ui/components/GitHubServiceCard.tsx
          - src/ui/components/ServiceAuthDialog.tsx
      implementation:
        trigger_handler: "src/mcp/auth/oauth-flow-manager.ts"
        auth_service: "src/mcp/auth/github-oauth-service.ts"
        ui_controller: "src/ui/controllers/auth-controller.ts"
      flow_initiation:
        user_action: "Click 'Connect to GitHub' button"
        system_response: "Display OAuth consent dialog with GitHub permissions"
        next_stage: "stage_2_oauth_flow_implementation"

    stage_2_oauth_flow_implementation:
      description: "Complete PKCE OAuth 2.0 flow with GitHub"
      substages:
        substage_2a_authorization_url_generation:
          description: "Generate secure authorization URL with PKCE parameters"
          implementation_file: "src/mcp/auth/github-oauth-service.ts"
          security_measures:
            - pkce_code_verifier_generation: "Cryptographically secure random string (128 chars)"
            - pkce_code_challenge: "SHA256 hash of code verifier, base64url encoded"
            - state_parameter: "Cryptographically secure random string (32 chars)"
            - nonce_parameter: "Additional security for token validation"
          parameters:
            client_id: "${ENV:GITHUB_CLIENT_ID}"
            redirect_uri: "thealpinecode.alpineintellect://auth-callback"
            scope: "repo user read:org"
            response_type: "code"
            state: "${GENERATED_STATE}"
            code_challenge: "${GENERATED_CODE_CHALLENGE}"
            code_challenge_method: "S256"
          storage_requirements:
            - store_code_verifier: "keytar:alpine-app:github-pkce-verifier"
            - store_state: "keytar:alpine-app:github-oauth-state"
            - store_nonce: "keytar:alpine-app:github-oauth-nonce"
          
        substage_2b_browser_redirect:
          description: "Open system browser with authorization URL"
          implementation_file: "src/mcp/auth/browser-launcher.ts"
          browser_handling:
            - open_system_browser: "Use electron.shell.openExternal()"
            - authorization_url: "https://github.com/login/oauth/authorize?[parameters]"
            - user_consent: "User sees GitHub OAuth consent screen"
            - permission_scopes: "Display requested permissions clearly"
          user_experience:
            - browser_opens: "System default browser opens to GitHub OAuth page"
            - github_login: "User logs into GitHub (if not already logged in)"
            - permission_review: "User reviews requested permissions"
            - consent_action: "User clicks 'Authorize' or 'Cancel'"
            
        substage_2c_authorization_code_exchange:
          description: "Exchange authorization code for access tokens via HTTP"
          implementation_file: "src/mcp/auth/token-exchange-service.ts"
          callback_handling:
            redirect_capture: "thealpinecode.alpineintellect://auth-callback/github?code=...&state=..."
            state_validation: "Verify state parameter matches stored value"
            code_extraction: "Extract authorization code from callback URL"
          token_exchange_request:
            endpoint: "https://github.com/login/oauth/access_token"
            method: "POST"
            headers:
              content_type: "application/x-www-form-urlencoded"
              accept: "application/json"
            body_parameters:
              grant_type: "authorization_code"
              client_id: "${ENV:GITHUB_CLIENT_ID}"
              client_secret: "${KEYTAR:alpine-app:github-client-secret}"
              code: "${AUTHORIZATION_CODE}"
              redirect_uri: "thealpinecode.alpineintellect://auth-callback/github"
              code_verifier: "${STORED_CODE_VERIFIER}"
          token_response:
            access_token: "Bearer token for GitHub API access"
            token_type: "bearer"
            scope: "Granted permissions"

        substage_2d_oauth_server_discovery:
          description: "Discover GitHub OAuth server metadata and MCP capabilities"
          implementation_file: "src/mcp/discovery/oauth-discovery-client.ts"
          discovery_process:
            discovery_endpoint: "https://api.github.com/mcp/.well-known/oauth-authorization-server"
            metadata_validation: "Validate discovered OAuth endpoints and PKCE support"
            capability_verification: "Verify MCP tools and notification capabilities"
            fallback_strategy: "Use static configuration if discovery fails"

        substage_2e_http_sse_connection_setup:
          description: "Establish HTTP/SSE connection to GitHub MCP service"
          implementation_file: "src/mcp/clients/github-http-sse-client.ts"
          connection_setup:
            http_endpoint: "https://api.github.com/mcp/v1"
            sse_endpoint: "https://api.github.com/mcp/v1/events"
            authentication: "Bearer ${GITHUB_ACCESS_TOKEN}"
            connection_validation: "Verify MCP capabilities and tool availability"
            discovery_integration: "Use discovered endpoints if available"

    stage_3_ide_redirect_handling:
      description: "Capture OAuth callback in Alpine Intellect IDE using existing deep link handler"
      implementation:
        existing_handler_extension: "src/electron/main.ts:handleDeepLink"
        callback_processor: "src/mcp/auth/github-oauth-callback-processor.ts"
        url_parser: "src/mcp/auth/callback-url-parser.ts"
      electron_setup:
        protocol_registration:
          - protocol_scheme: "thealpinecode.alpineintellect"
          - handler_registration: "app.setAsDefaultProtocolClient('thealpinecode.alpineintellect')"
          - existing_integration: "Extend existing handleDeepLink function"
        callback_interception:
          - url_pattern: "thealpinecode.alpineintellect://auth-callback/github*"
          - parameter_extraction: "Extract code, state, and error parameters"
          - validation_checks: "Verify state, check for error parameters"
          - service_detection: "Detect GitHub OAuth vs Alpine auth vs other OAuth providers"
          - routing_logic: "Route to GitHub HTTP/SSE client initialization"
      user_experience:
        browser_redirect: "Browser redirects to thealpinecode.alpineintellect://auth-callback/github"
        ide_activation: "Alpine Intellect IDE comes to foreground"
        processing_indicator: "Show 'Connecting to GitHub via HTTP/SSE...' message"
        connection_establishment: "Establish HTTP/SSE connection to GitHub MCP service"
        success_notification: "Display 'Successfully connected to GitHub' message"
        error_handling: "Show specific error message if authentication or connection fails"

    stage_4_token_storage_management:
      description: "Secure token storage and automatic refresh mechanisms using existing keytar patterns"
      implementation:
        token_storage: "src/mcp/auth/secure-token-storage.ts"
        refresh_manager: "src/mcp/auth/github-token-refresh-manager.ts"
        expiry_monitor: "src/mcp/auth/token-expiry-monitor.ts"
      storage_strategy:
        keytar_integration:
          service_name: "alpine-app"  # Same as existing Alpine auth
          account_mappings:
            access_token: "github-access-token"
            refresh_token: "github-refresh-token"  # GitHub doesn't provide refresh tokens
            expires_at: "github-token-expires-at"  # GitHub tokens don't expire
            token_metadata: "github-token-metadata"
        encryption_at_rest: "Keytar provides OS-level encryption"
        access_control: "Only Alpine Intellect process can access tokens"
      token_management:
        github_specifics: "GitHub personal access tokens don't expire automatically"
        validation_endpoint: "https://api.github.com/user"
        scope_verification: "Verify token has required scopes"
        revocation_handling: "Handle token revocation by user"

    stage_5_service_integration:
      description: "Enable Code Assistant Agent to use authenticated GitHub tools"
      implementation:
        agent_integration: "src/agents/code-assistant-agent.ts"
        github_client: "src/mcp/clients/github-remote-client.ts"
        tool_executor: "src/mcp/execution/github-tool-executor.ts"
      agent_workflow:
        tool_discovery:
          - available_tools: ["create_repository", "create_issue", "create_pull_request", "search_code", "manage_branches"]
          - capability_check: "Verify authentication status before tool execution"
          - permission_validation: "Check token scopes match tool requirements"
        tool_execution:
          - authentication_header: "Authorization: Bearer ${ACCESS_TOKEN}"
          - api_endpoint_mapping: "Map MCP tools to GitHub API endpoints"
          - response_processing: "Convert GitHub API responses to MCP tool results"
        workflow_examples:
          repository_setup:
            - create_repository: "Create new repository with initial structure"
            - create_issue: "Create initial setup issues"
            - setup_branches: "Create development and feature branches"
          bug_tracking:
            - search_code: "Find code related to bug reports"
            - create_issue: "Create detailed bug report with context"
            - create_pull_request: "Create PR with bug fix"
      user_experience:
        seamless_integration: "Tools work transparently once authenticated"
        real_time_feedback: "Show tool execution progress and results"
        error_transparency: "Clear error messages for authentication issues"

    stage_6_error_handling:
      description: "Comprehensive error handling for authentication failures and token issues"
      error_categories:
        authentication_errors:
          user_denied_access:
            error_code: "access_denied"
            user_message: "GitHub access was denied. Please try connecting again."
            recovery_action: "Restart OAuth flow"
            implementation: "src/mcp/error/github-oauth-error-handler.ts"
          invalid_client:
            error_code: "invalid_client"
            user_message: "Configuration error. Please contact support."
            recovery_action: "Check client credentials"
            implementation: "src/mcp/error/config-error-handler.ts"
          server_error:
            error_code: "server_error"
            user_message: "GitHub service temporarily unavailable. Please try again later."
            recovery_action: "Retry with exponential backoff"
            implementation: "src/mcp/error/service-error-handler.ts"
        token_errors:
          token_revoked:
            detection: "API returns 401 Unauthorized with token error"
            user_message: "GitHub access has been revoked. Please reconnect."
            recovery_action: "Clear stored tokens and restart OAuth flow"
          insufficient_scope:
            detection: "API returns 403 Forbidden with scope error"
            user_message: "Additional GitHub permissions required."
            recovery_action: "Restart OAuth flow with updated scopes"
          rate_limited:
            detection: "API returns 403 with rate limit headers"
            user_message: "GitHub API rate limit exceeded. Please wait."
            recovery_action: "Implement exponential backoff with rate limit headers"
        network_errors:
          connection_timeout:
            timeout_duration: "30 seconds"
            retry_strategy: "3 attempts with exponential backoff"
            user_message: "Connection to GitHub timed out. Retrying..."
          network_unavailable:
            detection: "DNS resolution failure or network unreachable"
            user_message: "Network connection required for GitHub integration."
            recovery_action: "Monitor network status and retry when available"

  implementation_files:
    authentication_core:
      - src/mcp/auth/github-oauth-service.ts  # GitHub OAuth 2.0 + PKCE implementation
      - src/mcp/auth/oauth-flow-manager.ts  # REUSE existing shared OAuth flow manager
      - src/mcp/auth/token-exchange-service.ts  # EXTEND existing token exchange service
      - src/mcp/auth/secure-token-storage.ts  # REUSE existing secure token storage
      - src/mcp/auth/github-token-manager.ts  # GitHub-specific token management
    oauth_discovery:
      - src/mcp/discovery/oauth-discovery-client.ts  # OAuth server metadata discovery
      - src/mcp/discovery/discovery-cache.ts  # Discovery metadata caching
      - src/mcp/config/mcp-configuration-manager.ts  # MCP configuration management
    http_sse_transport:
      - src/mcp/transports/http-sse-transport.ts  # Primary HTTP/SSE transport implementation
      - src/mcp/clients/github-http-sse-client.ts  # GitHub HTTP/SSE MCP client
      - src/mcp/clients/remote-service-client.ts  # Base class for remote service clients
    protocol_handling:
      - src/electron/main.ts  # EXTEND existing handleDeepLink function (NO MODIFICATION)
      - src/mcp/auth/github-oauth-callback-processor.ts  # GitHub-specific callback processor
      - src/mcp/auth/callback-url-parser.ts  # REUSE existing URL parser
      - src/mcp/auth/oauth-callback-router.ts  # Route callbacks to appropriate services
    ui_components:
      - src/ui/pages/MCPDashboard.tsx
      - src/ui/components/GitHubServiceCard.tsx
      - src/ui/components/ServiceAuthDialog.tsx
      - src/ui/components/AuthProgressIndicator.tsx
    error_handling:
      - src/mcp/error/github-oauth-error-handler.ts
      - src/mcp/error/config-error-handler.ts
      - src/mcp/error/service-error-handler.ts
    integration:
      - src/agents/code-assistant-agent.ts
      - src/mcp/clients/github-remote-client.ts
      - src/mcp/execution/github-tool-executor.ts

  security_considerations:
    pkce_implementation:
      - code_verifier_entropy: "Minimum 128 characters, cryptographically secure"
      - code_challenge_method: "S256 (SHA256) only, no plain text"
      - state_parameter_validation: "Prevent CSRF attacks"
    token_security:
      - storage_encryption: "OS-level keychain/credential manager using alpine-app service"
      - memory_protection: "Clear sensitive data from memory after use"
      - transmission_security: "HTTPS only, certificate pinning"
    redirect_uri_validation:
      - exact_match_required: "thealpinecode.alpineintellect://auth-callback only"
      - protocol_scheme_validation: "Only thealpinecode.alpineintellect:// scheme allowed"
      - parameter_sanitization: "Validate all callback parameters"

  github_specific_considerations:
    api_rate_limits:
      - authenticated_requests: "5000 requests per hour"
      - rate_limit_headers: "X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset"
      - handling_strategy: "Respect rate limits, implement exponential backoff"
    token_characteristics:
      - no_refresh_tokens: "GitHub doesn't provide refresh tokens for OAuth apps"
      - no_expiration: "GitHub tokens don't expire automatically"
      - revocation_detection: "Monitor for 401 responses indicating revoked tokens"
    required_scopes:
      - repo: "Full control of private repositories"
      - user: "Read/write access to profile info"
      - read_org: "Read org and team membership"

  testing_strategy:
    unit_tests:
      - github_oauth_flow_manager_test: "Test OAuth flow initiation and state management"
      - token_storage_test: "Test secure token storage and retrieval"
      - callback_processor_test: "Test OAuth callback processing and validation"
    integration_tests:
      - end_to_end_oauth_flow: "Complete OAuth flow from trigger to token storage"
      - github_api_integration: "Test authenticated GitHub API calls"
      - error_recovery_scenarios: "Various error conditions and recovery"
    security_tests:
      - pkce_parameter_validation: "Verify PKCE implementation security"
      - state_parameter_protection: "Test CSRF protection"
      - token_storage_security: "Verify secure token storage"

  success_criteria:
    functional_requirements:
      - [ ] User can initiate GitHub OAuth flow from multiple UI entry points
      - [ ] OAuth flow completes successfully with PKCE security
      - [ ] Tokens are stored securely using existing alpine-app keytar service
      - [ ] Code Assistant Agent can use GitHub tools after authentication
      - [ ] Error handling provides clear user feedback and recovery options
    security_requirements:
      - [ ] PKCE implementation follows OAuth 2.1 security best practices
      - [ ] All tokens stored in OS-level secure storage (Keytar)
      - [ ] State parameter prevents CSRF attacks
      - [ ] Redirect URI validation prevents authorization code interception
    user_experience_requirements:
      - [ ] OAuth flow completes in under 60 seconds for typical user
      - [ ] Clear progress indicators throughout authentication process
      - [ ] Graceful error handling with actionable user messages
      - [ ] Seamless tool usage after successful authentication
    integration_requirements:
      - [ ] PRESERVES all existing Alpine auth functionality
      - [ ] EXTENDS existing handleDeepLink function without modification
      - [ ] REUSES existing thealpinecode.alpineintellect:// protocol handler
      - [ ] Uses same keytar service name (alpine-app) as existing auth
      - [ ] Follows same IPC event patterns as existing authentication
      - [ ] Implements service detection to route callbacks appropriately
      - [ ] Enables independent enable/disable of GitHub OAuth
      - [ ] Establishes patterns for future OAuth providers (Figma, Slack, etc.)
      - [ ] Maintains backward compatibility with all existing systems
