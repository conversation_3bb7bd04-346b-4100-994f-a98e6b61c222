import { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ipc<PERSON>ain, shell } from "electron";
import { getIndexingMetaPath, ipcMainHandle, ipcMainOn, isDev, loadMetaJson, openFolderDialog, readAllSourceFiles, removeProjectFromMeta, updateProjectMetadata } from "./util.js";
import { getStaticData, pollResources } from "./resource-manager.js";
import { getPreloadPath, getUIPath } from "./path-resolver.js";
import { createTray } from "./tray.js";
import { createMenu } from "./menu.js";
import { createServer } from "./server.js";
import path from "path";
import { Server } from "http";
import portfinder from "portfinder";
import fs from "fs-extra";
import os from "os";
import "dotenv/config";
import { AIN_INDEX_DIR, SERVER_PORT_FILE } from "./constants.js";
import { exec } from "child_process";
import log from "electron-log";
import { promisify } from "util";
import { resolveIDEExecutable } from "./ide-detector/resolveExecutable.js";
import { getIDEVersion } from "./ide-detector/versionChecker.js";
import {
    getPluginInfo,
    getPluginUpdateStatus,
    isPluginInstalled,
} from "./ide-detector/pluginDetector.js";
import {
    detectInstalledIDEs,
    IDEName,
} from "./ide-detector/detectIdeInstalled.js";
import { downloadPluginFile } from "./plugin-manager/pluginDownloader.js";
import { installPlugin } from "./plugin-manager/pluginInstaller.js";
import { IDEDefinition, ideRegistry } from "./ide-detector/ideRegistry.js";
import { SessionManager, SessionMetadata } from "./session-manager.js";
import { CodebaseIndexer } from "./core/indexing/index-initializer.js";
import { cancelIndexing, startReindexingFromRenderer } from "./core/main.js";
import { ModelConfigManager, ModelConfiguration } from "./core/models/model-manager.js";
import { MCPHost } from "./mcp/host/mcp-host.js";

log.initialize();
export const indexer = new CodebaseIndexer();

if (app.isPackaged) {
    process.env.NODE_ENV = "production";
    process.env.CONSOLE_APP_URL_MAIN = "https://app.thealpinecode.com";
} else {
    process.env.NODE_ENV = "development";
    process.env.VITE_CONSOLE_URL = "https://app.thealpinecode.com";
}

log.debug("Environment Variables:", {
    Envrmnt: process.env.NODE_ENV,
    ConsoleDevURL: process.env.VITE_CONSOLE_URL,
    ConsoleProdURL: process.env.CONSOLE_APP_URL_MAIN,
});

const execAsync = promisify(exec);

const sessionManager = new SessionManager();


// Declare globally
let server: Server | null = null;
let deeplinkUrl: string | null = null;
let mainWindow: BrowserWindow | null = null;
let pendingAuthToken: string | null = null;
let mcpHost: MCPHost | null = null;
let willClose = false;

const DOWNLOAD_DIR = os.tmpdir(); // or use app.getPath('downloads')

app.setName("AlpineIntellect");

// Register protocol handler for deep linking
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient(
      'thealpinecode.alpineintellect',
      process.execPath,
      [path.resolve(process.argv[1])]
    );
  }
} else {
  app.setAsDefaultProtocolClient('thealpinecode.alpineintellect');
}

//Single Instance Lock and Deep Link Handling
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, argv) => {
    // Windows: protocol URL will be in argv
    const url = argv.find(arg => arg.startsWith('thealpinecode.alpineintellect://'));
    if (url) handleDeepLink(url);
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.show();
      mainWindow.focus();
    }
  });
}

// macOS handler for deep links
app.on('open-url', (event, url) => {
  event.preventDefault();
  deeplinkUrl = url;
  handleDeepLink(deeplinkUrl);
});

function createMainWindow() {
  mainWindow = new BrowserWindow({
    webPreferences: {
      preload: getPreloadPath(),
      contextIsolation: true,
      nodeIntegration: false,
    },
    frame: false,
    show: false, // don't show until ready
  });

  if (isDev()) {
    mainWindow.loadURL("http://localhost:5132");
  } else {
    mainWindow.loadFile(getUIPath());
  }

  mainWindow.once("ready-to-show", () => {
    mainWindow?.show();
    if (app.dock) app.dock.show();
  });

  // Deep link token handling
  if (pendingAuthToken) {
    mainWindow.webContents.once("did-finish-load", () => {
      mainWindow?.webContents.send("auth-success", pendingAuthToken);
      pendingAuthToken = null;
    });
  }

  pollResources(mainWindow);
  createTray(mainWindow);
  handleCloseEvents(mainWindow);
  createMenu(mainWindow);
}

app.on("ready", async () => {
    try {

        // const registered = app.setAsDefaultProtocolClient('thealpinecode.alpineintellect');
        // if (registered) {
        //     console.log('Protocol handler registered: thealpinecode.alpineintellect://');
        // } else {
        //     console.log('Protocol already registered or failed');
        // }
        // mainWindow = new BrowserWindow({
        //     webPreferences: {
        //         preload: getPreloadPath(),
        //         contextIsolation: true,
        //         nodeIntegration: false,
        //     },
        //     // disables default system frame (dont do this if you want a proper working menu bar)
        //     frame: false,
            
        // });

        /* Send pending token to renderer if it arrived early */
        // if (pendingAuthToken) {
        //     mainWindow.webContents.once("did-finish-load", () => {
        //         mainWindow?.webContents.send("auth-success", pendingAuthToken);
        //         pendingAuthToken = null;
        //     });
        // }

        // if (isDev()) {
        //     mainWindow.loadURL("http://localhost:5132");
        // } else {
        //     mainWindow.loadFile(getUIPath());
        // }

        // pollResources(mainWindow);

        ipcMainHandle("getStaticData", () => {
            return getStaticData();
        });

        ipcMainOn("sendFrameAction", (payload) => {
          if (mainWindow) {
            switch (payload) {
              case "CLOSE":
                mainWindow.close();
                break;
              case "MAXIMIZE":
                mainWindow.isMaximized() ? mainWindow.unmaximize() : mainWindow.maximize();
                break;
              case "MINIMIZE":
                mainWindow.minimize();
                break;
            }
          }
        });

        // createTray(mainWindow);
        // handleCloseEvents(mainWindow);
        // createMenu(mainWindow);

        createMainWindow();
        // Create and start Express server
        const expressApp = createServer();

        portfinder.basePort = +(process.env.BASE_SERVER_PORT || "3000");

        portfinder.getPort((err, port) => {
            if (err) {
                console.error("Error finding available port:", err);
                return;
            }

            server = expressApp.listen(port, "127.0.0.1", () => {
                console.log(`Embedding server started on port ${port}`);

                // Prepare the config directory and file path
                const configDir = path.join(os.homedir(), AIN_INDEX_DIR);
                const configFile = path.join(configDir, SERVER_PORT_FILE);

                // Ensure the directory exists
                fs.mkdir(configDir, { recursive: true }, (err) => {
                    if (err) {
                        console.error(
                            "Failed to create config directory:",
                            err
                        );
                        return;
                    }

                    // Write the port to the config file
                    fs.writeFile(
                        configFile,
                        JSON.stringify({ port }, null, 2),
                        (err) => {
                            if (err) {
                                console.error(
                                    "Failed to write port to config file:",
                                    err
                                );
                            } else {
                                console.log(
                                    `Port ${port} written to ${configFile}`
                                );
                            }
                        }
                    );
                });
            });

            expressApp.get("/validate-session", (req: any, res: any) => {
              const sessionId = req.query.session as string;
              const { valid, session } = sessionManager.validateSession(sessionId);

              if (!valid) {
                return res.status(403).json({ valid: false, reason: "invalid or expired" });
              }

              console.log("validate session ");

              res.json({
                valid: true,
                source: session!.source,
                redirectUri: session!.redirectUri,
              });

            });

            ipcMain.handle("create-login-session", async (_event, payload?: SessionMetadata) => {
              const sessionId = sessionManager.createSession({
                source: payload?.source || "desktop",
                redirectUri: payload?.redirectUri,
              });

              console.log("🆕 Created login session:", sessionId);
              return sessionId;
            });
        });

        // Initialize MCP Host
        try {
            const mcpConfigPath = path.join(process.cwd(), 'mcp-config.json');
            mcpHost = new MCPHost({ configPath: mcpConfigPath });
            await mcpHost.initialize();
            console.log("✅ MCP Host initialized successfully");
        } catch (error) {
            console.error("❌ Failed to initialize MCP Host:", error);
            // Continue without MCP functionality
        }

        /* Handle Windows deep link if passed via argv */
        if (process.platform === 'win32') {
            const argWithProtocol = process.argv.find(arg => arg.startsWith('thealpinecode.alpineintellect://'));
            if (argWithProtocol) {
            deeplinkUrl = argWithProtocol;
            handleDeepLink(deeplinkUrl);
            }
        }
    } catch (error) {
        console.error("Error during app initialization:", error);
        app.quit();
    }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createMainWindow();
  } else {
    mainWindow.show();
    mainWindow.focus();
    if (app.dock) app.dock.show();
  }
});

// Extract token from URL
function handleDeepLink(url: string) {
  try {
    console.log("🔗 Processing deep link:", url);
    const parsedUrl = new URL(url);

    // For custom protocol URLs, we need to construct the full path
    // thealpinecode.alpineintellect://auth-callback/github becomes:
    // - hostname: "auth-callback"
    // - pathname: "/github"
    // We need to combine them to get the full path: "/auth-callback/github"
    const fullPath = parsedUrl.hostname + parsedUrl.pathname;
    console.log("📍 Parsed URL hostname:", parsedUrl.hostname);
    console.log("📍 Parsed URL pathname:", parsedUrl.pathname);
    console.log("📍 Full path:", fullPath);
    console.log("📍 Parsed URL search params:", parsedUrl.searchParams.toString());

    // EXISTING Alpine auth logic (PRESERVE UNCHANGED)
    const token = parsedUrl.searchParams.get("token");
    if (token) {
      console.log("🔐 Received Token:", token);
      import('keytar').then(mod => {
        const keytar = mod.default;
        keytar.setPassword('alpine-app', 'auth-token', token);
      }).catch(console.error);

      if (mainWindow) {
        // Show and focus the window
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        mainWindow.setAlwaysOnTop(true);
        setTimeout(() => {
          if(mainWindow) mainWindow.setAlwaysOnTop(false)
        }, 1000);

        mainWindow.webContents.send("auth-success", token);
      } else {
        pendingAuthToken = token;
      }
      return;
    }

    // NEW: MCP OAuth callback routing (fixed for custom protocol URLs)
    console.log("🔍 Checking if full path starts with 'auth-callback/':", fullPath.startsWith('auth-callback/'));
    if (fullPath.startsWith('auth-callback/')) {
      console.log("✅ Routing to MCP OAuth callback handler");
      handleMCPOAuthCallback(url);
      return;
    }

    console.log("🔗 Deep link received without recognized parameters:", url);
  } catch (err) {
    console.error("Failed to parse auth callback URL:", url, err);
  }
}

// NEW function for MCP OAuth callbacks
async function handleMCPOAuthCallback(url: string) {
  try {
    console.log("🔗 MCP OAuth callback received:", url);

    if (!mcpHost) {
      console.error("❌ MCP Host not initialized");
      if (mainWindow) {
        mainWindow.webContents.send("mcp-oauth-callback-error", "MCP Host not initialized");
      }
      return;
    }

    // Handle OAuth callback through MCP Host
    await mcpHost.handleOAuthCallback(url);

    console.log("✅ MCP OAuth callback processed successfully");

    if (mainWindow) {
      // Show and focus the window
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.show();
      mainWindow.focus();
      mainWindow.setAlwaysOnTop(true);
      setTimeout(() => {
        if(mainWindow) mainWindow.setAlwaysOnTop(false)
      }, 1000);

      mainWindow.webContents.send("mcp-oauth-callback-success", { url });
    }
  } catch (error) {
    console.error("❌ MCP OAuth callback error:", error);

    if (mainWindow) {
      mainWindow.webContents.send("mcp-oauth-callback-error", {
        error: error instanceof Error ? error.message : String(error),
        url
      });
    }
  }
}

// Register IPC handler to get exsiting meta
ipcMain.handle("load-existing-meta", async () => {
  const metaPath = getIndexingMetaPath();
  const meta = await loadMetaJson(metaPath);
  // TO DO update meta data lastIndexed, indexingDuration, color, createdAt, based on available data 
  
  return updateProjectMetadata(meta);
});


ipcMain.handle("check-plugin-update", async (_event, ide: IDEName) => {
  const result = await getPluginUpdateStatus(ide as any);
  console.log('check-plugin-update:', result);
  return result;
});


ipcMain.handle('download-plugin', async (_event, url: string) => {
  return await downloadPluginFile(url);
});

ipcMain.handle('install-plugin', async (_event, filePath: string, ide: IDEName) => {
  return await installPlugin(filePath, ide as any);
});

// MCP IPC Handlers
ipcMain.handle('mcp-authenticate-service', async (_event, serviceId: string) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const authUrl = await mcpHost.authenticateService(serviceId);
    return { success: true, authUrl };
  } catch (error) {
    console.error(`Failed to authenticate MCP service ${serviceId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-get-service-status', async (_event, serviceId: string) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const status = await mcpHost.getServiceStatus(serviceId);
    return { success: true, status };
  } catch (error) {
    console.error(`Failed to get MCP service status for ${serviceId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-get-all-services-status', async (_event) => {
  try {
    console.log('🔍 UI requesting all MCP services status...');
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const statuses = await mcpHost.getAllServicesStatus();
    console.log(`📊 Returning ${Object.keys(statuses).length} service statuses:`, Object.keys(statuses));
    return { success: true, statuses };
  } catch (error) {
    console.error('Failed to get all MCP services status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-execute-tool', async (_event, serviceId: string, toolName: string, parameters: any) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const result = await mcpHost.executeTool(serviceId, toolName, parameters);
    return { success: true, result };
  } catch (error) {
    console.error(`Failed to execute MCP tool ${toolName} on ${serviceId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-enable-service', async (_event, serviceId: string) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    await mcpHost.enableService(serviceId);
    return { success: true };
  } catch (error) {
    console.error(`Failed to enable MCP service ${serviceId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-disable-service', async (_event, serviceId: string) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    await mcpHost.disableService(serviceId);
    return { success: true };
  } catch (error) {
    console.error(`Failed to disable MCP service ${serviceId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-get-host-stats', async (_event) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const stats = await mcpHost.getHostStats();
    return { success: true, stats };
  } catch (error) {
    console.error('Failed to get MCP host stats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-perform-health-check', async (_event) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const healthCheck = await mcpHost.performHealthCheck();
    return { success: true, healthCheck };
  } catch (error) {
    console.error('Failed to perform MCP health check:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-get-tool-execution-metrics', async (_event, timeWindowMs?: number) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    const metrics = await mcpHost.getToolExecutionMetrics(timeWindowMs);
    return { success: true, metrics };
  } catch (error) {
    console.error('Failed to get MCP tool execution metrics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

ipcMain.handle('mcp-initialize-authenticated-services', async (_event) => {
  try {
    if (!mcpHost) {
      throw new Error('MCP Host not initialized');
    }
    await mcpHost.initializeAuthenticatedServices();
    return {
      success: true,
      message: 'Authenticated services initialized successfully'
    };
  } catch (error) {
    console.error('Failed to initialize authenticated services:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});


ipcMain.handle('remove-index', async (_event, filePath: string) => {
  try {
    log.info(`[IPC] Removing index for path: ${filePath}`);
    await indexer.clearDirIndexes(filePath);  // Step 1: DB + LanceDB cleanup
    await removeProjectFromMeta(filePath);    // Step 2: Meta cleanup + Renderer sync
    return { success: true };
  } catch (error: any) {
    log.error(`[IPC] Failed to remove index for ${filePath}: ${error.message}`);
    return { success: false, error: error.message };
  }
});


ipcMain.handle('reindex', async (_event, dirPath: string) => {
  try {
    log.info(`[IPC] Reindexing started for ${dirPath}`);
    await startReindexingFromRenderer(dirPath);
    return { success: true };
  } catch (error: any) {
    log.error(`Failed to start reindexing for ${dirPath}:`, error.message);
    return { success: false, error: error.message };
  }
});


ipcMain.handle('resume-indexing', async (_event, dirPath: string) => {
  try {
    log.info(`[IPC] Resume indexing for ${dirPath}`);
    // Step 1: Load current progress from meta
    const metaJsonPath = getIndexingMetaPath();
    const existingMeta = await loadMetaJson(metaJsonPath);
    const currentMeta = existingMeta[dirPath] || {};
    const currentProgress = currentMeta.progress ?? 0;

    // Step 2: Patch meta with same progress (preserve it)
    await indexer.updateIndexingMeta(dirPath, {
      status: 'indexing',
      lastIndexed: 'Just resumed',
      progress: currentProgress,
    });

    // Step 3: Resume indexing
    await startReindexingFromRenderer(dirPath);
    return { success: true };
  } catch (error: any) {
    log.error(`Failed to resume indexing for ${dirPath}:`, error.message);
    return { success: false, error: error.message };
  }
});


ipcMain.handle('pause-indexing', async (_event, dirPath: string, curProgress: number) => {
  try {
    log.info(`[IPC] Pause indexing for ${dirPath}`);

    // Step 1: Cancel current indexing
    await cancelIndexing(dirPath, curProgress);

    // Step 2: Get current progress
    const currentProgress = curProgress ?? 0;

    // Step 3: Update meta file with paused status + progress
    await indexer.updateIndexingMeta(dirPath, {
      status: 'paused',
      lastIndexed: 'Just paused',
      progress: currentProgress,
    });


    return { success: true };
  } catch (error: any) {
    log.error(`Failed to pause indexing for ${dirPath}:`, error.message);
    return { success: false, error: error.message };
  }
});


type Status = 'installed' | 'not-installed' | 'unknown';

interface IDEScanResult {
  ideId: string;
  name: string;
  version: string;
  status: Status;
  pluginStatus: Status;
  pluginVersion: string;
  lastUpdated: string | null;
}

const typedRegistry = ideRegistry as Record<IDEName, IDEDefinition>;

ipcMain.handle('scan-ides', async (): Promise<IDEScanResult[]> => {
  const ideList: IDEName[] = Object.keys(typedRegistry) as IDEName[];
  const installedMap = detectInstalledIDEs(ideList);

  const results: IDEScanResult[] = ideList.map((ide) => {
    const { label, pluginId } = typedRegistry[ide];
    const isInstalled = installedMap[ide];

    let version = '-';
    let pluginStatus: Status = 'not-installed';
    let pluginVersion = '-';
    let lastUpdated: string | null = null;

    if (isInstalled) {
      const resolved:any = resolveIDEExecutable(ide as any);

      console.log("resolved path",resolved);
      if (resolved.path && resolved.source !== 'prompt' as any) {
        try {
          version = getIDEVersion(resolved.path) ?? '-';
        } catch (err) {
          console.warn(`Failed to get version for ${ide}`, err);
          version = '-';
        }

        if (pluginId) {
          try {
            const pluginInfo = getPluginInfo(ide as any, pluginId);
            pluginStatus = pluginInfo.pluginStatus;
            pluginVersion = pluginInfo.installedVersion;
            lastUpdated = pluginInfo.lastUpdated;
          } catch (err) {
            console.warn(`Failed to get plugin info for ${ide}`, err);
          }
        }
      } else {
        console.warn(`${ide} CLI not found:`, resolved.reason);
        version = '-';
        pluginStatus = 'unknown';
      }
    }

    return {
      ideId: ide,
      name: label,
      version,
      status: isInstalled ? 'installed' : 'not-installed',
      pluginStatus,
      pluginVersion,
      lastUpdated,
    };
  });

  console.log('scan-ides complete:', results);
  return results;
});


ipcMain.handle('get-ide-plugins', async (_event, ideName: IDEName) => {
  const pluginFolders: string[] = [];

  const platform = process.platform as 'darwin' | 'win32' | 'linux';
  const ide = typedRegistry[ideName];

  if (!ide) {
    console.warn(`IDE "${ideName}" not found in registry`);
    return pluginFolders;
  }

  const pluginPath = ide.pluginPath?.[platform];
  if (!pluginPath || !fs.existsSync(pluginPath)) {
    console.warn(`Plugin path not found for IDE "${ideName}" at "${pluginPath}"`);
    return pluginFolders;
  }

  const pluginId = ide.pluginId;

  const folders = fs.readdirSync(pluginPath);
  folders.forEach(folder => {
    if (!folder || folder.startsWith('.')) return;

    // If pluginId is defined, highlight matching folders
    if (pluginId && folder.includes(pluginId)) {
      pluginFolders.push(folder); // optionally highlight with a flag
    }
  });

  console.log(`[get-ide-plugins] ${ide.label}:`, pluginFolders);
  return pluginFolders;
});



ipcMain.handle("get-auth-token", async () => {
  try {
    const keytar = (await import("keytar")).default;
    return await keytar.getPassword("alpine-app", "auth-token");
  } catch (err) {
    console.error("keytar error:", err);
    return null;
  }
});

ipcMain.handle("logout", async () => {
  try {
    const keytar = (await import("keytar")).default;
    await keytar.deletePassword("alpine-app", "auth-token");
    console.log("🚪 Logged out: token removed from keytar");
    return { success: true };
  } catch (error) {
    console.error("Logout failed:", error);
    return { success: false, error };
  }
});

// Model Management IPC Handlers
ipcMain.handle('get-model-configuration', async () => {
  try {
    const configManager = new ModelConfigManager();
    const config = await configManager.loadConfig();
    log.info('Model configuration loaded successfully');
    return config;
  } catch (error: any) {
    log.error('Failed to load model configuration:', error.message);
    throw error;
  }
});

ipcMain.handle('save-model-configuration', async (_, config: ModelConfiguration) => {
  try {
    const configManager = new ModelConfigManager();
    await configManager.saveConfig(config);
    log.info('Model configuration saved successfully');
    return { success: true };
  } catch (error: any) {
    log.error('Failed to save model configuration:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('toggle-model', async (_, modelId: string, modelType: 'llm' | 'embedding') => {
  try {
    const configManager = new ModelConfigManager();
    const config = await configManager.loadConfig();

    if (modelType === 'llm') {
      const model = config.llmModels.find(m => m.id === modelId);
      if (model) {
        model.enabled = !model.enabled;
        log.info(`LLM model ${modelId} ${model.enabled ? 'enabled' : 'disabled'}`);
      }
    } else {
      const model = config.embeddingModels.find(m => m.id === modelId);
      if (model) {
        model.enabled = !model.enabled;
        // Update status based on enabled state and provider
        if (model.provider === 'local') {
          model.status = model.enabled ? 'running' : 'stopped';
        } else {
          // For API-based models, status is always 'running' when enabled
          model.status = model.enabled ? 'running' : 'stopped';
        }
        log.info(`Embedding model ${modelId} ${model.enabled ? 'enabled' : 'disabled'}`);
      }
    }

    await configManager.saveConfig(config);
    return { success: true };
  } catch (error: any) {
    log.error('Failed to toggle model:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('set-preferred-model', async (_, type: string, modelId: string) => {
  try {
    const configManager = new ModelConfigManager();
    const config = await configManager.loadConfig();

    switch (type) {
      case 'chat':
        config.preferences.defaultChatModel = modelId;
        break;
      case 'indexing':
        config.preferences.defaultIndexingModel = modelId;
        break;
      // case 'code-assistant':
      //   config.preferences.defaultCodeAssistantModel = modelId;
      //   break;
      default:
        throw new Error(`Unknown preference type: ${type}`);
    }

    await configManager.saveConfig(config);
    log.info(`Preferred ${type} model set to ${modelId}`);
    return { success: true };
  } catch (error: any) {
    log.error('Failed to set preferred model:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-model-cache', async (_, modelId: string) => {
  try {
    // Clear local embedding model cache
    log.info(`Clearing cache for model ${modelId}`);

    const configManager = new ModelConfigManager();
    const config = await configManager.loadConfig();
    const model = config.embeddingModels.find(m => m.id === modelId);

    if (model && model.provider === 'local') {
      // TODO: Implement actual local model cache clearing
      // This could involve clearing downloaded model files, embeddings cache, etc.
      await new Promise(resolve => setTimeout(resolve, 1000));
      log.info(`Cache cleared for local model ${modelId}`);
    } else {
      log.info(`No cache to clear for non-local model ${modelId}`);
    }

    return { success: true };
  } catch (error: any) {
    log.error('Failed to clear model cache:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sync-with-plugins', async () => {
  try {
    const configManager = new ModelConfigManager();
    const config = await configManager.loadConfig();

    // Create shared config for VS Code extension and other plugins
    await configManager.saveConfig(config); // This triggers shared config creation

    log.info('Model configuration synced with plugins');
    return { success: true };
  } catch (error: any) {
    log.error('Failed to sync with plugins:', error.message);
    return { success: false, error: error.message };
  }
});



function handleCloseEvents(mainWindow: BrowserWindow) {
  mainWindow.on("close", (e) => {
    if (!willClose) {
      e.preventDefault();
      mainWindow.hide();
      if (app.dock) app.dock.hide();
    }
  });

  app.on("before-quit", async () => {
    willClose = true;
    if (server) server.close();

    // Shutdown MCP Host
    if (mcpHost) {
      try {
        await mcpHost.shutdown();
        console.log("✅ MCP Host shutdown completed");
      } catch (error) {
        console.error("❌ Error during MCP Host shutdown:", error);
      }
    }
  });

  mainWindow.on("show", () => {
    willClose = false;
  });
}


ipcMain.handle('open-folder-dialog', async (event) => {
  const win = BrowserWindow.getFocusedWindow();
  return await openFolderDialog(win!);
});


ipcMain.handle('read-source-files', async (_, folderPath) => {
  return readAllSourceFiles(folderPath);
});

ipcMain.handle('save-file', async (_event, folderPath: string, fileName: string, content: string) => {
  try {
    const filePath = path.join(folderPath, fileName);
    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true, path: filePath };
  } catch (error) {
    console.error('File write error:', error);
    return { success: false, error: error };
  }
});

ipcMain.on('open-external', (event, url) => {
  log.info('Opening external URL:', url);
  shell.openExternal(url).catch(err => {
    log.error('shell.openExternal failed, trying OS-specific exec:', err);
    let command = '';
    switch (os.platform()) {
      case 'darwin':
        command = `open "${url}"`;
        break;
      case 'win32':
        command = `start "" "${url}"`;
        break;
      case 'linux':
        command = `xdg-open "${url}"`;
        break;
      default:
        log.error(`Unsupported platform: ${os.platform()}`);
        return;
    }
    exec(command, (error) => {
      if (error) {
        log.error(`exec open failed on ${os.platform()}:`, error);
      }
    });
  });
});




