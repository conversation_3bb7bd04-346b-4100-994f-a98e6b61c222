import { ServiceRegistry } from '../registry/service-registry.js';
import { ToolResult } from '../clients/remote-service-client.js';

export interface ToolExecutionRequest {
  serviceId: string;
  toolName: string;
  parameters: any;
  requestId?: string;
  timeout?: number;
}

export interface ToolExecutionResult {
  success: boolean;
  result?: ToolResult;
  error?: string;
  executionTime: number;
  requestId?: string;
}

export interface ToolExecutionMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  executionsByService: Record<string, number>;
  executionsByTool: Record<string, number>;
}

export class ToolExecutionEngine {
  private serviceRegistry: ServiceRegistry;
  private executionHistory: Array<{
    request: ToolExecutionRequest;
    result: ToolExecutionResult;
    timestamp: number;
  }> = [];
  private readonly MAX_HISTORY_SIZE = 1000;

  constructor(serviceRegistry: ServiceRegistry) {
    this.serviceRegistry = serviceRegistry;
  }

  async executeTool(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    const requestId = request.requestId || this.generateRequestId();

    console.log(`Executing tool ${request.toolName} on service ${request.serviceId}`);

    try {
      // Validate request
      this.validateExecutionRequest(request);

      // Get service
      const service = this.serviceRegistry.getService(request.serviceId);
      if (!service) {
        throw new Error(`Service not found: ${request.serviceId}`);
      }

      // Check service status
      const authStatus = await service.getAuthStatus();
      if (!authStatus.authenticated) {
        throw new Error(`Service ${request.serviceId} is not authenticated`);
      }

      if (!service.isHealthy()) {
        throw new Error(`Service ${request.serviceId} is not healthy`);
      }

      // Check if tool is available
      const availableTools = service.getAvailableTools();
      if (!availableTools.includes(request.toolName)) {
        throw new Error(`Tool ${request.toolName} is not available on service ${request.serviceId}`);
      }

      // Execute tool with timeout
      const result = await this.executeWithTimeout(
        () => service.executeTool(request.toolName, request.parameters),
        request.timeout || 30000
      );

      const executionTime = Date.now() - startTime;
      const executionResult: ToolExecutionResult = {
        success: true,
        result,
        executionTime,
        requestId
      };

      // Record execution
      this.recordExecution(request, executionResult);

      console.log(`✅ Tool ${request.toolName} executed successfully in ${executionTime}ms`);
      return executionResult;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const executionResult: ToolExecutionResult = {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime,
        requestId
      };

      // Record failed execution
      this.recordExecution(request, executionResult);

      console.error(`❌ Tool ${request.toolName} execution failed:`, error);
      return executionResult;
    }
  }

  async executeBatch(requests: ToolExecutionRequest[]): Promise<ToolExecutionResult[]> {
    console.log(`Executing batch of ${requests.length} tools`);

    const results = await Promise.allSettled(
      requests.map(request => this.executeTool(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          error: result.reason instanceof Error ? result.reason.message : String(result.reason),
          executionTime: 0,
          requestId: requests[index].requestId
        };
      }
    });
  }

  private validateExecutionRequest(request: ToolExecutionRequest): void {
    if (!request.serviceId) {
      throw new Error('Service ID is required');
    }

    if (!request.toolName) {
      throw new Error('Tool name is required');
    }

    if (typeof request.parameters !== 'object') {
      throw new Error('Parameters must be an object');
    }

    if (request.timeout && (request.timeout < 1000 || request.timeout > 300000)) {
      throw new Error('Timeout must be between 1 second and 5 minutes');
    }
  }

  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      operation()
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  private recordExecution(
    request: ToolExecutionRequest,
    result: ToolExecutionResult
  ): void {
    this.executionHistory.push({
      request,
      result,
      timestamp: Date.now()
    });

    // Maintain history size limit
    if (this.executionHistory.length > this.MAX_HISTORY_SIZE) {
      this.executionHistory.shift();
    }
  }

  private generateRequestId(): string {
    return `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  getExecutionHistory(serviceId?: string, toolName?: string): Array<{
    request: ToolExecutionRequest;
    result: ToolExecutionResult;
    timestamp: number;
  }> {
    let filtered = this.executionHistory;

    if (serviceId) {
      filtered = filtered.filter(entry => entry.request.serviceId === serviceId);
    }

    if (toolName) {
      filtered = filtered.filter(entry => entry.request.toolName === toolName);
    }

    return filtered;
  }

  getExecutionMetrics(timeWindowMs?: number): ToolExecutionMetrics {
    let executions = this.executionHistory;

    if (timeWindowMs) {
      const cutoff = Date.now() - timeWindowMs;
      executions = executions.filter(entry => entry.timestamp > cutoff);
    }

    const totalExecutions = executions.length;
    const successfulExecutions = executions.filter(entry => entry.result.success).length;
    const failedExecutions = totalExecutions - successfulExecutions;

    const totalExecutionTime = executions.reduce(
      (sum, entry) => sum + entry.result.executionTime,
      0
    );
    const averageExecutionTime = totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0;

    const executionsByService: Record<string, number> = {};
    const executionsByTool: Record<string, number> = {};

    for (const entry of executions) {
      const serviceId = entry.request.serviceId;
      const toolName = entry.request.toolName;

      executionsByService[serviceId] = (executionsByService[serviceId] || 0) + 1;
      executionsByTool[toolName] = (executionsByTool[toolName] || 0) + 1;
    }

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageExecutionTime,
      executionsByService,
      executionsByTool
    };
  }

  getRecentFailures(timeWindowMs: number = 3600000): Array<{
    request: ToolExecutionRequest;
    result: ToolExecutionResult;
    timestamp: number;
  }> {
    const cutoff = Date.now() - timeWindowMs;
    return this.executionHistory.filter(
      entry => entry.timestamp > cutoff && !entry.result.success
    );
  }

  clearExecutionHistory(serviceId?: string): void {
    if (serviceId) {
      this.executionHistory = this.executionHistory.filter(
        entry => entry.request.serviceId !== serviceId
      );
    } else {
      this.executionHistory = [];
    }
  }

  getToolUsageStats(): Record<string, {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    lastUsed: number;
  }> {
    const toolStats: Record<string, {
      totalExecutions: number;
      successfulExecutions: number;
      totalExecutionTime: number;
      lastUsed: number;
    }> = {};

    for (const entry of this.executionHistory) {
      const toolKey = `${entry.request.serviceId}.${entry.request.toolName}`;
      
      if (!toolStats[toolKey]) {
        toolStats[toolKey] = {
          totalExecutions: 0,
          successfulExecutions: 0,
          totalExecutionTime: 0,
          lastUsed: 0
        };
      }

      const stats = toolStats[toolKey];
      stats.totalExecutions++;
      if (entry.result.success) {
        stats.successfulExecutions++;
      }
      stats.totalExecutionTime += entry.result.executionTime;
      stats.lastUsed = Math.max(stats.lastUsed, entry.timestamp);
    }

    // Convert to final format
    const result: Record<string, {
      totalExecutions: number;
      successRate: number;
      averageExecutionTime: number;
      lastUsed: number;
    }> = {};

    for (const [toolKey, stats] of Object.entries(toolStats)) {
      result[toolKey] = {
        totalExecutions: stats.totalExecutions,
        successRate: stats.totalExecutions > 0 ? stats.successfulExecutions / stats.totalExecutions : 0,
        averageExecutionTime: stats.totalExecutions > 0 ? stats.totalExecutionTime / stats.totalExecutions : 0,
        lastUsed: stats.lastUsed
      };
    }

    return result;
  }
}
