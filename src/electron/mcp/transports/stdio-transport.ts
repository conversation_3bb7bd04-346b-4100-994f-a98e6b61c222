import { ChildProcess } from 'child_process';
import {
  MC<PERSON>essage,
  MCPRequest,
  MCPResponse,
  MCPNotification,
  TransportMetrics,
  MessageHandler,
  <PERSON>rror<PERSON><PERSON><PERSON>,
  CloseHandler
} from './http-sse-transport.js';

export interface StdioTransportConfig {
  process: ChildProcess;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableLogging?: boolean;
}

interface PendingRequest {
  resolve: (response: MCPResponse) => void;
  reject: (error: Error) => void;
  timeout: NodeJS.Timeout;
  timestamp: number;
}

export class StdioTransport {
  private config: StdioTransportConfig;
  private process: ChildProcess;
  private messageQueue: Map<string, PendingRequest> = new Map();
  private messageHandlers: MessageHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];
  private closeHandlers: CloseHandler[] = [];
  private metrics: TransportMetrics;
  private connectionStartTime: number = 0;
  private requestTimes: number[] = [];
  private initialized: boolean = false;
  private messageBuffer: string = '';
  private nextRequestId: number = 1;

  constructor(config: StdioTransportConfig) {
    this.config = {
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      enableLogging: true,
      ...config
    };

    this.process = config.process;
    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      connectionUptime: 0
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('Stdio transport already initialized');
      return;
    }

    console.log('Initializing stdio transport...');

    try {
      // Set up process event handlers
      this.setupProcessHandlers();

      // Perform MCP handshake
      await this.performHandshake();

      this.connectionStartTime = Date.now();
      this.initialized = true;
      console.log('✅ Stdio transport initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize stdio transport:', error);
      throw error;
    }
  }

  private setupProcessHandlers(): void {
    if (!this.process.stdout || !this.process.stderr || !this.process.stdin) {
      throw new Error('Process must have stdin, stdout, and stderr pipes');
    }

    // Handle stdout data (MCP messages)
    this.process.stdout.on('data', (data: Buffer) => {
      this.handleStdoutData(data);
    });

    // Handle stderr data (logging/errors)
    this.process.stderr.on('data', (data: Buffer) => {
      if (this.config.enableLogging) {
        console.log(`[MCP Server stderr]: ${data.toString()}`);
      }
    });

    // Handle process exit
    this.process.on('exit', (code: number | null, signal: string | null) => {
      console.log(`MCP server process exited with code ${code}, signal ${signal}`);
      this.handleProcessExit(code, signal);
    });

    // Handle process errors
    this.process.on('error', (error: Error) => {
      console.error('MCP server process error:', error);
      this.handleProcessError(error);
    });
  }

  private handleStdoutData(data: Buffer): void {
    this.messageBuffer += data.toString();
    
    // Process complete JSON-RPC messages (one per line)
    const lines = this.messageBuffer.split('\n');
    this.messageBuffer = lines.pop() || ''; // Keep incomplete line in buffer

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine) {
        try {
          const message = JSON.parse(trimmedLine) as MCPMessage;
          this.processMessage(message);
        } catch (error) {
          console.error('Failed to parse MCP message:', trimmedLine, error);
        }
      }
    }
  }

  private processMessage(message: MCPMessage): void {
    if (message.id && this.messageQueue.has(String(message.id))) {
      // This is a response to a pending request
      const pendingRequest = this.messageQueue.get(String(message.id))!;
      clearTimeout(pendingRequest.timeout);
      this.messageQueue.delete(String(message.id));

      // Update metrics
      const responseTime = Date.now() - pendingRequest.timestamp;
      this.requestTimes.push(responseTime);
      if (this.requestTimes.length > 100) {
        this.requestTimes.shift();
      }
      this.updateAverageResponseTime();

      if (message.error) {
        pendingRequest.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
      } else {
        pendingRequest.resolve(message as MCPResponse);
      }
    } else {
      // This is a notification or unsolicited message
      this.messageHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in message handler:', error);
        }
      });
    }
  }

  private async performHandshake(): Promise<void> {
    console.log('Performing MCP handshake...');
    
    const initializeRequest: MCPRequest = {
      jsonrpc: "2.0",
      id: this.generateRequestId(),
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        },
        clientInfo: {
          name: "Alpine Intellect Desktop",
          version: "1.0.0"
        }
      }
    };

    try {
      const response = await this.sendRequest(initializeRequest);
      console.log('✅ MCP handshake completed:', response.result);
    } catch (error) {
      console.error('❌ MCP handshake failed:', error);
      throw error;
    }
  }

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    if (!this.initialized) {
      throw new Error('Transport not initialized');
    }

    const startTime = Date.now();
    this.metrics.requestCount++;

    // Ensure request has an ID
    if (!request.id) {
      request.id = this.generateRequestId();
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(String(request.id));
        this.metrics.errorCount++;
        reject(new Error(`Request timeout: ${request.id}`));
      }, this.config.timeout);

      this.messageQueue.set(String(request.id), {
        resolve,
        reject,
        timeout,
        timestamp: startTime
      });

      try {
        const message = JSON.stringify(request) + '\n';
        this.process.stdin!.write(message);
        
        if (this.config.enableLogging) {
          console.log(`📤 Sent MCP request: ${request.method} (${request.id})`);
        }
      } catch (error) {
        clearTimeout(timeout);
        this.messageQueue.delete(String(request.id));
        this.metrics.errorCount++;
        reject(error);
      }
    });
  }

  async sendNotification(notification: MCPNotification): Promise<void> {
    if (!this.initialized) {
      throw new Error('Transport not initialized');
    }

    try {
      const message = JSON.stringify(notification) + '\n';
      this.process.stdin!.write(message);
      
      if (this.config.enableLogging) {
        console.log(`📤 Sent MCP notification: ${notification.method}`);
      }
    } catch (error) {
      console.error('❌ Failed to send notification:', error);
      throw error;
    }
  }

  onMessage(handler: MessageHandler): void {
    this.messageHandlers.push(handler);
  }

  onError(handler: ErrorHandler): void {
    this.errorHandlers.push(handler);
  }

  onClose(handler: CloseHandler): void {
    this.closeHandlers.push(handler);
  }

  isHealthy(): boolean {
    return this.initialized && 
           this.process && 
           !this.process.killed && 
           this.process.exitCode === null;
  }

  getMetrics(): TransportMetrics {
    return {
      ...this.metrics,
      connectionUptime: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0
    };
  }

  async close(): Promise<void> {
    console.log('Closing stdio transport...');
    
    // Clear all pending requests
    for (const [, pendingRequest] of this.messageQueue) {
      clearTimeout(pendingRequest.timeout);
      pendingRequest.reject(new Error('Transport closed'));
    }
    this.messageQueue.clear();

    // Close process stdin to signal shutdown
    if (this.process.stdin && !this.process.stdin.destroyed) {
      this.process.stdin.end();
    }

    // Give process time to shutdown gracefully
    await new Promise<void>((resolve) => {
      const timeout = setTimeout(() => {
        if (!this.process.killed) {
          console.log('Force killing MCP server process...');
          this.process.kill('SIGKILL');
        }
        resolve();
      }, 5000);

      this.process.on('exit', () => {
        clearTimeout(timeout);
        resolve();
      });
    });

    // Notify close handlers
    this.closeHandlers.forEach(handler => {
      try {
        handler();
      } catch (error) {
        console.error('Error in close handler:', error);
      }
    });

    this.initialized = false;
    console.log('✅ Stdio transport closed');
  }

  private generateRequestId(): string {
    return `req_${this.nextRequestId++}`;
  }

  private updateAverageResponseTime(): void {
    if (this.requestTimes.length > 0) {
      const sum = this.requestTimes.reduce((a, b) => a + b, 0);
      this.metrics.averageResponseTime = sum / this.requestTimes.length;
    }
  }

  private handleProcessExit(code: number | null, signal: string | null): void {
    this.initialized = false;
    
    const error = new Error(`MCP server process exited with code ${code}, signal ${signal}`);
    
    // Reject all pending requests
    for (const [, pendingRequest] of this.messageQueue) {
      clearTimeout(pendingRequest.timeout);
      pendingRequest.reject(error);
    }
    this.messageQueue.clear();

    // Notify error handlers
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
      }
    });
  }

  private handleProcessError(error: Error): void {
    this.metrics.errorCount++;
    this.metrics.lastError = error.message;

    // Notify error handlers
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
      }
    });
  }
}
