import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import { StdioTransport } from '../transports/stdio-transport.js';
import { MCPRequest, MCPResponse, MCPNotification } from '../transports/http-sse-transport.js';
import { OAuthService } from '../auth/oauth-service.js';

export interface LocalServerConfig {
  executable: string;
  args?: string[];
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  restartDelay?: number;
  maxRestarts?: number;
  enableLogging?: boolean;
}

export interface LocalServiceClientConfig {
  serviceId: string;
  displayName: string;
  serverConfig: LocalServerConfig;
  oauthConfig?: {
    clientId: string;
    authorizationEndpoint: string;
    tokenEndpoint: string;
    redirectUri: string;
    scopes: string[];
    pkce: boolean;
  };
}

export interface AuthStatus {
  authenticated: boolean;
  expiresAt?: Date;
  scopes?: string[];
  error?: string;
}

export abstract class LocalServiceClient {
  public readonly serviceId: string;
  public readonly displayName: string;
  protected serverConfig: LocalServerConfig;
  protected oauthService?: OAuthService;
  
  private serverProcess: ChildProcess | null = null;
  protected transport: StdioTransport | null = null;
  private restartCount: number = 0;
  private isShuttingDown: boolean = false;
  private availableTools: string[] = [];
  private serverCapabilities: any = {};
  private lastHealthCheck: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private processStartTime: Date | null = null;

  constructor(config: LocalServiceClientConfig) {
    this.serviceId = config.serviceId;
    this.displayName = config.displayName;
    this.serverConfig = {
      timeout: 30000,
      restartDelay: 2000,
      maxRestarts: 3,
      enableLogging: true,
      ...config.serverConfig
    };

    // Initialize OAuth service if configuration is provided
    if (config.oauthConfig) {
      this.oauthService = new OAuthService({
        serviceId: this.serviceId,
        ...config.oauthConfig
      });
    }
  }

  async initialize(): Promise<void> {
    console.log(`Initializing local service client: ${this.displayName}`);

    try {
      // Perform service-specific initialization
      await this.performServiceSpecificInitialization();

      // Start the local MCP server process
      await this.startServerProcess();

      // Initialize transport
      await this.initializeTransport();

      // Discover server capabilities and tools
      await this.discoverCapabilities();

      // Start health monitoring
      this.startHealthMonitoring();

      console.log(`✅ ${this.displayName} local service client initialized successfully`);
    } catch (error) {
      console.error(`❌ Failed to initialize ${this.displayName} local service client:`, error);
      await this.cleanup();
      throw error;
    }
  }

  protected abstract performServiceSpecificInitialization(): Promise<void>;

  private async startServerProcess(): Promise<void> {
    if (this.serverProcess) {
      console.log('Server process already running');
      return;
    }

    console.log(`Starting MCP server process: ${this.serverConfig.executable}`);

    try {
      // Prepare environment variables
      const env = await this.prepareEnvironment();

      // Spawn the server process
      this.serverProcess = spawn(this.serverConfig.executable, this.serverConfig.args || [], {
        cwd: this.serverConfig.cwd || process.cwd(),
        env: {
          ...process.env,
          ...env
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Set up process event handlers
      this.setupProcessEventHandlers();

      // Wait for process to be ready
      await this.waitForProcessReady();

      console.log(`✅ MCP server process started with PID: ${this.serverProcess.pid}`);
    } catch (error) {
      console.error('❌ Failed to start MCP server process:', error);
      throw error;
    }
  }

  private async prepareEnvironment(): Promise<Record<string, string>> {
    const env: Record<string, string> = {
      ...this.serverConfig.env,
      MCP_SERVER_NAME: this.serviceId,
      MCP_LOG_LEVEL: this.serverConfig.enableLogging ? 'info' : 'error'
    };

    // Inject OAuth tokens if available
    if (this.oauthService) {
      try {
        const authStatus = await this.oauthService.getAuthStatus();
        if (authStatus.authenticated) {
          const accessToken = await this.oauthService.getAccessToken();
          env.OAUTH_ACCESS_TOKEN = accessToken;

          // Note: Refresh tokens are stored securely and not exposed directly
          // The OAuth service handles token refresh automatically
        }
      } catch (error) {
        console.warn('Failed to get OAuth tokens for environment:', error);
      }
    }

    return env;
  }

  private setupProcessEventHandlers(): void {
    if (!this.serverProcess) return;

    this.serverProcess.on('error', (error) => {
      console.error(`MCP server process error (${this.serviceId}):`, error);
      this.handleProcessError(error);
    });

    this.serverProcess.on('exit', (code, signal) => {
      console.log(`MCP server process exited (${this.serviceId}): code=${code}, signal=${signal}`);
      this.handleProcessExit(code, signal);
    });

    this.serverProcess.on('spawn', () => {
      console.log(`MCP server process spawned (${this.serviceId})`);
    });
  }

  private async waitForProcessReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('No server process'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Process ready timeout'));
      }, this.serverConfig.timeout);

      // Consider process ready when it doesn't exit immediately
      setTimeout(() => {
        if (this.serverProcess && !this.serverProcess.killed && this.serverProcess.exitCode === null) {
          clearTimeout(timeout);
          resolve();
        } else {
          clearTimeout(timeout);
          reject(new Error('Process failed to start'));
        }
      }, 1000);
    });
  }

  private async initializeTransport(): Promise<void> {
    if (!this.serverProcess) {
      throw new Error('Server process not started');
    }

    this.transport = new StdioTransport({
      process: this.serverProcess,
      timeout: this.serverConfig.timeout,
      enableLogging: this.serverConfig.enableLogging
    });

    await this.transport.initialize();
  }

  private async discoverCapabilities(): Promise<void> {
    if (!this.transport) {
      throw new Error('Transport not initialized');
    }

    try {
      // Discover tools
      const toolsResponse = await this.transport.sendRequest({
        jsonrpc: "2.0",
        id: "discover_tools",
        method: "tools/list",
        params: {}
      });

      if (toolsResponse.result && toolsResponse.result.tools) {
        this.availableTools = toolsResponse.result.tools.map((tool: any) => tool.name);
        console.log(`Discovered tools for ${this.serviceId}:`, this.availableTools);
      }

      // Discover other capabilities (resources, prompts, etc.)
      await this.discoverAdditionalCapabilities();

    } catch (error) {
      console.warn(`Failed to discover capabilities for ${this.serviceId}:`, error);
    }
  }

  protected async discoverAdditionalCapabilities(): Promise<void> {
    // Override in subclasses to discover service-specific capabilities
  }

  async executeTool(toolName: string, parameters: any): Promise<any> {
    if (!this.transport) {
      throw new Error('Transport not initialized');
    }

    if (!this.availableTools.includes(toolName)) {
      throw new Error(`Tool not available: ${toolName}`);
    }

    const request: MCPRequest = {
      jsonrpc: "2.0",
      id: `tool_${Date.now()}`,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: parameters
      }
    };

    try {
      const response = await this.transport.sendRequest(request);
      return response.result;
    } catch (error) {
      console.error(`Failed to execute tool ${toolName}:`, error);
      throw error;
    }
  }

  async getAuthStatus(): Promise<AuthStatus> {
    if (!this.oauthService) {
      return { authenticated: false };
    }

    try {
      const status = await this.oauthService.getAuthStatus();
      return {
        authenticated: status.authenticated,
        expiresAt: status.expiresAt,
        scopes: status.scopes,
        error: status.error
      };
    } catch (error) {
      return {
        authenticated: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  getAvailableTools(): string[] {
    return [...this.availableTools];
  }

  isHealthy(): boolean {
    return this.transport?.isHealthy() ?? false;
  }

  private startHealthMonitoring(): void {
    // Perform health checks every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.warn(`Health check failed for ${this.serviceId}:`, error);
      }
    }, 30000);

    this.processStartTime = new Date();
    console.log(`Started health monitoring for ${this.serviceId}`);
  }

  private async performHealthCheck(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.lastHealthCheck = new Date();

    // Check if process is still running
    if (!this.serverProcess || this.serverProcess.killed || this.serverProcess.exitCode !== null) {
      console.warn(`Process health check failed for ${this.serviceId}: process not running`);
      return;
    }

    // Check if transport is healthy
    if (!this.transport?.isHealthy()) {
      console.warn(`Transport health check failed for ${this.serviceId}`);
      return;
    }

    // Try a simple ping to the MCP server
    try {
      await this.transport.sendRequest({
        jsonrpc: "2.0",
        id: "health_ping",
        method: "ping",
        params: {}
      });
    } catch (error) {
      console.warn(`MCP server ping failed for ${this.serviceId}:`, error);
    }
  }

  getHealthMetrics(): any {
    return {
      serviceId: this.serviceId,
      processStartTime: this.processStartTime,
      lastHealthCheck: this.lastHealthCheck,
      restartCount: this.restartCount,
      isHealthy: this.isHealthy(),
      processId: this.serverProcess?.pid,
      uptime: this.processStartTime ? Date.now() - this.processStartTime.getTime() : 0,
      transportMetrics: this.transport?.getMetrics()
    };
  }

  private async handleProcessError(error: Error): Promise<void> {
    console.error(`Process error for ${this.serviceId}:`, error);
    
    if (!this.isShuttingDown) {
      await this.attemptRestart();
    }
  }

  private async handleProcessExit(code: number | null, signal: string | null): Promise<void> {
    console.log(`Process exit for ${this.serviceId}: code=${code}, signal=${signal}`);
    
    this.serverProcess = null;
    this.transport = null;

    if (!this.isShuttingDown && this.shouldRestart(code, signal)) {
      await this.attemptRestart();
    }
  }

  private shouldRestart(code: number | null, signal: string | null): boolean {
    // Don't restart if explicitly killed or max restarts exceeded
    if (signal === 'SIGKILL' || signal === 'SIGTERM' || this.restartCount >= (this.serverConfig.maxRestarts || 3)) {
      return false;
    }

    // Restart on unexpected exits
    return code !== 0;
  }

  private async attemptRestart(): Promise<void> {
    if (this.restartCount >= (this.serverConfig.maxRestarts || 3)) {
      console.error(`Max restart attempts exceeded for ${this.serviceId}`);
      return;
    }

    this.restartCount++;
    console.log(`Attempting restart ${this.restartCount} for ${this.serviceId}...`);

    try {
      // Wait before restarting
      await new Promise(resolve => setTimeout(resolve, this.serverConfig.restartDelay || 2000));

      // Clean up any remaining resources
      await this.cleanup();

      // Restart the service
      await this.initialize();

      console.log(`✅ Successfully restarted ${this.serviceId}`);
      this.restartCount = 0; // Reset counter on successful restart
    } catch (error) {
      console.error(`❌ Failed to restart ${this.serviceId}:`, error);
    }
  }

  async close(): Promise<void> {
    await this.shutdown();
  }

  async shutdown(): Promise<void> {
    console.log(`Shutting down ${this.serviceId}...`);
    this.isShuttingDown = true;

    await this.cleanup();
    console.log(`✅ ${this.serviceId} shutdown completed`);
  }

  private async cleanup(): Promise<void> {
    // Stop health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Close transport
    if (this.transport) {
      try {
        await this.transport.close();
      } catch (error) {
        console.error('Error closing transport:', error);
      }
      this.transport = null;
    }

    // Kill server process
    if (this.serverProcess && !this.serverProcess.killed) {
      try {
        this.serverProcess.kill('SIGTERM');
        
        // Wait for graceful shutdown, then force kill if needed
        await new Promise<void>((resolve) => {
          const timeout = setTimeout(() => {
            if (this.serverProcess && !this.serverProcess.killed) {
              this.serverProcess.kill('SIGKILL');
            }
            resolve();
          }, 5000);

          this.serverProcess!.on('exit', () => {
            clearTimeout(timeout);
            resolve();
          });
        });
      } catch (error) {
        console.error('Error killing server process:', error);
      }
      this.serverProcess = null;
    }
  }
}
