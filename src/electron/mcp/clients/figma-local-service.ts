import path from 'path';
import { LocalServiceClient, LocalServiceClientConfig } from './local-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../auth/oauth-discovery-client.js';

export class FigmaLocalService extends LocalServiceClient {
  private discoveredMetadata?: AuthorizationServerMetadata;

  constructor(config: ServerConfiguration) {
    // Determine the MCP server executable path
    const serverExecutable = FigmaLocalService.resolveFigmaServerPath(config);
    
    const localConfig: LocalServiceClientConfig = {
      serviceId: 'figma',
      displayName: 'Figma',
      serverConfig: {
        executable: serverExecutable.executable,
        args: serverExecutable.args,
        cwd: serverExecutable.cwd,
        env: {
          ...config.stdio?.env,
          // Additional Figma-specific environment variables
          FIGMA_API_BASE_URL: 'https://api.figma.com/v1',
          MCP_SERVER_TYPE: 'figma'
        },
        timeout: config.connection.timeout,
        restartDelay: config.stdio?.restart_delay || 2000,
        maxRestarts: config.stdio?.max_restarts || 3,
        enableLogging: true
      },
      oauthConfig: {
        clientId: config.auth.client_id,
        authorizationEndpoint: config.auth.authorization_endpoint || 'https://www.figma.com/oauth',
        tokenEndpoint: config.auth.token_endpoint || 'https://www.figma.com/api/oauth/token',
        redirectUri: config.auth.redirect_uri,
        scopes: config.auth.scopes,
        pkce: config.auth.pkce
      }
    };

    super(localConfig);
  }

  private static resolveFigmaServerPath(config: ServerConfiguration): {
    executable: string;
    args: string[];
    cwd?: string;
  } {
    if (config.stdio?.executable) {
      return {
        executable: config.stdio.executable,
        args: config.stdio.args || [],
        cwd: config.stdio.cwd
      };
    }

    // Default to Node.js with figma-mcp-server.js
    // In a real implementation, this would be bundled with the app
    const serverPath = path.join(__dirname, '..', '..', '..', 'mcp-servers', 'figma-mcp-server.js');
    
    return {
      executable: 'node',
      args: [serverPath],
      cwd: path.dirname(serverPath)
    };
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    console.log('Performing Figma-specific initialization...');

    try {
      // Verify OAuth configuration
      await this.verifyOAuthConfiguration();

      // Perform any additional Figma-specific setup
      await this.setupFigmaSpecificConfiguration();

      console.log('✅ Figma-specific initialization completed');
    } catch (error) {
      console.error('❌ Figma-specific initialization failed:', error);
      throw error;
    }
  }

  private async verifyOAuthConfiguration(): Promise<void> {
    if (!this.oauthService) {
      throw new Error('OAuth service not configured for Figma');
    }

    // Verify that we have the necessary OAuth configuration
    const authStatus = await this.oauthService.getAuthStatus();
    console.log('Figma OAuth status:', authStatus);

    // If we have discovered metadata, use it
    if (this.discoveredMetadata) {
      this.oauthService.setDiscoveredMetadata(this.discoveredMetadata);
      console.log('Using discovered OAuth metadata for Figma');
    }
  }

  private async setupFigmaSpecificConfiguration(): Promise<void> {
    // Add any Figma-specific configuration here
    // For example, validating API access, setting up webhooks, etc.
    console.log('Setting up Figma-specific configuration...');
  }

  protected async discoverAdditionalCapabilities(): Promise<void> {
    if (!this.transport) {
      return;
    }

    try {
      // Discover Figma-specific resources
      const resourcesResponse = await this.transport.sendRequest({
        jsonrpc: "2.0",
        id: "discover_resources",
        method: "resources/list",
        params: {}
      });

      if (resourcesResponse.result && resourcesResponse.result.resources) {
        console.log('Discovered Figma resources:', resourcesResponse.result.resources);
      }

      // Discover Figma-specific prompts
      const promptsResponse = await this.transport.sendRequest({
        jsonrpc: "2.0",
        id: "discover_prompts",
        method: "prompts/list",
        params: {}
      });

      if (promptsResponse.result && promptsResponse.result.prompts) {
        console.log('Discovered Figma prompts:', promptsResponse.result.prompts);
      }

    } catch (error) {
      console.warn('Failed to discover additional Figma capabilities:', error);
    }
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.discoveredMetadata = metadata;
    if (this.oauthService) {
      this.oauthService.setDiscoveredMetadata(metadata);
      console.log('Figma OAuth metadata updated from discovery');
    }
  }

  // Figma-specific tool methods
  async getFileContent(fileKey: string): Promise<any> {
    return await this.executeTool('get_file_content', { file_key: fileKey });
  }

  async exportAssets(fileKey: string, nodeIds: string[], format: string = 'png'): Promise<any> {
    return await this.executeTool('export_assets', {
      file_key: fileKey,
      node_ids: nodeIds,
      format
    });
  }

  async createComponent(fileKey: string, componentData: any): Promise<any> {
    return await this.executeTool('create_component', {
      file_key: fileKey,
      component_data: componentData
    });
  }

  async getTeamProjects(teamId: string): Promise<any> {
    return await this.executeTool('get_team_projects', { team_id: teamId });
  }

  async searchFiles(query: string, teamId?: string): Promise<any> {
    const params: any = { query };
    if (teamId) {
      params.team_id = teamId;
    }
    return await this.executeTool('search_files', params);
  }

  async getComments(fileKey: string): Promise<any> {
    return await this.executeTool('get_comments', { file_key: fileKey });
  }

  async postComment(fileKey: string, message: string, clientMeta?: any): Promise<any> {
    return await this.executeTool('post_comment', {
      file_key: fileKey,
      message,
      client_meta: clientMeta
    });
  }

  // Override to provide Figma-specific environment preparation
  private async prepareEnvironment(): Promise<Record<string, string>> {
    // Get base environment from parent class logic
    const baseEnv: Record<string, string> = {
      ...this.serverConfig.env,
      MCP_SERVER_NAME: this.serviceId,
      MCP_LOG_LEVEL: this.serverConfig.enableLogging ? 'info' : 'error'
    };

    // Add Figma-specific environment variables
    const figmaEnv: Record<string, string> = {
      ...baseEnv,
      FIGMA_SERVICE_NAME: 'figma',
      FIGMA_MCP_VERSION: '1.0.0'
    };

    // Inject OAuth tokens with Figma-specific naming
    if (this.oauthService) {
      try {
        const authStatus = await this.oauthService.getAuthStatus();
        if (authStatus.authenticated) {
          const accessToken = await this.oauthService.getAccessToken();
          figmaEnv.FIGMA_ACCESS_TOKEN = accessToken;
          figmaEnv.FIGMA_TOKEN_TYPE = 'Bearer';

          if (authStatus.expiresAt) {
            figmaEnv.FIGMA_TOKEN_EXPIRY = authStatus.expiresAt.toISOString();
          }

          if (authStatus.scopes) {
            figmaEnv.FIGMA_SCOPES = authStatus.scopes.join(',');
          }
        }
      } catch (error) {
        console.warn('Failed to get Figma OAuth tokens for environment:', error);
      }
    }

    return figmaEnv;
  }

  // Health check specific to Figma service
  async performHealthCheck(): Promise<{ healthy: boolean; details: any }> {
    const baseHealth = this.isHealthy();
    
    if (!baseHealth) {
      return {
        healthy: false,
        details: {
          transport: false,
          process: false,
          oauth: false
        }
      };
    }

    try {
      // Check if we can list tools (basic connectivity test)
      const toolsResponse = await this.transport!.sendRequest({
        jsonrpc: "2.0",
        id: "health_check",
        method: "tools/list",
        params: {}
      });

      const oauthStatus = await this.getAuthStatus();

      return {
        healthy: true,
        details: {
          transport: true,
          process: true,
          oauth: oauthStatus.authenticated,
          tools_available: toolsResponse.result?.tools?.length || 0,
          last_check: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          transport: true,
          process: true,
          oauth: false,
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  // Get Figma-specific status information
  async getFigmaStatus(): Promise<any> {
    const authStatus = await this.getAuthStatus();
    const healthCheck = await this.performHealthCheck();
    
    return {
      serviceId: this.serviceId,
      displayName: this.displayName,
      transport: 'stdio',
      authenticated: authStatus.authenticated,
      healthy: healthCheck.healthy,
      availableTools: this.getAvailableTools(),
      authStatus,
      healthDetails: healthCheck.details,
      serverMetrics: this.transport?.getMetrics()
    };
  }
}
