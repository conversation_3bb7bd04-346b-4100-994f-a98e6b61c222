import { RemoteServiceClient, ToolResult } from './remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export class FigmaService extends RemoteServiceClient {
  constructor(config: ServerConfiguration) {
    super('figma', 'Figma', config);
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.oauthService.setDiscoveredMetadata(metadata);
    console.log('Figma OAuth metadata updated from discovery');
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    console.log('Performing Figma-specific initialization...');

    try {
      // Verify Figma API access
      await this.verifyFigmaAccess();

      // Subscribe to Figma events if SSE is available
      await this.subscribeToFigmaEvents();

      console.log('✅ Figma-specific initialization completed');
    } catch (error) {
      console.error('❌ Figma-specific initialization failed:', error);
      throw error;
    }
  }

  protected handleIncomingMessage(message: any): void {
    console.log('Figma SSE message received:', message);

    // Handle different types of Figma events
    if (message.method === 'notifications/file_updated') {
      this.handleFileUpdate(message.params);
    } else if (message.method === 'notifications/comment_added') {
      this.handleCommentAdded(message.params);
    } else if (message.method === 'notifications/team_updated') {
      this.handleTeamUpdate(message.params);
    } else {
      console.log('Unhandled Figma message type:', message.method);
    }
  }

  protected handleTransportError(error: Error): void {
    console.error('Figma transport error:', error);
    
    // Implement Figma-specific error handling
    if (error.message.includes('401')) {
      console.log('Figma authentication may have expired, requiring re-authentication');
    } else if (error.message.includes('403')) {
      console.log('Figma access may be restricted for this resource');
    } else if (error.message.includes('429')) {
      console.log('Figma rate limit exceeded');
    }
  }

  protected handleTransportClose(): void {
    console.log('Figma transport connection closed');
    this.initialized = false;
  }

  // Figma-specific tool implementations
  async getFileContent(params: {
    file_key: string;
    version?: string;
    ids?: string[];
    depth?: number;
    geometry?: 'paths' | 'bounds';
    plugin_data?: string;
    branch_data?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('get_file_content', params);
  }

  async exportAssets(params: {
    file_key: string;
    ids: string[];
    format: 'jpg' | 'png' | 'svg' | 'pdf';
    scale?: number;
    svg_outline_text?: boolean;
    svg_include_id?: boolean;
    svg_simplify_stroke?: boolean;
    use_absolute_bounds?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('export_assets', params);
  }

  async createComponent(params: {
    file_key: string;
    node_id: string;
    name: string;
    description?: string;
  }): Promise<ToolResult> {
    return await this.executeTool('create_component', params);
  }

  async getTeamProjects(params: {
    team_id: string;
  }): Promise<ToolResult> {
    return await this.executeTool('get_team_projects', params);
  }

  async getProjectFiles(params: {
    project_id: string;
    branch_data?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('get_project_files', params);
  }

  async getComments(params: {
    file_key: string;
  }): Promise<ToolResult> {
    return await this.executeTool('get_comments', params);
  }

  async postComment(params: {
    file_key: string;
    message: string;
    client_meta: {
      x: number;
      y: number;
      node_id?: string;
      node_offset?: { x: number; y: number };
    };
  }): Promise<ToolResult> {
    return await this.executeTool('post_comment', params);
  }

  async getTeamStyles(params: {
    team_id: string;
    page_size?: number;
    after?: string;
  }): Promise<ToolResult> {
    return await this.executeTool('get_team_styles', params);
  }

  async getTeamComponents(params: {
    team_id: string;
    page_size?: number;
    after?: string;
  }): Promise<ToolResult> {
    return await this.executeTool('get_team_components', params);
  }

  private async verifyFigmaAccess(): Promise<void> {
    try {
      // Test basic Figma API access by getting user info
      const userInfo = await this.executeTool('get_me', {});
      console.log('✅ Figma API access verified');
    } catch (error) {
      console.error('❌ Figma API access verification failed:', error);
      throw new Error('Failed to verify Figma API access');
    }
  }

  private async subscribeToFigmaEvents(): Promise<void> {
    try {
      // Subscribe to relevant Figma events via SSE
      console.log('Subscribing to Figma events...');
      
      // The SSE connection is already established in the transport
      // This method can be used to configure which events to receive
      
      console.log('✅ Subscribed to Figma events');
    } catch (error) {
      console.warn('⚠️ Failed to subscribe to Figma events:', error);
      // Non-critical error, continue initialization
    }
  }

  private handleFileUpdate(params: any): void {
    console.log('Figma file updated:', params);
    // Emit event to main process or handle file update
  }

  private handleCommentAdded(params: any): void {
    console.log('Figma comment added:', params);
    // Emit event to main process or handle comment addition
  }

  private handleTeamUpdate(params: any): void {
    console.log('Figma team updated:', params);
    // Emit event to main process or handle team update
  }

  // Override tool response processing for Figma-specific formatting
  protected processToolResponse(response: any): ToolResult {
    if (response.result && response.result.content) {
      return {
        content: response.result.content,
        isError: false
      };
    }

    // Handle Figma-specific response formats
    if (response.result) {
      const result = response.result;
      
      // Format file content response
      if (result.document && result.name) {
        return {
          content: [{
            type: 'text',
            text: `File: ${result.name}\nLast Modified: ${result.lastModified}\nVersion: ${result.version}\nNodes: ${Object.keys(result.document.children || {}).length}`
          }],
          isError: false
        };
      }

      // Format export response
      if (result.images && typeof result.images === 'object') {
        const imageUrls = Object.values(result.images).filter(url => url);
        return {
          content: [{
            type: 'text',
            text: `Exported ${imageUrls.length} assets:\n${imageUrls.join('\n')}`
          }],
          isError: false
        };
      }

      // Format team projects response
      if (result.projects && Array.isArray(result.projects)) {
        const projects = result.projects.slice(0, 10); // Limit to first 10 projects
        const text = projects.map((project: any) => 
          `${project.name} (ID: ${project.id})`
        ).join('\n');
        
        return {
          content: [{
            type: 'text',
            text: `Found ${result.projects.length} projects:\n${text}`
          }],
          isError: false
        };
      }

      // Format comments response
      if (result.comments && Array.isArray(result.comments)) {
        const comments = result.comments.slice(0, 5); // Limit to first 5 comments
        const text = comments.map((comment: any) => 
          `${comment.user.handle}: ${comment.message}`
        ).join('\n');
        
        return {
          content: [{
            type: 'text',
            text: `Found ${result.comments.length} comments:\n${text}`
          }],
          isError: false
        };
      }
    }

    // Fallback to default processing
    return super.processToolResponse(response);
  }
}
