import { RemoteServiceClient, ToolResult } from './remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export class GitHubService extends RemoteServiceClient {
  constructor(config: ServerConfiguration) {
    super('github', 'GitHub', config);
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.oauthService.setDiscoveredMetadata(metadata);
    console.log('GitHub OAuth metadata updated from discovery');
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    console.log('Performing GitHub-specific initialization...');

    try {
      // Subscribe to GitHub events if SSE is available
      await this.subscribeToGitHubEvents();

      console.log('✅ GitHub-specific initialization completed');
      console.log('ℹ️ GitHub API access will be verified on first tool execution');
    } catch (error) {
      console.error('❌ GitHub-specific initialization failed:', error);
      throw error;
    }
  }

  protected handleIncomingMessage(message: any): void {
    console.log('GitHub SSE message received:', message);

    // Handle different types of GitHub events
    if (message.method === 'notifications/repository_updated') {
      this.handleRepositoryUpdate(message.params);
    } else if (message.method === 'notifications/issue_updated') {
      this.handleIssueUpdate(message.params);
    } else if (message.method === 'notifications/pull_request_updated') {
      this.handlePullRequestUpdate(message.params);
    } else {
      console.log('Unhandled GitHub message type:', message.method);
    }
  }

  protected handleTransportError(error: Error): void {
    console.error('GitHub transport error:', error);
    
    // Implement GitHub-specific error handling
    if (error.message.includes('401')) {
      console.log('GitHub authentication may have expired, requiring re-authentication');
    } else if (error.message.includes('403')) {
      console.log('GitHub rate limit may have been exceeded');
    }
  }

  protected handleTransportClose(): void {
    console.log('GitHub transport connection closed');
    this.initialized = false;
  }

  // GitHub-specific tool implementations
  async createRepository(params: {
    name: string;
    description?: string;
    private?: boolean;
    auto_init?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('create_repository', params);
  }

  async createIssue(params: {
    owner: string;
    repo: string;
    title: string;
    body?: string;
    labels?: string[];
    assignees?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('create_issue', params);
  }

  async searchCode(params: {
    query: string;
    sort?: 'indexed' | 'updated';
    order?: 'asc' | 'desc';
    per_page?: number;
    page?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('search_code', params);
  }

  async getFileContent(params: {
    owner: string;
    repo: string;
    path: string;
    ref?: string;
  }): Promise<ToolResult> {
    return await this.executeTool('get_file_content', params);
  }

  async listRepositories(params: {
    type?: 'all' | 'owner' | 'public' | 'private' | 'member';
    sort?: 'created' | 'updated' | 'pushed' | 'full_name';
    direction?: 'asc' | 'desc';
    per_page?: number;
    page?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('list_repositories', params);
  }

  async createPullRequest(params: {
    owner: string;
    repo: string;
    title: string;
    head: string;
    base: string;
    body?: string;
    draft?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('create_pull_request', params);
  }



  private async subscribeToGitHubEvents(): Promise<void> {
    try {
      // Subscribe to relevant GitHub events via SSE
      console.log('Subscribing to GitHub events...');
      
      // The SSE connection is already established in the transport
      // This method can be used to configure which events to receive
      
      console.log('✅ Subscribed to GitHub events');
    } catch (error) {
      console.warn('⚠️ Failed to subscribe to GitHub events:', error);
      // Non-critical error, continue initialization
    }
  }

  private handleRepositoryUpdate(params: any): void {
    console.log('Repository updated:', params);
    // Emit event to main process or handle repository update
  }

  private handleIssueUpdate(params: any): void {
    console.log('Issue updated:', params);
    // Emit event to main process or handle issue update
  }

  private handlePullRequestUpdate(params: any): void {
    console.log('Pull request updated:', params);
    // Emit event to main process or handle pull request update
  }

  // Override tool response processing for GitHub-specific formatting
  protected processToolResponse(response: any): ToolResult {
    if (response.result && response.result.content) {
      return {
        content: response.result.content,
        isError: false
      };
    }

    // Handle GitHub-specific response formats
    if (response.result) {
      const result = response.result;
      
      // Format repository creation response
      if (result.html_url && result.clone_url) {
        return {
          content: [{
            type: 'text',
            text: `Repository created successfully!\nURL: ${result.html_url}\nClone URL: ${result.clone_url}`
          }],
          isError: false
        };
      }

      // Format issue creation response
      if (result.number && result.html_url) {
        return {
          content: [{
            type: 'text',
            text: `Issue #${result.number} created successfully!\nURL: ${result.html_url}`
          }],
          isError: false
        };
      }

      // Format search results
      if (result.items && Array.isArray(result.items)) {
        const items = result.items.slice(0, 10); // Limit to first 10 results
        const text = items.map((item: any) => 
          `${item.name || item.path}: ${item.html_url}`
        ).join('\n');
        
        return {
          content: [{
            type: 'text',
            text: `Found ${result.total_count} results:\n${text}`
          }],
          isError: false
        };
      }
    }

    // Fallback to default processing
    return super.processToolResponse(response);
  }
}
