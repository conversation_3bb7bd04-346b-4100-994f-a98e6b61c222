import { HTTPSSETransport, MCPRequest, MCPResponse } from '../transports/http-sse-transport.js';
import { OAuthService, AuthStatus } from '../auth/oauth-service.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { OAuthCallbackHandler } from '../auth/oauth-callback-router.js';

export interface ToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

export interface RemoteService extends OAuthCallbackHandler {
  readonly serviceId: string;
  readonly displayName: string;
  readonly callbackPath: string;
  readonly endpoints: {
    http: string;
    sse: string;
    discovery?: string;
  };

  initialize(): Promise<void>;
  authenticate(): Promise<string>;
  executeTool(toolName: string, parameters: any): Promise<ToolResult>;
  getAuthStatus(): Promise<AuthStatus>;
  getAvailableTools(): string[];
  isHealthy(): boolean;
  close(): Promise<void>;
}

export abstract class RemoteServiceClient implements RemoteService {
  public readonly serviceId: string;
  public readonly displayName: string;
  public readonly callbackPath: string;
  public readonly endpoints: {
    http: string;
    sse: string;
    discovery?: string;
  };

  protected config: ServerConfiguration;
  protected oauthService: OAuthService;
  protected transport: HTTPSSETransport;
  protected initialized = false;

  constructor(serviceId: string, displayName: string, config: ServerConfiguration) {
    this.serviceId = serviceId;
    this.displayName = displayName;
    this.callbackPath = `/auth-callback/${serviceId}`;
    this.config = config;
    
    this.endpoints = {
      http: config.endpoints.http,
      sse: config.endpoints.sse,
      discovery: config.endpoints.discovery || ''
    };

    // Initialize OAuth service with static endpoints if available
    this.oauthService = new OAuthService({
      serviceId,
      clientId: config.auth.client_id,
      authorizationEndpoint: config.auth.authorization_endpoint || '', // Use static endpoint if available
      tokenEndpoint: config.auth.token_endpoint || '', // Use static endpoint if available
      redirectUri: config.auth.redirect_uri,
      scopes: config.auth.scopes,
      pkce: config.auth.pkce
    });

    // Initialize transport
    this.transport = new HTTPSSETransport({
      httpEndpoint: config.endpoints.http,
      sseEndpoint: config.endpoints.sse,
      getAccessToken: () => this.oauthService.getAccessToken(),
      timeout: config.connection.timeout,
      retryAttempts: config.connection.retry_attempts,
      retryDelay: config.connection.retry_delay
    });
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log(`${this.serviceId} service already initialized`);
      return;
    }

    console.log(`Initializing ${this.serviceId} service...`);

    try {
      // Check if we have valid authentication
      const authStatus = await this.oauthService.getAuthStatus();
      if (!authStatus.authenticated) {
        console.log(`${this.serviceId} not authenticated, authentication required`);
        return;
      }

      // Initialize transport
      await this.transport.initialize();

      // Set up message handlers
      this.setupMessageHandlers();

      // Perform service-specific initialization
      await this.performServiceSpecificInitialization();

      this.initialized = true;
      console.log(`✅ ${this.serviceId} service initialized successfully`);
    } catch (error) {
      console.error(`❌ Failed to initialize ${this.serviceId} service:`, error);
      throw error;
    }
  }

  async authenticate(): Promise<string> {
    console.log(`Starting authentication for ${this.serviceId}...`);
    return await this.oauthService.initializeOAuth();
  }

  async handleCallback(params: URLSearchParams): Promise<void> {
    console.log(`Handling OAuth callback for ${this.serviceId}...`);

    const code = params.get('code');
    const state = params.get('state');

    if (!code || !state) {
      throw new Error('Missing required OAuth parameters');
    }

    // Exchange code for tokens
    await this.oauthService.exchangeCodeForTokens(code, state);

    // Initialize the service now that we have tokens
    await this.initialize();
  }

  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    if (!this.initialized) {
      const authStatus = await this.oauthService.getAuthStatus();
      if (!authStatus.authenticated) {
        throw new Error(`${this.serviceId} service not initialized: authentication required`);
      } else {
        throw new Error(`${this.serviceId} service not initialized: initialization in progress or failed`);
      }
    }

    console.log(`Executing tool ${toolName} on ${this.serviceId} with parameters:`, parameters);

    try {
      const request: MCPRequest = {
        jsonrpc: "2.0",
        id: this.generateRequestId(),
        method: "tools/call",
        params: {
          name: toolName,
          arguments: parameters
        }
      };

      const response = await this.transport.sendRequest(request);

      if (response.error) {
        throw new Error(`Tool execution failed: ${response.error.message}`);
      }

      const result = this.processToolResponse(response);
      console.log(`✅ Tool ${toolName} executed successfully on ${this.serviceId}`);
      return result;
    } catch (error) {
      console.error(`❌ Tool execution failed for ${toolName} on ${this.serviceId}:`, error);
      throw error;
    }
  }

  async getAuthStatus(): Promise<AuthStatus> {
    return await this.oauthService.getAuthStatus();
  }

  getAvailableTools(): string[] {
    return this.config.tools;
  }

  isHealthy(): boolean {
    return this.initialized && this.transport.isHealthy();
  }

  async close(): Promise<void> {
    console.log(`Closing ${this.serviceId} service...`);
    
    if (this.transport) {
      await this.transport.close();
    }

    this.initialized = false;
    console.log(`✅ ${this.serviceId} service closed`);
  }

  protected setupMessageHandlers(): void {
    this.transport.onMessage((message) => {
      console.log(`Received message from ${this.serviceId}:`, message);
      this.handleIncomingMessage(message);
    });

    this.transport.onError((error) => {
      console.error(`Transport error for ${this.serviceId}:`, error);
      this.handleTransportError(error);
    });

    this.transport.onClose(() => {
      console.log(`Transport closed for ${this.serviceId}`);
      this.handleTransportClose();
    });
  }

  protected abstract performServiceSpecificInitialization(): Promise<void>;
  
  protected abstract handleIncomingMessage(message: any): void;
  
  protected abstract handleTransportError(error: Error): void;
  
  protected abstract handleTransportClose(): void;

  protected processToolResponse(response: MCPResponse): ToolResult {
    // Default implementation - can be overridden by specific services
    if (response.result && response.result.content) {
      return {
        content: response.result.content,
        isError: false
      };
    }

    return {
      content: [{
        type: 'text',
        text: JSON.stringify(response.result)
      }],
      isError: false
    };
  }

  protected generateRequestId(): string {
    return `${this.serviceId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}
