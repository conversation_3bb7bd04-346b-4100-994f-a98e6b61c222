export interface ConfigError {
  type: 'validation' | 'missing_file' | 'parse_error' | 'schema_error' | 'env_var_missing';
  field?: string;
  message: string;
  value?: any;
  timestamp: number;
}

export interface ConfigErrorHandlingResult {
  userMessage: string;
  technicalMessage: string;
  recoveryAction: {
    type: 'fix_config' | 'set_env_var' | 'contact_support' | 'use_defaults';
    message: string;
    details?: string;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  canContinue: boolean;
}

export class ConfigErrorHandler {
  private configErrors: ConfigError[] = [];
  private readonly MAX_ERROR_HISTORY = 50;

  handleConfigError(
    type: ConfigError['type'],
    message: string,
    field?: string,
    value?: any
  ): ConfigErrorHandlingResult {
    const configError: ConfigError = {
      type,
      field,
      message,
      value,
      timestamp: Date.now()
    };

    this.addToHistory(configError);

    return this.determineErrorHandling(configError);
  }

  private determineErrorHandling(error: ConfigError): ConfigErrorHandlingResult {
    switch (error.type) {
      case 'missing_file':
        return {
          userMessage: 'MCP configuration file not found. Default configuration will be created.',
          technicalMessage: `Configuration file missing: ${error.message}`,
          recoveryAction: {
            type: 'use_defaults',
            message: 'Using default configuration',
            details: 'A default configuration file will be created automatically'
          },
          severity: 'medium',
          canContinue: true
        };

      case 'parse_error':
        return {
          userMessage: 'Configuration file has invalid format. Please check the JSON syntax.',
          technicalMessage: `JSON parse error: ${error.message}`,
          recoveryAction: {
            type: 'fix_config',
            message: 'Fix configuration file syntax',
            details: 'Check for missing commas, brackets, or quotes in the configuration file'
          },
          severity: 'high',
          canContinue: false
        };

      case 'schema_error':
        return this.handleSchemaError(error);

      case 'env_var_missing':
        return this.handleMissingEnvVar(error);

      case 'validation':
        return this.handleValidationError(error);

      default:
        return {
          userMessage: 'Configuration error occurred. Please check your settings.',
          technicalMessage: `Unknown config error: ${error.message}`,
          recoveryAction: {
            type: 'contact_support',
            message: 'Contact support for assistance'
          },
          severity: 'medium',
          canContinue: false
        };
    }
  }

  private handleSchemaError(error: ConfigError): ConfigErrorHandlingResult {
    const field = error.field || 'unknown';
    
    if (field.includes('client_id')) {
      return {
        userMessage: 'OAuth client ID is missing or invalid. Please check your environment variables.',
        technicalMessage: `Invalid client_id in field: ${field}`,
        recoveryAction: {
          type: 'set_env_var',
          message: 'Set the required environment variable',
          details: 'Set GITHUB_CLIENT_ID and/or FIGMA_CLIENT_ID environment variables'
        },
        severity: 'high',
        canContinue: false
      };
    }

    if (field.includes('redirect_uri')) {
      return {
        userMessage: 'OAuth redirect URI is invalid. Please check the configuration.',
        technicalMessage: `Invalid redirect_uri in field: ${field}`,
        recoveryAction: {
          type: 'fix_config',
          message: 'Fix redirect URI in configuration',
          details: 'Ensure redirect URI uses the correct protocol: thealpinecode.alpineintellect://'
        },
        severity: 'high',
        canContinue: false
      };
    }

    if (field.includes('endpoints')) {
      return {
        userMessage: 'Service endpoint configuration is invalid. Please check the URLs.',
        technicalMessage: `Invalid endpoint in field: ${field}`,
        recoveryAction: {
          type: 'fix_config',
          message: 'Fix endpoint URLs in configuration',
          details: 'Ensure all endpoint URLs are valid HTTPS URLs'
        },
        severity: 'high',
        canContinue: false
      };
    }

    return {
      userMessage: `Configuration field "${field}" is invalid. Please check the value.`,
      technicalMessage: `Schema validation failed for field: ${field} - ${error.message}`,
      recoveryAction: {
        type: 'fix_config',
        message: 'Fix configuration field',
        details: `Check the value for field: ${field}`
      },
      severity: 'medium',
      canContinue: false
    };
  }

  private handleMissingEnvVar(error: ConfigError): ConfigErrorHandlingResult {
    const envVar = error.field || 'unknown';
    
    if (envVar.includes('CLIENT_ID')) {
      return {
        userMessage: `${envVar} environment variable is required for OAuth authentication.`,
        technicalMessage: `Missing environment variable: ${envVar}`,
        recoveryAction: {
          type: 'set_env_var',
          message: `Set ${envVar} environment variable`,
          details: `Add ${envVar}=your_client_id to your environment variables`
        },
        severity: 'high',
        canContinue: false
      };
    }

    if (envVar.includes('CLIENT_SECRET')) {
      return {
        userMessage: `${envVar} environment variable is required for OAuth authentication.`,
        technicalMessage: `Missing environment variable: ${envVar}`,
        recoveryAction: {
          type: 'set_env_var',
          message: `Set ${envVar} environment variable`,
          details: `Add ${envVar}=your_client_secret to your environment variables`
        },
        severity: 'critical',
        canContinue: false
      };
    }

    return {
      userMessage: `Required environment variable ${envVar} is missing.`,
      technicalMessage: `Missing environment variable: ${envVar}`,
      recoveryAction: {
        type: 'set_env_var',
        message: `Set ${envVar} environment variable`,
        details: `Add ${envVar}=value to your environment variables`
      },
      severity: 'medium',
      canContinue: false
    };
  }

  private handleValidationError(error: ConfigError): ConfigErrorHandlingResult {
    const field = error.field || 'unknown';
    const message = error.message;

    if (message.includes('timeout')) {
      return {
        userMessage: 'Connection timeout value is invalid. Using default value.',
        technicalMessage: `Invalid timeout in field: ${field}`,
        recoveryAction: {
          type: 'use_defaults',
          message: 'Using default timeout value',
          details: 'Timeout values should be positive numbers in milliseconds'
        },
        severity: 'low',
        canContinue: true
      };
    }

    if (message.includes('scope')) {
      return {
        userMessage: 'OAuth scope configuration is invalid. Please check the scopes.',
        technicalMessage: `Invalid scope in field: ${field}`,
        recoveryAction: {
          type: 'fix_config',
          message: 'Fix OAuth scopes in configuration',
          details: 'Ensure scopes are valid for the service provider'
        },
        severity: 'medium',
        canContinue: false
      };
    }

    return {
      userMessage: `Configuration validation failed for "${field}". Please check the value.`,
      technicalMessage: `Validation error in field: ${field} - ${message}`,
      recoveryAction: {
        type: 'fix_config',
        message: 'Fix configuration value',
        details: `Check and correct the value for: ${field}`
      },
      severity: 'medium',
      canContinue: false
    };
  }

  private addToHistory(error: ConfigError): void {
    this.configErrors.push(error);
    
    if (this.configErrors.length > this.MAX_ERROR_HISTORY) {
      this.configErrors.shift();
    }
  }

  getConfigErrors(): ConfigError[] {
    return [...this.configErrors];
  }

  getRecentConfigErrors(timeWindowMs: number = 3600000): ConfigError[] {
    const cutoff = Date.now() - timeWindowMs;
    return this.configErrors.filter(error => error.timestamp > cutoff);
  }

  clearConfigErrors(): void {
    this.configErrors = [];
  }

  getConfigErrorStats(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsByField: Record<string, number>;
    recentErrors: number;
  } {
    const oneHourAgo = Date.now() - 3600000;
    const recentErrors = this.configErrors.filter(error => error.timestamp > oneHourAgo);

    const errorsByType: Record<string, number> = {};
    const errorsByField: Record<string, number> = {};

    for (const error of this.configErrors) {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      if (error.field) {
        errorsByField[error.field] = (errorsByField[error.field] || 0) + 1;
      }
    }

    return {
      totalErrors: this.configErrors.length,
      errorsByType,
      errorsByField,
      recentErrors: recentErrors.length
    };
  }

  generateConfigDiagnostics(): {
    hasErrors: boolean;
    criticalErrors: number;
    recommendations: string[];
  } {
    const recentErrors = this.getRecentConfigErrors();
    const criticalErrors = recentErrors.filter(error => 
      error.type === 'env_var_missing' || error.type === 'parse_error'
    );

    const recommendations: string[] = [];

    if (criticalErrors.some(e => e.field?.includes('CLIENT_ID'))) {
      recommendations.push('Set up OAuth client IDs for GitHub and Figma');
    }

    if (criticalErrors.some(e => e.type === 'parse_error')) {
      recommendations.push('Fix JSON syntax errors in configuration file');
    }

    if (recentErrors.some(e => e.field?.includes('endpoints'))) {
      recommendations.push('Verify service endpoint URLs are correct');
    }

    if (recentErrors.length > 5) {
      recommendations.push('Consider resetting to default configuration');
    }

    return {
      hasErrors: recentErrors.length > 0,
      criticalErrors: criticalErrors.length,
      recommendations
    };
  }
}
