export interface OAuthError {
  code: string;
  message: string;
  description?: string;
  uri?: string;
  serviceId: string;
  timestamp: number;
}

export interface ErrorRecoveryAction {
  type: 'retry' | 'restart_flow' | 'contact_support' | 'check_config' | 'wait_and_retry';
  message: string;
  autoExecute?: boolean;
  delay?: number;
}

export interface ErrorHandlingResult {
  userMessage: string;
  technicalMessage: string;
  recoveryAction: ErrorRecoveryAction;
  shouldLog: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class OAuthErrorHandler {
  private errorHistory: OAuthError[] = [];
  private readonly MAX_HISTORY_SIZE = 100;

  handleOAuthError(
    error: string,
    serviceId: string,
    description?: string,
    uri?: string
  ): ErrorHandlingResult {
    const oauthError: OAuthError = {
      code: error,
      message: this.getErrorMessage(error),
      description,
      uri,
      serviceId,
      timestamp: Date.now()
    };

    // Add to error history
    this.addToHistory(oauthError);

    // Determine error handling strategy
    return this.determineErrorHandling(oauthError);
  }

  private determineErrorHandling(error: OAuthError): ErrorHandlingResult {
    switch (error.code) {
      case 'access_denied':
        return {
          userMessage: 'You chose not to authorize the application. You can try connecting again if you change your mind.',
          technicalMessage: `User denied OAuth access for ${error.serviceId}`,
          recoveryAction: {
            type: 'restart_flow',
            message: 'Click "Connect" to try again'
          },
          shouldLog: false,
          severity: 'low'
        };

      case 'invalid_client':
        return {
          userMessage: 'There\'s a configuration issue with the application. Please contact support.',
          technicalMessage: `Invalid OAuth client configuration for ${error.serviceId}`,
          recoveryAction: {
            type: 'contact_support',
            message: 'Contact support for assistance'
          },
          shouldLog: true,
          severity: 'high'
        };

      case 'invalid_grant':
      case 'invalid_request':
        return {
          userMessage: 'The authentication request was invalid. Please try connecting again.',
          technicalMessage: `Invalid OAuth grant/request for ${error.serviceId}: ${error.code}`,
          recoveryAction: {
            type: 'restart_flow',
            message: 'Try connecting again'
          },
          shouldLog: true,
          severity: 'medium'
        };

      case 'server_error':
        return {
          userMessage: `${this.getServiceDisplayName(error.serviceId)} is experiencing technical difficulties. Please try again in a few minutes.`,
          technicalMessage: `OAuth server error for ${error.serviceId}`,
          recoveryAction: {
            type: 'wait_and_retry',
            message: 'Try again in a few minutes',
            delay: 300000 // 5 minutes
          },
          shouldLog: true,
          severity: 'medium'
        };

      case 'temporarily_unavailable':
        return {
          userMessage: `${this.getServiceDisplayName(error.serviceId)} authentication is temporarily unavailable. Please try again later.`,
          technicalMessage: `OAuth service temporarily unavailable for ${error.serviceId}`,
          recoveryAction: {
            type: 'wait_and_retry',
            message: 'Try again later',
            delay: 600000 // 10 minutes
          },
          shouldLog: true,
          severity: 'medium'
        };

      case 'invalid_scope':
        return {
          userMessage: 'The requested permissions are not available. Please contact support.',
          technicalMessage: `Invalid OAuth scope for ${error.serviceId}`,
          recoveryAction: {
            type: 'contact_support',
            message: 'Contact support for assistance'
          },
          shouldLog: true,
          severity: 'high'
        };

      case 'unauthorized_client':
        return {
          userMessage: 'This application is not authorized to connect. Please contact support.',
          technicalMessage: `Unauthorized OAuth client for ${error.serviceId}`,
          recoveryAction: {
            type: 'contact_support',
            message: 'Contact support for assistance'
          },
          shouldLog: true,
          severity: 'critical'
        };

      default:
        return {
          userMessage: `Authentication failed: ${error.message}. Please try again.`,
          technicalMessage: `Unknown OAuth error for ${error.serviceId}: ${error.code}`,
          recoveryAction: {
            type: 'restart_flow',
            message: 'Try connecting again'
          },
          shouldLog: true,
          severity: 'medium'
        };
    }
  }

  private getErrorMessage(errorCode: string): string {
    const errorMessages: Record<string, string> = {
      'access_denied': 'Access was denied by the user',
      'invalid_request': 'The request is missing a required parameter or is otherwise malformed',
      'invalid_client': 'Client authentication failed',
      'invalid_grant': 'The provided authorization grant is invalid',
      'unauthorized_client': 'The client is not authorized to request an authorization code',
      'unsupported_grant_type': 'The authorization grant type is not supported',
      'invalid_scope': 'The requested scope is invalid or unknown',
      'server_error': 'The authorization server encountered an unexpected condition',
      'temporarily_unavailable': 'The authorization server is currently unable to handle the request'
    };

    return errorMessages[errorCode] || `Unknown OAuth error: ${errorCode}`;
  }

  private getServiceDisplayName(serviceId: string): string {
    const serviceNames: Record<string, string> = {
      'github': 'GitHub',
      'figma': 'Figma'
    };

    return serviceNames[serviceId] || serviceId;
  }

  private addToHistory(error: OAuthError): void {
    this.errorHistory.push(error);
    
    // Maintain history size limit
    if (this.errorHistory.length > this.MAX_HISTORY_SIZE) {
      this.errorHistory.shift();
    }
  }

  getErrorHistory(serviceId?: string): OAuthError[] {
    if (serviceId) {
      return this.errorHistory.filter(error => error.serviceId === serviceId);
    }
    return [...this.errorHistory];
  }

  getRecentErrors(serviceId: string, timeWindowMs: number = 3600000): OAuthError[] {
    const cutoff = Date.now() - timeWindowMs;
    return this.errorHistory.filter(
      error => error.serviceId === serviceId && error.timestamp > cutoff
    );
  }

  isRepeatedError(serviceId: string, errorCode: string, timeWindowMs: number = 300000): boolean {
    const recentErrors = this.getRecentErrors(serviceId, timeWindowMs);
    return recentErrors.filter(error => error.code === errorCode).length > 1;
  }

  shouldThrottleRetry(serviceId: string, maxAttempts: number = 3, timeWindowMs: number = 300000): boolean {
    const recentErrors = this.getRecentErrors(serviceId, timeWindowMs);
    return recentErrors.length >= maxAttempts;
  }

  clearErrorHistory(serviceId?: string): void {
    if (serviceId) {
      this.errorHistory = this.errorHistory.filter(error => error.serviceId !== serviceId);
    } else {
      this.errorHistory = [];
    }
  }

  getErrorStats(): {
    totalErrors: number;
    errorsByService: Record<string, number>;
    errorsByCode: Record<string, number>;
    recentErrors: number;
  } {
    const oneHourAgo = Date.now() - 3600000;
    const recentErrors = this.errorHistory.filter(error => error.timestamp > oneHourAgo);

    const errorsByService: Record<string, number> = {};
    const errorsByCode: Record<string, number> = {};

    for (const error of this.errorHistory) {
      errorsByService[error.serviceId] = (errorsByService[error.serviceId] || 0) + 1;
      errorsByCode[error.code] = (errorsByCode[error.code] || 0) + 1;
    }

    return {
      totalErrors: this.errorHistory.length,
      errorsByService,
      errorsByCode,
      recentErrors: recentErrors.length
    };
  }
}
