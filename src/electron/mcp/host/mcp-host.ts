import path from 'path';
import os from 'os';
import { AIN_INDEX_DIR } from '../../constants.js';
import { MCPConfigurationManager } from '../config/mcp-configuration-manager.js';
import { ServiceRegistry } from '../registry/service-registry.js';
import { DiscoveryCache } from '../discovery/discovery-cache.js';
import { ToolExecutionEngine } from '../execution/tool-execution-engine.js';

export interface MCPHostConfig {
  configPath?: string;
  enableDiscovery?: boolean;
  enableMetrics?: boolean;
}

export interface MCPHostStats {
  uptime: number;
  configPath: string;
  discoveryEnabled: boolean;
  serviceStats: any;
  cacheStats: any;
}

export class MCPHost {
  private configManager: MCPConfigurationManager;
  private serviceRegistry: ServiceRegistry;
  private discoveryCache: DiscoveryCache;
  private toolExecutionEngine: ToolExecutionEngine;
  private initialized = false;
  private startTime: number = 0;
  private config: MCPHostConfig;

  constructor(config: MCPHostConfig = {}) {
    this.config = {
      configPath: config.configPath || path.join(os.homedir(), AIN_INDEX_DIR, 'mcp-config.json'),
      enableDiscovery: config.enableDiscovery ?? true,
      enableMetrics: config.enableMetrics ?? true
    };

    this.configManager = new MCPConfigurationManager(this.config.configPath);
    this.serviceRegistry = ServiceRegistry.getInstance(this.configManager);
    this.discoveryCache = new DiscoveryCache();
    this.toolExecutionEngine = new ToolExecutionEngine(this.serviceRegistry);
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('MCP Host already initialized');
      return;
    }

    console.log('Initializing MCP Host...');
    this.startTime = Date.now();

    try {
      // Load configuration
      await this.configManager.loadConfiguration();
      console.log('✅ MCP configuration loaded');

      // Initialize service registry from configuration
      await this.serviceRegistry.initializeFromConfiguration();
      console.log('✅ Service registry initialized');

      this.initialized = true;
      console.log('✅ MCP Host initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize MCP Host:', error);
      throw error;
    }
  }

  async authenticateService(serviceId: string): Promise<string> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    console.log(`Authenticating service: ${serviceId}`);
    return await this.serviceRegistry.authenticateService(serviceId);
  }

  async handleOAuthCallback(url: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    console.log(`Handling OAuth callback: ${url}`);
    await this.serviceRegistry.handleOAuthCallback(url);
  }

  async initializeAuthenticatedServices(): Promise<void> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    console.log('🔄 Initializing authenticated services...');
    await this.serviceRegistry.initializeAuthenticatedServices();
  }

  async executeTool(serviceId: string, toolName: string, parameters: any): Promise<any> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    console.log(`Executing tool ${toolName} on service ${serviceId}`);
    const result = await this.toolExecutionEngine.executeTool({
      serviceId,
      toolName,
      parameters
    });

    if (!result.success) {
      throw new Error(result.error || 'Tool execution failed');
    }

    return result.result;
  }

  async getServiceStatus(serviceId: string): Promise<any> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    const service = this.serviceRegistry.getService(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    const authStatus = await service.getAuthStatus();
    const config = this.configManager.getServerConfiguration(serviceId);

    return {
      serviceId: service.serviceId,
      displayName: service.displayName,
      enabled: config?.enabled ?? false,
      authenticated: authStatus.authenticated,
      healthy: service.isHealthy(),
      authStatus,
      availableTools: service.getAvailableTools(),
      endpoints: service.endpoints
    };
  }

  async getAllServicesStatus(): Promise<any[]> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    const services = this.serviceRegistry.getAllServices();
    const statusPromises = services.map(service => this.getServiceStatus(service.serviceId));
    return await Promise.all(statusPromises);
  }

  async enableService(serviceId: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    await this.serviceRegistry.enableService(serviceId);
  }

  async disableService(serviceId: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    await this.serviceRegistry.disableService(serviceId);
  }

  async reloadConfiguration(): Promise<void> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    console.log('Reloading MCP configuration...');

    try {
      // Close all existing services
      await this.serviceRegistry.closeAllServices();

      // Reload configuration
      await this.configManager.reloadConfiguration();

      // Reinitialize services
      await this.serviceRegistry.initializeFromConfiguration();

      console.log('✅ MCP configuration reloaded successfully');
    } catch (error) {
      console.error('❌ Failed to reload MCP configuration:', error);
      throw error;
    }
  }

  async getHostStats(): Promise<MCPHostStats> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    const serviceStats = await this.serviceRegistry.getServiceStats();
    const cacheStats = this.discoveryCache.getStats();

    return {
      uptime: Date.now() - this.startTime,
      configPath: this.config.configPath!,
      discoveryEnabled: this.config.enableDiscovery!,
      serviceStats,
      cacheStats
    };
  }

  async getToolExecutionMetrics(timeWindowMs?: number): Promise<any> {
    if (!this.initialized) {
      throw new Error('MCP Host not initialized');
    }

    return this.toolExecutionEngine.getExecutionMetrics(timeWindowMs);
  }

  async performHealthCheck(): Promise<{
    healthy: boolean;
    services: Array<{
      serviceId: string;
      healthy: boolean;
      authenticated: boolean;
      error?: string;
    }>;
  }> {
    if (!this.initialized) {
      return {
        healthy: false,
        services: []
      };
    }

    const services = this.serviceRegistry.getAllServices();
    const healthChecks = await Promise.all(
      services.map(async (service) => {
        try {
          const authStatus = await service.getAuthStatus();
          return {
            serviceId: service.serviceId,
            healthy: service.isHealthy(),
            authenticated: authStatus.authenticated,
            error: authStatus.error
          };
        } catch (error) {
          return {
            serviceId: service.serviceId,
            healthy: false,
            authenticated: false,
            error: error instanceof Error ? error.message : String(error)
          };
        }
      })
    );

    const allHealthy = healthChecks.every(check => check.healthy);

    return {
      healthy: allHealthy,
      services: healthChecks
    };
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  getConfigurationManager(): MCPConfigurationManager {
    return this.configManager;
  }

  getServiceRegistry(): ServiceRegistry {
    return this.serviceRegistry;
  }

  async shutdown(): Promise<void> {
    console.log('Shutting down MCP Host...');

    try {
      if (this.serviceRegistry) {
        await this.serviceRegistry.closeAllServices();
      }

      this.initialized = false;
      console.log('✅ MCP Host shutdown completed');
    } catch (error) {
      console.error('❌ Error during MCP Host shutdown:', error);
      throw error;
    }
  }
}
