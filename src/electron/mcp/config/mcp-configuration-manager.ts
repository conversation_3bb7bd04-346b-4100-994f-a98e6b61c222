import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import AjvModule from 'ajv';
import addFormatsModule from 'ajv-formats';
import type { ErrorObject } from 'ajv';
import { AIN_INDEX_DIR } from '../../constants.js';
import { OAuthDiscoveryClient, AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';
import {
  MCPConfiguration,
  ServerConfiguration,
  MCPConfigurationValidationError,
  MCPConfigurationValidationResult,
  DEFAULT_MCP_CONFIGURATION_WITH_SERVICES,
  MCP_CONFIGURATION_SCHEMA
} from './mcp-config-types.js';

export class MCPConfigurationManager {
  private config: MCPConfiguration;
  private configPath: string;
  private discoveryClient: OAuthDiscoveryClient;
  private ajv: any;

  constructor(configPath?: string) {
    this.configPath = configPath || path.join(os.homedir(), AIN_INDEX_DIR, 'mcp-config.json');
    this.discoveryClient = new OAuthDiscoveryClient();
    
    // Initialize JSON schema validator
    this.ajv = new (AjvModule as any)({ allErrors: true });
    (addFormatsModule as any)(this.ajv);
    this.ajv.addSchema(MCP_CONFIGURATION_SCHEMA, 'mcp-config');
    
    this.config = DEFAULT_MCP_CONFIGURATION_WITH_SERVICES;
  }

  async loadConfiguration(configPath?: string): Promise<void> {
    if (configPath) {
      this.configPath = configPath;
    }

    try {
      if (await fs.pathExists(this.configPath)) {
        console.log(`Loading MCP configuration from ${this.configPath}`);
        const configContent = await fs.readFile(this.configPath, 'utf-8');
        const rawConfig = JSON.parse(configContent);

        // Substitute environment variables
        this.config = this.substituteEnvironmentVariables(rawConfig);

        console.log(`📋 Loaded configuration with servers: ${Object.keys(this.config.servers).join(', ')}`);

        // Validate configuration
        const validation = this.validateConfiguration(this.config);
        if (!validation.valid) {
          throw new Error(`Configuration validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
        }

        console.log('MCP configuration loaded and validated successfully');
      } else {
        console.log('No existing MCP configuration found, using default configuration');
        this.config = DEFAULT_MCP_CONFIGURATION_WITH_SERVICES;
        await this.saveConfiguration();
      }

      // Perform discovery for enabled servers
      if (this.config.global.discovery.enabled) {
        await this.performDiscovery();
      }
    } catch (error) {
      console.error('Failed to load MCP configuration:', error);
      throw error;
    }
  }

  async saveConfiguration(config?: MCPConfiguration): Promise<void> {
    if (config) {
      const validation = this.validateConfiguration(config);
      if (!validation.valid) {
        throw new Error(`Configuration validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }
      this.config = config;
    }

    try {
      await fs.ensureDir(path.dirname(this.configPath));
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
      console.log(`MCP configuration saved to ${this.configPath}`);
    } catch (error) {
      console.error('Failed to save MCP configuration:', error);
      throw error;
    }
  }

  getConfiguration(): MCPConfiguration {
    return { ...this.config };
  }

  getServerConfiguration(serverId: string): ServerConfiguration | undefined {
    return this.config.servers[serverId];
  }

  getEnabledServers(): string[] {
    const allServers = Object.keys(this.config.servers);
    const enabledServers = Object.entries(this.config.servers)
      .filter(([_, config]) => config.enabled)
      .map(([serverId, _]) => serverId);

    console.log(`📊 Server configuration summary:`);
    console.log(`   All servers: ${allServers.join(', ')}`);
    console.log(`   Enabled servers: ${enabledServers.join(', ')}`);

    return enabledServers;
  }

  async updateServerConfiguration(serverId: string, serverConfig: ServerConfiguration): Promise<void> {
    this.config.servers[serverId] = serverConfig;
    await this.saveConfiguration();
  }

  async enableServer(serverId: string): Promise<void> {
    const serverConfig = this.config.servers[serverId];
    if (serverConfig) {
      serverConfig.enabled = true;
      await this.saveConfiguration();
      console.log(`Enabled MCP server: ${serverId}`);
    }
  }

  async disableServer(serverId: string): Promise<void> {
    const serverConfig = this.config.servers[serverId];
    if (serverConfig) {
      serverConfig.enabled = false;
      await this.saveConfiguration();
      console.log(`Disabled MCP server: ${serverId}`);
    }
  }

  validateConfiguration(config: MCPConfiguration): MCPConfigurationValidationResult {
    const validate = this.ajv.getSchema('mcp-config');
    if (!validate) {
      return {
        valid: false,
        errors: [{ field: 'schema', message: 'Schema not found' }]
      };
    }

    const valid = validate(config);
    if (valid) {
      return { valid: true, errors: [] };
    }

    const errors: MCPConfigurationValidationError[] = (validate.errors || []).map((error: ErrorObject) => ({
      field: error.instancePath || error.schemaPath,
      message: error.message || 'Validation error',
      value: error.data
    }));

    return { valid: false, errors };
  }

  private substituteEnvironmentVariables(config: any): MCPConfiguration {
    const configStr = JSON.stringify(config);
    const substituted = configStr.replace(/\$\{ENV:([^}]+)\}/g, (match, envVar) => {
      const value = process.env[envVar];
      if (!value) {
        console.warn(`Environment variable not found: ${envVar}, using placeholder`);
        return match; // Keep the placeholder if env var not found
      }
      return value;
    });

    return JSON.parse(substituted);
  }

  private async performDiscovery(): Promise<void> {
    console.log('Performing OAuth discovery for enabled servers...');
    
    for (const [serverId, serverConfig] of Object.entries(this.config.servers)) {
      if (!serverConfig.enabled) {
        continue;
      }

      // Skip discovery if no discovery endpoint is configured
      if (!serverConfig.endpoints?.discovery) {
        console.log(`📋 Using static OAuth configuration for ${serverId} (no discovery endpoint)`);
        continue;
      }

      try {
        console.log(`Discovering OAuth metadata for ${serverId}...`);
        console.log(`Discovering OAuth server metadata from ${serverConfig.endpoints.discovery}`);
        const metadata = await this.discoveryClient.discoverAuthorizationServer(
          serverConfig.endpoints.discovery
        );

        // Validate discovered metadata against configuration
        this.validateDiscoveredMetadata(serverId, serverConfig, metadata);

        console.log(`✅ Discovery successful for ${serverId}`);
      } catch (error) {
        console.warn(`⚠️ Discovery failed for ${serverId}:`, error instanceof Error ? error.message : error);
        console.log(`Continuing with static configuration for ${serverId}`);
      }
    }
  }

  private validateDiscoveredMetadata(
    serverId: string,
    config: ServerConfiguration,
    metadata: AuthorizationServerMetadata
  ): void {
    // Validate scopes
    const unsupportedScopes = config.auth.scopes.filter(
      scope => !metadata.scopes_supported.includes(scope)
    );

    if (unsupportedScopes.length > 0) {
      console.warn(`Unsupported scopes for ${serverId}: ${unsupportedScopes.join(', ')}`);
    }

    // Validate PKCE requirement
    if (config.auth.pkce && !metadata.code_challenge_methods_supported?.includes('S256')) {
      throw new Error(`PKCE S256 required for ${serverId} but not supported by server`);
    }

    // Validate redirect URI scheme
    const redirectUri = new URL(config.auth.redirect_uri);
    if (!this.config.global.security.allowed_redirect_schemes.includes(redirectUri.protocol.slice(0, -1))) {
      throw new Error(`Redirect URI scheme not allowed for ${serverId}: ${redirectUri.protocol}`);
    }

    console.log(`✅ Discovered metadata validation passed for ${serverId}`);
  }

  async reloadConfiguration(): Promise<void> {
    await this.loadConfiguration();
  }

  getDiscoveryClient(): OAuthDiscoveryClient {
    return this.discoveryClient;
  }
}
