/**
 * Test script to validate MCP configuration validation logic
 */

import { MCPConfigurationManager } from '../config/mcp-configuration-manager.js';
import { MCPConfiguration } from '../config/mcp-config-types.js';

class ConfigValidationTest {
  private configManager: MCPConfigurationManager;

  constructor() {
    this.configManager = new MCPConfigurationManager();
  }

  async runTests(): Promise<void> {
    console.log('🧪 Testing MCP Configuration Validation...\n');

    await this.testValidStdioConfiguration();
    await this.testValidHttpSseConfiguration();
    await this.testInvalidStdioConfiguration();
    await this.testInvalidHttpSseConfiguration();
    await this.testMixedConfiguration();

    console.log('\n✅ All configuration validation tests completed!');
  }

  private async testValidStdioConfiguration(): Promise<void> {
    console.log('📋 Testing valid stdio configuration...');

    const config: MCPConfiguration = {
      version: "1.0",
      servers: {
        figma: {
          transport: "stdio",
          enabled: true,
          stdio: {
            executable: "node",
            args: ["figma-mcp-server.js"]
          },
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        }
      },
      global: {
        discovery: {
          enabled: false,
          timeout: 10000,
          cache_duration: 3600000
        },
        security: {
          validate_certificates: true,
          require_pkce: true,
          allowed_redirect_schemes: ["test"]
        }
      }
    };

    const result = this.configManager.validateConfiguration(config);
    if (result.valid) {
      console.log('   ✅ Valid stdio configuration passed validation');
    } else {
      console.log('   ❌ Valid stdio configuration failed validation:', result.errors);
    }
  }

  private async testValidHttpSseConfiguration(): Promise<void> {
    console.log('📋 Testing valid HTTP/SSE configuration...');

    const config: MCPConfiguration = {
      version: "1.0",
      servers: {
        github: {
          transport: "http-sse",
          enabled: true,
          endpoints: {
            http: "https://api.github.com/mcp",
            sse: "https://api.github.com/mcp/events"
          },
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        }
      },
      global: {
        discovery: {
          enabled: false,
          timeout: 10000,
          cache_duration: 3600000
        },
        security: {
          validate_certificates: true,
          require_pkce: true,
          allowed_redirect_schemes: ["test"]
        }
      }
    };

    const result = this.configManager.validateConfiguration(config);
    if (result.valid) {
      console.log('   ✅ Valid HTTP/SSE configuration passed validation');
    } else {
      console.log('   ❌ Valid HTTP/SSE configuration failed validation:', result.errors);
    }
  }

  private async testInvalidStdioConfiguration(): Promise<void> {
    console.log('📋 Testing invalid stdio configuration (missing stdio block)...');

    const config: MCPConfiguration = {
      version: "1.0",
      servers: {
        figma: {
          transport: "stdio",
          enabled: true,
          // Missing stdio configuration
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        }
      },
      global: {
        discovery: {
          enabled: false,
          timeout: 10000,
          cache_duration: 3600000
        },
        security: {
          validate_certificates: true,
          require_pkce: true,
          allowed_redirect_schemes: ["test"]
        }
      }
    } as MCPConfiguration;

    const result = this.configManager.validateConfiguration(config);
    if (!result.valid) {
      console.log('   ✅ Invalid stdio configuration correctly failed validation');
      console.log('   📝 Errors:', result.errors.map(e => e.message).join(', '));
    } else {
      console.log('   ❌ Invalid stdio configuration incorrectly passed validation');
    }
  }

  private async testInvalidHttpSseConfiguration(): Promise<void> {
    console.log('📋 Testing invalid HTTP/SSE configuration (missing endpoints)...');

    const config: MCPConfiguration = {
      version: "1.0",
      servers: {
        github: {
          transport: "http-sse",
          enabled: true,
          // Missing endpoints configuration
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        }
      },
      global: {
        discovery: {
          enabled: false,
          timeout: 10000,
          cache_duration: 3600000
        },
        security: {
          validate_certificates: true,
          require_pkce: true,
          allowed_redirect_schemes: ["test"]
        }
      }
    } as MCPConfiguration;

    const result = this.configManager.validateConfiguration(config);
    if (!result.valid) {
      console.log('   ✅ Invalid HTTP/SSE configuration correctly failed validation');
      console.log('   📝 Errors:', result.errors.map(e => e.message).join(', '));
    } else {
      console.log('   ❌ Invalid HTTP/SSE configuration incorrectly passed validation');
    }
  }

  private async testMixedConfiguration(): Promise<void> {
    console.log('📋 Testing mixed configuration (stdio + HTTP/SSE)...');

    const config: MCPConfiguration = {
      version: "1.0",
      servers: {
        figma: {
          transport: "stdio",
          enabled: true,
          stdio: {
            executable: "node",
            args: ["figma-mcp-server.js"]
          },
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        },
        github: {
          transport: "http-sse",
          enabled: true,
          endpoints: {
            http: "https://api.github.com/mcp",
            sse: "https://api.github.com/mcp/events"
          },
          auth: {
            type: "oauth",
            client_id: "test_client_id",
            redirect_uri: "test://callback",
            scopes: ["read"],
            pkce: true
          },
          capabilities: ["tools"],
          tools: [],
          connection: {
            timeout: 30000,
            retry_attempts: 3,
            retry_delay: 1000
          },
          cache: {
            enabled: true,
            ttl: 300000
          }
        }
      },
      global: {
        discovery: {
          enabled: false,
          timeout: 10000,
          cache_duration: 3600000
        },
        security: {
          validate_certificates: true,
          require_pkce: true,
          allowed_redirect_schemes: ["test"]
        }
      }
    };

    const result = this.configManager.validateConfiguration(config);
    if (result.valid) {
      console.log('   ✅ Mixed configuration passed validation');
    } else {
      console.log('   ❌ Mixed configuration failed validation:', result.errors);
    }
  }
}

// Export for use in other test files
export { ConfigValidationTest };

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new ConfigValidationTest();
  test.runTests().catch(console.error);
}
