/**
 * Test script to validate Figma local MCP integration
 * This script tests the transition from HTTP/SSE to stdio transport
 */

import { FigmaLocalService } from '../clients/figma-local-service.js';
import { MCPConfigurationManager } from '../config/mcp-configuration-manager.js';
import { ServiceRegistry } from '../registry/service-registry.js';
import { FIGMA_SERVER_CONFIG } from '../config/mcp-config-types.js';

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class FigmaLocalIntegrationTest {
  private configManager: MCPConfigurationManager;
  private serviceRegistry: ServiceRegistry;
  private testResults: TestResult[] = [];

  constructor() {
    this.configManager = new MCPConfigurationManager();
    this.serviceRegistry = ServiceRegistry.getInstance(this.configManager);
  }

  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Figma Local MCP Integration Tests...\n');

    await this.testConfigurationLoading();
    await this.testServiceRegistryCreation();
    await this.testFigmaLocalServiceInstantiation();
    await this.testOAuthConfigurationPreservation();
    await this.testStdioTransportConfiguration();
    await this.testProcessLifecycleManagement();
    await this.testHealthMonitoring();
    await this.testErrorHandlingAndRestart();

    this.printTestSummary();
    return this.testResults;
  }

  private async testConfigurationLoading(): Promise<void> {
    try {
      console.log('📋 Testing configuration loading...');

      // Test that stdio configuration is properly loaded
      const config = FIGMA_SERVER_CONFIG;

      const passed =
        config.transport === 'stdio' &&
        config.stdio?.executable === 'node' &&
        config.stdio?.args?.includes('figma-mcp-server.js') &&
        config.auth.type === 'oauth' &&
        config.auth.pkce === true;

      const details = {
        transport: config.transport,
        executable: config.stdio?.executable,
        args: config.stdio?.args,
        authType: config.auth.type,
        pkce: config.auth.pkce
      };

      if (passed) {
        this.addTestResult('Configuration Loading', true, undefined, details);
      } else {
        this.addTestResult('Configuration Loading', false, 'Configuration validation failed', details);
      }

    } catch (error) {
      this.addTestResult('Configuration Loading', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testServiceRegistryCreation(): Promise<void> {
    try {
      console.log('🏭 Testing service registry creation...');
      
      // Test that service registry can create local services
      const config = FIGMA_SERVER_CONFIG;
      const service = (this.serviceRegistry as any).createServiceInstance('figma', config);
      
      const passed = 
        service instanceof FigmaLocalService &&
        service.serviceId === 'figma' &&
        service.displayName === 'Figma';

      if (passed) {
        this.addTestResult('Service Registry Creation', true, undefined, {
          serviceType: service.constructor.name,
          serviceId: service.serviceId,
          displayName: service.displayName
        });
      } else {
        this.addTestResult('Service Registry Creation', false, 'Service creation validation failed', {
          serviceType: service.constructor.name,
          serviceId: service.serviceId,
          displayName: service.displayName
        });
      }

    } catch (error) {
      this.addTestResult('Service Registry Creation', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testFigmaLocalServiceInstantiation(): Promise<void> {
    try {
      console.log('🎨 Testing Figma local service instantiation...');
      
      const config = FIGMA_SERVER_CONFIG;
      const service = new FigmaLocalService(config);
      
      const passed = 
        service.serviceId === 'figma' &&
        service.displayName === 'Figma';

      if (passed) {
        this.addTestResult('Figma Local Service Instantiation', true, undefined, {
          serviceId: service.serviceId,
          displayName: service.displayName
        });
      } else {
        this.addTestResult('Figma Local Service Instantiation', false, 'Service instantiation validation failed', {
          serviceId: service.serviceId,
          displayName: service.displayName
        });
      }

    } catch (error) {
      this.addTestResult('Figma Local Service Instantiation', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testOAuthConfigurationPreservation(): Promise<void> {
    try {
      console.log('🔐 Testing OAuth configuration preservation...');
      
      const config = FIGMA_SERVER_CONFIG;
      const service = new FigmaLocalService(config);
      
      // Check that OAuth configuration is preserved
      const authStatus = await service.getAuthStatus();
      
      const passed = 
        config.auth.type === 'oauth' &&
        config.auth.client_id !== undefined &&
        config.auth.redirect_uri.includes('figma') &&
        config.auth.scopes.length > 0 &&
        config.auth.pkce === true;

      const details = {
        authType: config.auth.type,
        hasClientId: !!config.auth.client_id,
        redirectUri: config.auth.redirect_uri,
        scopesCount: config.auth.scopes.length,
        pkce: config.auth.pkce,
        authStatus: authStatus.authenticated
      };

      if (passed) {
        this.addTestResult('OAuth Configuration Preservation', true, undefined, details);
      } else {
        this.addTestResult('OAuth Configuration Preservation', false, 'OAuth configuration validation failed', details);
      }

    } catch (error) {
      this.addTestResult('OAuth Configuration Preservation', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testStdioTransportConfiguration(): Promise<void> {
    try {
      console.log('📡 Testing stdio transport configuration...');
      
      const config = FIGMA_SERVER_CONFIG;
      
      const passed = 
        config.transport === 'stdio' &&
        config.stdio?.executable !== undefined &&
        config.stdio?.restart_delay !== undefined &&
        config.stdio?.max_restarts !== undefined;

      const details = {
        transport: config.transport,
        executable: config.stdio?.executable,
        restartDelay: config.stdio?.restart_delay,
        maxRestarts: config.stdio?.max_restarts
      };

      if (passed) {
        this.addTestResult('Stdio Transport Configuration', true, undefined, details);
      } else {
        this.addTestResult('Stdio Transport Configuration', false, 'Stdio transport configuration validation failed', details);
      }

    } catch (error) {
      this.addTestResult('Stdio Transport Configuration', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testProcessLifecycleManagement(): Promise<void> {
    try {
      console.log('🔄 Testing process lifecycle management...');
      
      // This is a mock test since we can't actually start the MCP server without it being available
      const config = FIGMA_SERVER_CONFIG;
      const service = new FigmaLocalService(config);
      
      // Test that the service has the necessary lifecycle methods
      const hasLifecycleMethods = 
        typeof service.initialize === 'function' &&
        typeof service.close === 'function' &&
        typeof service.isHealthy === 'function';

      const details = {
        hasInitialize: typeof service.initialize === 'function',
        hasClose: typeof service.close === 'function',
        hasIsHealthy: typeof service.isHealthy === 'function'
      };

      if (hasLifecycleMethods) {
        this.addTestResult('Process Lifecycle Management', true, undefined, details);
      } else {
        this.addTestResult('Process Lifecycle Management', false, 'Lifecycle methods validation failed', details);
      }

    } catch (error) {
      this.addTestResult('Process Lifecycle Management', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testHealthMonitoring(): Promise<void> {
    try {
      console.log('💓 Testing health monitoring...');
      
      const config = FIGMA_SERVER_CONFIG;
      const service = new FigmaLocalService(config);
      
      // Test that health monitoring methods exist
      const hasHealthMethods = 
        typeof service.isHealthy === 'function' &&
        typeof (service as any).getHealthMetrics === 'function';

      const details = {
        hasIsHealthy: typeof service.isHealthy === 'function',
        hasGetHealthMetrics: typeof (service as any).getHealthMetrics === 'function'
      };

      if (hasHealthMethods) {
        this.addTestResult('Health Monitoring', true, undefined, details);
      } else {
        this.addTestResult('Health Monitoring', false, 'Health monitoring methods validation failed', details);
      }

    } catch (error) {
      this.addTestResult('Health Monitoring', false, error instanceof Error ? error.message : String(error));
    }
  }

  private async testErrorHandlingAndRestart(): Promise<void> {
    try {
      console.log('🔧 Testing error handling and restart logic...');
      
      const config = FIGMA_SERVER_CONFIG;
      
      // Test restart configuration
      const hasRestartConfig = 
        config.stdio?.restart_delay !== undefined &&
        config.stdio?.max_restarts !== undefined &&
        config.stdio?.max_restarts > 0;

      const details = {
        restartDelay: config.stdio?.restart_delay,
        maxRestarts: config.stdio?.max_restarts
      };

      if (hasRestartConfig) {
        this.addTestResult('Error Handling and Restart', true, undefined, details);
      } else {
        this.addTestResult('Error Handling and Restart', false, 'Restart configuration validation failed', details);
      }

    } catch (error) {
      this.addTestResult('Error Handling and Restart', false, error instanceof Error ? error.message : String(error));
    }
  }

  private addTestResult(testName: string, passed: boolean, error?: string, details?: any): void {
    this.testResults.push({
      testName,
      passed,
      error,
      details
    });

    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`   ${status}: ${testName}`);
    if (error) {
      console.log(`      Error: ${error}`);
    }
    if (details) {
      console.log(`      Details:`, details);
    }
    console.log('');
  }

  private printTestSummary(): void {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('📊 Test Summary:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   - ${r.testName}: ${r.error}`);
        });
    }

    console.log('\n🎉 Figma Local MCP Integration Tests Complete!');
  }
}

// Export for use in other test files
export { FigmaLocalIntegrationTest, TestResult };

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new FigmaLocalIntegrationTest();
  test.runAllTests().catch(console.error);
}
