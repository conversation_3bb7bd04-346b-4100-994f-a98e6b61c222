import fetch from 'node-fetch';
import keytar from 'keytar';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export interface TokenExchangeRequest {
  serviceId: string;
  authorizationCode: string;
  state: string;
  codeVerifier: string;
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  tokenEndpoint: string;
}

export interface TokenExchangeResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
  scope?: string;
}

export interface TokenRefreshRequest {
  serviceId: string;
  refreshToken: string;
  clientId: string;
  clientSecret: string;
  tokenEndpoint: string;
}

export class TokenExchangeService {
  private readonly KEYTAR_SERVICE = 'alpine-app';

  async exchangeCodeForTokens(request: TokenExchangeRequest): Promise<TokenExchangeResponse> {
    console.log(`Exchanging authorization code for tokens for ${request.serviceId}`);

    try {
      // Validate state parameter
      const storedState = await keytar.getPassword(this.KEYTAR_SERVICE, `${request.serviceId}-oauth-state`);
      if (request.state !== storedState) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Prepare token exchange request
      const tokenParams = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: request.clientId,
        client_secret: request.clientSecret,
        code: request.authorizationCode,
        redirect_uri: request.redirectUri,
        code_verifier: request.codeVerifier
      });

      // Exchange code for tokens
      const response = await fetch(request.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        },
        body: tokenParams.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const tokenResponse = await response.json() as TokenExchangeResponse;

      // Store tokens securely
      await this.storeTokens(request.serviceId, tokenResponse);

      // Clean up OAuth state
      await this.cleanupOAuthState(request.serviceId);

      console.log(`✅ OAuth tokens obtained and stored for ${request.serviceId}`);
      return tokenResponse;

    } catch (error) {
      console.error(`❌ Token exchange failed for ${request.serviceId}:`, error);
      await this.cleanupOAuthState(request.serviceId);
      throw error;
    }
  }

  async refreshTokens(request: TokenRefreshRequest): Promise<TokenExchangeResponse> {
    console.log(`Refreshing tokens for ${request.serviceId}`);

    try {
      const refreshParams = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: request.clientId,
        client_secret: request.clientSecret,
        refresh_token: request.refreshToken
      });

      const response = await fetch(request.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        },
        body: refreshParams.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token refresh failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const tokenResponse = await response.json() as TokenExchangeResponse;

      // Store refreshed tokens
      await this.storeTokens(request.serviceId, tokenResponse);

      console.log(`✅ Tokens refreshed for ${request.serviceId}`);
      return tokenResponse;

    } catch (error) {
      console.error(`❌ Token refresh failed for ${request.serviceId}:`, error);
      throw error;
    }
  }

  async getStoredTokens(serviceId: string): Promise<{
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    tokenType?: string;
    scope?: string;
  }> {
    try {
      const accessToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${serviceId}-access-token`);
      const refreshToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${serviceId}-refresh-token`);
      const expiresAtStr = await keytar.getPassword(this.KEYTAR_SERVICE, `${serviceId}-token-expires-at`);
      const metadataStr = await keytar.getPassword(this.KEYTAR_SERVICE, `${serviceId}-token-metadata`);

      let tokenType: string | undefined;
      let scope: string | undefined;

      if (metadataStr) {
        try {
          const metadata = JSON.parse(metadataStr);
          tokenType = metadata.token_type;
          scope = metadata.scope;
        } catch (error) {
          console.warn(`Failed to parse token metadata for ${serviceId}:`, error);
        }
      }

      return {
        accessToken: accessToken || undefined,
        refreshToken: refreshToken || undefined,
        expiresAt: expiresAtStr ? parseInt(expiresAtStr) : undefined,
        tokenType,
        scope
      };
    } catch (error) {
      console.error(`Failed to get stored tokens for ${serviceId}:`, error);
      return {};
    }
  }

  async revokeTokens(serviceId: string): Promise<void> {
    console.log(`Revoking tokens for ${serviceId}`);

    try {
      // Remove all stored tokens
      await Promise.all([
        keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-access-token`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-refresh-token`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-token-expires-at`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-token-metadata`)
      ]);

      console.log(`✅ Tokens revoked for ${serviceId}`);
    } catch (error) {
      console.error(`❌ Failed to revoke tokens for ${serviceId}:`, error);
      throw error;
    }
  }

  async isTokenExpired(serviceId: string): Promise<boolean> {
    const tokens = await this.getStoredTokens(serviceId);
    if (!tokens.expiresAt) {
      return false; // No expiration info, assume valid
    }

    return Date.now() >= tokens.expiresAt;
  }

  async getValidAccessToken(serviceId: string): Promise<string | null> {
    const tokens = await this.getStoredTokens(serviceId);
    
    if (!tokens.accessToken) {
      return null;
    }

    // Check if token is expired
    if (await this.isTokenExpired(serviceId)) {
      if (tokens.refreshToken) {
        try {
          // Attempt to refresh the token
          const clientSecret = await keytar.getPassword(this.KEYTAR_SERVICE, `${serviceId}-client-secret`);
          if (!clientSecret) {
            throw new Error('Client secret not found');
          }

          // This would need the token endpoint - in practice, this should be provided
          // For now, we'll just return null to indicate re-authentication is needed
          console.warn(`Token expired for ${serviceId} and refresh not implemented in this context`);
          return null;
        } catch (error) {
          console.error(`Failed to refresh token for ${serviceId}:`, error);
          return null;
        }
      } else {
        console.warn(`Token expired for ${serviceId} and no refresh token available`);
        return null;
      }
    }

    return tokens.accessToken;
  }

  private async storeTokens(serviceId: string, tokenResponse: TokenExchangeResponse): Promise<void> {
    const promises: Promise<void>[] = [
      keytar.setPassword(this.KEYTAR_SERVICE, `${serviceId}-access-token`, tokenResponse.access_token)
    ];

    if (tokenResponse.refresh_token) {
      promises.push(
        keytar.setPassword(this.KEYTAR_SERVICE, `${serviceId}-refresh-token`, tokenResponse.refresh_token)
      );
    }

    if (tokenResponse.expires_in) {
      const expiresAt = Date.now() + (tokenResponse.expires_in * 1000);
      promises.push(
        keytar.setPassword(this.KEYTAR_SERVICE, `${serviceId}-token-expires-at`, expiresAt.toString())
      );
    }

    // Store token metadata
    const metadata = {
      token_type: tokenResponse.token_type,
      scope: tokenResponse.scope,
      issued_at: Date.now()
    };
    promises.push(
      keytar.setPassword(this.KEYTAR_SERVICE, `${serviceId}-token-metadata`, JSON.stringify(metadata))
    );

    await Promise.all(promises);
  }

  private async cleanupOAuthState(serviceId: string): Promise<void> {
    await Promise.all([
      keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-code-verifier`),
      keytar.deletePassword(this.KEYTAR_SERVICE, `${serviceId}-oauth-state`)
    ]);
  }
}
