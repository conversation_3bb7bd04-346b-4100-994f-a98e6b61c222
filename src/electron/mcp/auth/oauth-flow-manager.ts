import { OAuthService } from './oauth-service.js';
import { OAuthCallbackRouter } from './oauth-callback-router.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';

export interface OAuthFlowResult {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface OAuthFlowManager {
  initializeFlow(serviceId: string): Promise<OAuthFlowResult>;
  handleCallback(url: string): Promise<void>;
  getAuthStatus(serviceId: string): Promise<any>;
  revokeAccess(serviceId: string): Promise<void>;
}

export class DefaultOAuthFlowManager implements OAuthFlowManager {
  private oauthServices = new Map<string, OAuthService>();
  private callbackRouter: OAuthCallbackRouter;

  constructor() {
    this.callbackRouter = new OAuthCallbackRouter();
  }

  registerService(serviceId: string, config: ServerConfiguration): void {
    const oauthService = new OAuthService({
      serviceId,
      clientId: config.auth.client_id,
      authorizationEndpoint: '', // Will be set from discovery
      tokenEndpoint: '', // Will be set from discovery
      redirectUri: config.auth.redirect_uri,
      scopes: config.auth.scopes,
      pkce: config.auth.pkce
    });

    this.oauthServices.set(serviceId, oauthService);

    // Register callback handler
    this.callbackRouter.registerHandler({
      serviceId,
      callbackPath: `/auth-callback/${serviceId}`,
      handleCallback: async (params: URLSearchParams) => {
        const code = params.get('code');
        const state = params.get('state');

        if (!code || !state) {
          throw new Error('Missing required OAuth parameters');
        }

        await oauthService.exchangeCodeForTokens(code, state);
      }
    });

    console.log(`OAuth flow manager registered service: ${serviceId}`);
  }

  async initializeFlow(serviceId: string): Promise<OAuthFlowResult> {
    try {
      const oauthService = this.oauthServices.get(serviceId);
      if (!oauthService) {
        throw new Error(`OAuth service not found: ${serviceId}`);
      }

      console.log(`Initializing OAuth flow for ${serviceId}`);
      const authUrl = await oauthService.initializeOAuth();

      return {
        success: true,
        authUrl
      };
    } catch (error) {
      console.error(`Failed to initialize OAuth flow for ${serviceId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  async handleCallback(url: string): Promise<void> {
    console.log(`Handling OAuth callback: ${url}`);
    
    const result = await this.callbackRouter.routeCallback(url);
    if (!result.success) {
      throw new Error(result.error || 'OAuth callback failed');
    }

    console.log(`✅ OAuth callback handled successfully for ${result.serviceId}`);
  }

  async getAuthStatus(serviceId: string): Promise<any> {
    const oauthService = this.oauthServices.get(serviceId);
    if (!oauthService) {
      throw new Error(`OAuth service not found: ${serviceId}`);
    }

    return await oauthService.getAuthStatus();
  }

  async revokeAccess(serviceId: string): Promise<void> {
    const oauthService = this.oauthServices.get(serviceId);
    if (!oauthService) {
      throw new Error(`OAuth service not found: ${serviceId}`);
    }

    console.log(`Revoking OAuth access for ${serviceId}`);
    await oauthService.revokeAccess();
  }

  getRegisteredServices(): string[] {
    return Array.from(this.oauthServices.keys());
  }

  hasService(serviceId: string): boolean {
    return this.oauthServices.has(serviceId);
  }

  setDiscoveredMetadata(serviceId: string, metadata: any): void {
    const oauthService = this.oauthServices.get(serviceId);
    if (oauthService && 'setDiscoveredMetadata' in oauthService) {
      (oauthService as any).setDiscoveredMetadata(metadata);
      console.log(`Updated OAuth metadata for ${serviceId} from discovery`);
    }
  }

  async getAccessToken(serviceId: string): Promise<string> {
    const oauthService = this.oauthServices.get(serviceId);
    if (!oauthService) {
      throw new Error(`OAuth service not found: ${serviceId}`);
    }

    return await oauthService.getAccessToken();
  }

  unregisterService(serviceId: string): void {
    this.oauthServices.delete(serviceId);
    this.callbackRouter.unregisterHandler(serviceId);
    console.log(`OAuth flow manager unregistered service: ${serviceId}`);
  }

  getCallbackRouter(): OAuthCallbackRouter {
    return this.callbackRouter;
  }
}
