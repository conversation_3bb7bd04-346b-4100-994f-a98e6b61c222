export interface ParsedOAuthCallback {
  serviceId: string;
  code?: string;
  state?: string;
  error?: string;
  errorDescription?: string;
  errorUri?: string;
}

export interface CallbackValidationResult {
  valid: boolean;
  error?: string;
  parsed?: ParsedOAuthCallback;
}

export class CallbackUrlParser {
  private readonly EXPECTED_PROTOCOL = 'thealpinecode.alpineintellect:';
  private readonly CALLBACK_PATH_PATTERN = /^\/auth-callback\/([^\/]+)$/;

  parseCallback(url: string): CallbackValidationResult {
    try {
      console.log(`Parsing OAuth callback URL: ${url}`);

      // Parse the URL
      const parsedUrl = new URL(url);

      // Validate protocol
      if (parsedUrl.protocol !== this.EXPECTED_PROTOCOL) {
        return {
          valid: false,
          error: `Invalid protocol: expected ${this.EXPECTED_PROTOCOL}, got ${parsedUrl.protocol}`
        };
      }

      // Extract service ID from path
      const serviceId = this.extractServiceId(parsedUrl.pathname);
      if (!serviceId) {
        return {
          valid: false,
          error: `Could not extract service ID from path: ${parsedUrl.pathname}`
        };
      }

      // Extract OAuth parameters
      const code = parsedUrl.searchParams.get('code');
      const state = parsedUrl.searchParams.get('state');
      const error = parsedUrl.searchParams.get('error');
      const errorDescription = parsedUrl.searchParams.get('error_description');
      const errorUri = parsedUrl.searchParams.get('error_uri');

      // Validate OAuth response
      if (error) {
        return {
          valid: false,
          error: `OAuth error: ${error} - ${errorDescription || 'No description provided'}`,
          parsed: {
            serviceId,
            error,
            errorDescription: errorDescription || undefined,
            errorUri: errorUri || undefined
          }
        };
      }

      // Validate required parameters for successful flow
      if (!code) {
        return {
          valid: false,
          error: 'Missing authorization code in callback'
        };
      }

      if (!state) {
        return {
          valid: false,
          error: 'Missing state parameter in callback'
        };
      }

      const parsed: ParsedOAuthCallback = {
        serviceId,
        code,
        state
      };

      console.log(`✅ Successfully parsed OAuth callback for service: ${serviceId}`);
      return {
        valid: true,
        parsed
      };

    } catch (error) {
      console.error('Failed to parse OAuth callback URL:', error);
      return {
        valid: false,
        error: `URL parsing failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  validateCallbackUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // Check protocol
      if (parsedUrl.protocol !== this.EXPECTED_PROTOCOL) {
        return false;
      }

      // Check path pattern
      const serviceId = this.extractServiceId(parsedUrl.pathname);
      return serviceId !== null;

    } catch (error) {
      return false;
    }
  }

  extractServiceId(pathname: string): string | null {
    const match = pathname.match(this.CALLBACK_PATH_PATTERN);
    return match ? match[1] : null;
  }

  buildCallbackUrl(serviceId: string, params: Record<string, string>): string {
    const url = new URL(`${this.EXPECTED_PROTOCOL}//auth-callback/${serviceId}`);
    
    for (const [key, value] of Object.entries(params)) {
      url.searchParams.set(key, value);
    }

    return url.toString();
  }

  getSupportedServices(): string[] {
    // This could be made configurable or dynamic
    return ['github', 'figma'];
  }

  isServiceSupported(serviceId: string): boolean {
    return this.getSupportedServices().includes(serviceId);
  }

  validateStateParameter(state: string): boolean {
    // Basic validation - state should be a non-empty string
    // In practice, you might want more sophisticated validation
    return typeof state === 'string' && state.length > 0;
  }

  validateAuthorizationCode(code: string): boolean {
    // Basic validation - code should be a non-empty string
    // OAuth providers typically use alphanumeric codes
    return typeof code === 'string' && code.length > 0 && /^[a-zA-Z0-9_-]+$/.test(code);
  }

  sanitizeErrorMessage(error: string, errorDescription?: string): string {
    // Sanitize error messages to prevent XSS or information leakage
    const sanitizedError = error.replace(/[<>]/g, '');
    const sanitizedDescription = errorDescription?.replace(/[<>]/g, '');

    if (sanitizedDescription) {
      return `${sanitizedError}: ${sanitizedDescription}`;
    }

    return sanitizedError;
  }

  getErrorUserMessage(error: string): string {
    // Convert OAuth error codes to user-friendly messages
    const errorMessages: Record<string, string> = {
      'access_denied': 'You denied access to the application. Please try again if you want to connect.',
      'invalid_request': 'There was an error with the authentication request. Please try again.',
      'invalid_client': 'There was a configuration error. Please contact support.',
      'invalid_grant': 'The authentication code was invalid or expired. Please try again.',
      'unauthorized_client': 'This application is not authorized. Please contact support.',
      'unsupported_grant_type': 'Authentication method not supported. Please contact support.',
      'invalid_scope': 'The requested permissions are not available. Please contact support.',
      'server_error': 'The authentication server encountered an error. Please try again later.',
      'temporarily_unavailable': 'The authentication service is temporarily unavailable. Please try again later.'
    };

    return errorMessages[error] || `Authentication failed: ${error}. Please try again.`;
  }

  logCallbackAttempt(url: string, result: CallbackValidationResult): void {
    if (result.valid) {
      console.log(`✅ Valid OAuth callback processed for service: ${result.parsed?.serviceId}`);
    } else {
      console.warn(`❌ Invalid OAuth callback: ${result.error}`);
      console.warn(`   URL: ${url}`);
    }
  }
}
