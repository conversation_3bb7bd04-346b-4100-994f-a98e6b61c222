import { URL } from 'url';

export interface OAuthCallbackResult {
  success: boolean;
  serviceId: string;
  message: string;
  error?: string;
}

export interface OAuthCallbackHandler {
  serviceId: string;
  callbackPath: string;
  handleCallback(params: URLSearchParams): Promise<void>;
}

export class OAuthCallbackRouter {
  private handlers = new Map<string, OAuthCallbackHandler>();

  registerHandler(handler: OAuthCallbackHandler): void {
    this.handlers.set(handler.serviceId, handler);
    console.log(`Registered OAuth callback handler for ${handler.serviceId} at ${handler.callbackPath}`);
  }

  unregisterHandler(serviceId: string): void {
    const removed = this.handlers.delete(serviceId);
    if (removed) {
      console.log(`Unregistered OAuth callback handler for ${serviceId}`);
    }
  }

  async routeCallback(url: string): Promise<OAuthCallbackResult> {
    console.log(`Routing OAuth callback: ${url}`);
    console.log(`Registered handlers: ${Array.from(this.handlers.keys()).join(', ')}`);

    try {
      const parsedUrl = new URL(url);
      console.log(`Parsed URL pathname: ${parsedUrl.pathname}`);
      console.log(`Parsed URL hostname: ${parsedUrl.hostname}`);

      // For custom protocol URLs, combine hostname and pathname to get full path
      const fullPath = parsedUrl.hostname + parsedUrl.pathname;
      console.log(`Full path: ${fullPath}`);

      // Extract service ID from path
      const serviceId = this.extractServiceId(fullPath);
      console.log(`Extracted service ID: ${serviceId}`);
      if (!serviceId) {
        throw new Error('Could not determine service from callback URL');
      }

      // Find handler for service
      const handler = this.handlers.get(serviceId);
      console.log(`Found handler for ${serviceId}: ${!!handler}`);
      if (!handler) {
        throw new Error(`No OAuth handler registered for service: ${serviceId}`);
      }

      // Check for OAuth errors
      const error = parsedUrl.searchParams.get('error');
      if (error) {
        const errorDescription = parsedUrl.searchParams.get('error_description') || 'OAuth authorization failed';
        throw new Error(`OAuth error: ${error} - ${errorDescription}`);
      }

      // Validate required parameters
      const code = parsedUrl.searchParams.get('code');
      const state = parsedUrl.searchParams.get('state');

      if (!code) {
        throw new Error('Authorization code not found in callback');
      }

      if (!state) {
        throw new Error('State parameter not found in callback');
      }

      // Route to service-specific handler
      await handler.handleCallback(parsedUrl.searchParams);

      console.log(`✅ OAuth callback successfully processed for ${serviceId}`);
      return {
        success: true,
        serviceId,
        message: `Successfully authenticated with ${serviceId}`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ OAuth callback routing failed:', errorMessage);
      
      const errorParsedUrl = new URL(url);
      const errorFullPath = errorParsedUrl.hostname + errorParsedUrl.pathname;

      return {
        success: false,
        serviceId: this.extractServiceId(errorFullPath) || 'unknown',
        message: 'OAuth authentication failed',
        error: errorMessage
      };
    }
  }

  getRegisteredServices(): string[] {
    return Array.from(this.handlers.keys());
  }

  hasHandler(serviceId: string): boolean {
    return this.handlers.has(serviceId);
  }

  private extractServiceId(pathname: string): string | null {
    // Handle both formats:
    // 1. Standard: /auth-callback/{serviceId}
    // 2. Custom protocol: auth-callback/{serviceId} (from hostname + pathname combination)
    const standardMatch = pathname.match(/^\/auth-callback\/([^\/]+)$/);
    if (standardMatch) {
      return standardMatch[1];
    }

    const customProtocolMatch = pathname.match(/^auth-callback\/([^\/]+)$/);
    if (customProtocolMatch) {
      return customProtocolMatch[1];
    }

    return null;
  }
}
