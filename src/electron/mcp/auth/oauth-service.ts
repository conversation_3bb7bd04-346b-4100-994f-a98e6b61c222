import crypto from 'crypto';
import keytar from 'keytar';
import fetch from 'node-fetch';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export interface OAuthConfig {
  serviceId: string;
  clientId: string;
  authorizationEndpoint: string;
  tokenEndpoint: string;
  redirectUri: string;
  scopes: string[];
  pkce: boolean;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
  scope?: string;
}

export interface AuthStatus {
  authenticated: boolean;
  expiresAt?: Date;
  scopes?: string[];
  error?: string;
}

export class OAuthService {
  protected config: OAuthConfig;
  protected metadata?: AuthorizationServerMetadata;
  private readonly KEYTAR_SERVICE = 'alpine-app';

  constructor(config: OAuthConfig) {
    this.config = config;
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.metadata = metadata;
    console.log(`Using discovered OAuth metadata for ${this.config.serviceId}`);
  }

  async initializeOAuth(): Promise<string> {
    console.log(`Initializing OAuth flow for ${this.config.serviceId}`);

    try {
      // Generate PKCE parameters
      const codeVerifier = this.generateCodeVerifier();
      const codeChallenge = await this.generateCodeChallenge(codeVerifier);
      const state = this.generateState();

      // Store PKCE parameters securely
      await keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-code-verifier`, codeVerifier);
      await keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-oauth-state`, state);

      // Use discovered endpoints if available, otherwise fall back to config
      const authEndpoint = this.metadata?.authorization_endpoint || this.config.authorizationEndpoint;

      // Build authorization URL
      const authUrl = new URL(authEndpoint);
      authUrl.searchParams.set('client_id', this.config.clientId);
      authUrl.searchParams.set('redirect_uri', this.config.redirectUri);
      authUrl.searchParams.set('scope', this.config.scopes.join(' '));
      authUrl.searchParams.set('response_type', 'code');
      authUrl.searchParams.set('state', state);

      if (this.config.pkce) {
        authUrl.searchParams.set('code_challenge', codeChallenge);
        authUrl.searchParams.set('code_challenge_method', 'S256');
      }

      const authUrlString = authUrl.toString();
      console.log(`✅ OAuth authorization URL generated for ${this.config.serviceId}`);
      return authUrlString;
    } catch (error) {
      console.error(`❌ Failed to initialize OAuth for ${this.config.serviceId}:`, error);
      throw error;
    }
  }

  async exchangeCodeForTokens(code: string, state: string): Promise<TokenResponse> {
    console.log(`Exchanging authorization code for tokens for ${this.config.serviceId}`);

    try {
      // Validate state parameter
      const storedState = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-oauth-state`);
      if (state !== storedState) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Get stored code verifier
      const codeVerifier = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-code-verifier`);
      if (!codeVerifier) {
        throw new Error('Code verifier not found');
      }

      // Get client secret from environment variables
      const clientSecret = this.getClientSecret();
      if (!clientSecret) {
        throw new Error('Client secret not found in environment variables');
      }

      // Use discovered endpoints if available
      const tokenEndpoint = this.metadata?.token_endpoint || this.config.tokenEndpoint;

      // Prepare token exchange request
      const tokenParams = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: this.config.clientId,
        client_secret: clientSecret,
        code,
        redirect_uri: this.config.redirectUri
      });

      if (this.config.pkce) {
        tokenParams.set('code_verifier', codeVerifier);
      }

      // Exchange code for tokens
      const response = await fetch(tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: tokenParams.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const tokenResponse = await response.json() as TokenResponse;

      // Store tokens securely
      await this.storeTokens(tokenResponse);

      // Clean up PKCE parameters
      await this.cleanupOAuthState();

      console.log(`✅ OAuth tokens obtained and stored for ${this.config.serviceId}`);
      return tokenResponse;
    } catch (error) {
      console.error(`❌ Token exchange failed for ${this.config.serviceId}:`, error);
      await this.cleanupOAuthState();
      throw error;
    }
  }

  async getAccessToken(): Promise<string> {
    const accessToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-access-token`);
    if (!accessToken) {
      throw new Error(`No access token found for ${this.config.serviceId}`);
    }

    // Check if token is expired
    const expiresAtStr = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-expires-at`);
    if (expiresAtStr) {
      const expiresAt = parseInt(expiresAtStr);
      if (Date.now() >= expiresAt) {
        // Try to refresh token
        await this.refreshTokenIfNeeded();
        
        // Get the refreshed token
        const refreshedToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-access-token`);
        if (!refreshedToken) {
          throw new Error(`Token expired and refresh failed for ${this.config.serviceId}`);
        }
        return refreshedToken;
      }
    }

    return accessToken;
  }

  async getAuthStatus(): Promise<AuthStatus> {
    try {
      const accessToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-access-token`);
      if (!accessToken) {
        return { authenticated: false };
      }

      const expiresAtStr = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-expires-at`);
      const metadataStr = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-metadata`);

      let expiresAt: Date | undefined;
      let scopes: string[] | undefined;

      if (expiresAtStr) {
        expiresAt = new Date(parseInt(expiresAtStr));
      }

      if (metadataStr) {
        try {
          const metadata = JSON.parse(metadataStr);
          scopes = metadata.scope?.split(' ');
        } catch (error) {
          console.warn('Failed to parse token metadata:', error);
        }
      }

      return {
        authenticated: true,
        expiresAt,
        scopes
      };
    } catch (error) {
      return {
        authenticated: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  async revokeAccess(): Promise<void> {
    console.log(`Revoking OAuth access for ${this.config.serviceId}`);

    try {
      // Remove all stored tokens
      await Promise.all([
        keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-access-token`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-refresh-token`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-expires-at`),
        keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-metadata`)
      ]);

      console.log(`✅ OAuth access revoked for ${this.config.serviceId}`);
    } catch (error) {
      console.error(`❌ Failed to revoke OAuth access for ${this.config.serviceId}:`, error);
      throw error;
    }
  }

  private async storeTokens(tokenResponse: TokenResponse): Promise<void> {
    const promises: Promise<void>[] = [
      keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-access-token`, tokenResponse.access_token)
    ];

    if (tokenResponse.refresh_token) {
      promises.push(
        keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-refresh-token`, tokenResponse.refresh_token)
      );
    }

    if (tokenResponse.expires_in) {
      const expiresAt = Date.now() + (tokenResponse.expires_in * 1000);
      promises.push(
        keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-expires-at`, expiresAt.toString())
      );
    }

    // Store token metadata
    const metadata = {
      token_type: tokenResponse.token_type,
      scope: tokenResponse.scope,
      issued_at: Date.now()
    };
    promises.push(
      keytar.setPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-token-metadata`, JSON.stringify(metadata))
    );

    await Promise.all(promises);
  }

  private async refreshTokenIfNeeded(): Promise<void> {
    const refreshToken = await keytar.getPassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-refresh-token`);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // Implement refresh token logic here
    console.log(`Refreshing token for ${this.config.serviceId}...`);
    // This would be implemented based on the specific OAuth provider's refresh token flow
  }

  private getClientSecret(): string | null {
    // Get client secret from environment variables based on service ID
    const envVarName = `${this.config.serviceId.toUpperCase()}_CLIENT_SECRET`;
    const clientSecret = process.env[envVarName] || null;
    console.log(`Looking for client secret in env var: ${envVarName}, found: ${!!clientSecret}`);
    return clientSecret;
  }

  private async cleanupOAuthState(): Promise<void> {
    await Promise.all([
      keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-code-verifier`),
      keytar.deletePassword(this.KEYTAR_SERVICE, `${this.config.serviceId}-oauth-state`)
    ]);
  }

  private generateCodeVerifier(): string {
    const buffer = crypto.randomBytes(96);
    return buffer.toString('base64url');
  }

  private async generateCodeChallenge(codeVerifier: string): Promise<string> {
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return hash.toString('base64url');
  }

  private generateState(): string {
    return crypto.randomBytes(32).toString('base64url');
  }
}
