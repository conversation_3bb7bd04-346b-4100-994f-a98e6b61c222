import fetch from 'node-fetch';

export interface AuthorizationServerMetadata {
  issuer: string;
  authorization_endpoint: string;
  token_endpoint: string;
  scopes_supported: string[];
  response_types_supported: string[];
  grant_types_supported: string[];
  code_challenge_methods_supported: string[];
  token_endpoint_auth_methods_supported: string[];
  revocation_endpoint?: string;
  mcp_capabilities?: {
    tools: boolean;
    resources: boolean;
    prompts: boolean;
    notifications: boolean;
  };
  mcp_endpoints?: {
    http: string;
    sse: string;
  };
}

export interface ProtectedResourceMetadata {
  resource: string;
  authorization_servers: string[];
  scopes_supported: string[];
  bearer_methods_supported: string[];
  resource_documentation?: string;
  mcp_tools?: Array<{
    name: string;
    required_scopes: string[];
    description: string;
  }>;
}

interface DiscoveryMetadata {
  authServerMetadata: AuthorizationServerMetadata;
  protectedResourceMetadata?: ProtectedResourceMetadata;
  timestamp: number;
}

export interface DiscoveryConfig {
  timeout: number;
  cache_duration: number;
  validate_certificates: boolean;
}

export class OAuthDiscoveryClient {
  private cache = new Map<string, DiscoveryMetadata>();
  private config: DiscoveryConfig;

  constructor(config: DiscoveryConfig = {
    timeout: 10000,
    cache_duration: 3600000, // 1 hour
    validate_certificates: true
  }) {
    this.config = config;
  }

  async discoverAuthorizationServer(discoveryUrl: string): Promise<AuthorizationServerMetadata> {
    const cached = this.cache.get(discoveryUrl);
    if (cached && !this.isExpired(cached)) {
      console.log(`Using cached discovery metadata for ${discoveryUrl}`);
      return cached.authServerMetadata;
    }

    console.log(`Discovering OAuth server metadata from ${discoveryUrl}`);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(discoveryUrl, {
        headers: { 'Accept': 'application/json' },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Discovery failed: ${response.status} ${response.statusText}`);
      }

      const metadata = await response.json() as AuthorizationServerMetadata;
      this.validateMetadata(metadata);

      this.cache.set(discoveryUrl, {
        authServerMetadata: metadata,
        timestamp: Date.now()
      });

      console.log(`Successfully discovered OAuth server metadata for ${metadata.issuer}`);
      return metadata;
    } catch (error) {
      console.error(`OAuth discovery failed for ${discoveryUrl}:`, error);
      throw error;
    }
  }

  async discoverProtectedResource(resourceUrl: string): Promise<ProtectedResourceMetadata> {
    const discoveryUrl = `${resourceUrl}/.well-known/oauth-protected-resource`;
    
    console.log(`Discovering protected resource metadata from ${discoveryUrl}`);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(discoveryUrl, {
        headers: { 'Accept': 'application/json' },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Resource discovery failed: ${response.status} ${response.statusText}`);
      }

      const metadata = await response.json() as ProtectedResourceMetadata;
      console.log(`Successfully discovered protected resource metadata for ${metadata.resource}`);
      return metadata;
    } catch (error) {
      console.error(`Protected resource discovery failed for ${resourceUrl}:`, error);
      throw error;
    }
  }

  private validateMetadata(metadata: AuthorizationServerMetadata): void {
    const required = ['issuer', 'authorization_endpoint', 'token_endpoint'];
    for (const field of required) {
      if (!metadata[field as keyof AuthorizationServerMetadata]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate PKCE support
    if (!metadata.code_challenge_methods_supported?.includes('S256')) {
      throw new Error('PKCE S256 not supported - required for security');
    }

    // Validate grant types
    if (!metadata.grant_types_supported?.includes('authorization_code')) {
      throw new Error('Authorization code grant type not supported');
    }

    console.log(`OAuth server metadata validation passed for ${metadata.issuer}`);
  }

  private isExpired(cached: DiscoveryMetadata): boolean {
    return Date.now() - cached.timestamp > this.config.cache_duration;
  }

  clearCache(): void {
    this.cache.clear();
    console.log('OAuth discovery cache cleared');
  }

  getCacheSize(): number {
    return this.cache.size;
  }

  getCachedMetadata(discoveryUrl: string): AuthorizationServerMetadata | null {
    const cached = this.cache.get(discoveryUrl);
    if (cached && !this.isExpired(cached)) {
      return cached.authServerMetadata;
    }
    return null;
  }
}
