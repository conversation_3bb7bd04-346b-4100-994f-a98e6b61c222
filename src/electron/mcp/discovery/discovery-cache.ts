import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { AIN_INDEX_DIR } from '../../constants.js';
import { AuthorizationServerMetadata, ProtectedResourceMetadata } from './oauth-discovery-client.js';

export interface CachedDiscoveryData {
  authServerMetadata: AuthorizationServerMetadata;
  protectedResourceMetadata?: ProtectedResourceMetadata;
  timestamp: number;
  expiresAt: number;
}

export interface DiscoveryCacheConfig {
  ttl: number; // Time to live in milliseconds
  maxEntries: number;
  persistToDisk: boolean;
  cacheDirectory?: string;
}

export class DiscoveryCache {
  private cache = new Map<string, CachedDiscoveryData>();
  private config: DiscoveryCacheConfig;
  private cacheFilePath: string;

  constructor(config: DiscoveryCacheConfig = {
    ttl: 3600000, // 1 hour
    maxEntries: 100,
    persistToDisk: true
  }) {
    this.config = config;
    this.cacheFilePath = path.join(
      config.cacheDirectory || path.join(os.homedir(), AIN_INDEX_DIR),
      'oauth-discovery-cache.json'
    );

    if (config.persistToDisk) {
      this.loadFromDisk();
    }

    // Start cleanup interval
    this.startCleanupInterval();
  }

  async set(
    discoveryUrl: string,
    authServerMetadata: AuthorizationServerMetadata,
    protectedResourceMetadata?: ProtectedResourceMetadata
  ): Promise<void> {
    const now = Date.now();
    const expiresAt = now + this.config.ttl;

    const cachedData: CachedDiscoveryData = {
      authServerMetadata,
      protectedResourceMetadata,
      timestamp: now,
      expiresAt
    };

    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxEntries) {
      this.evictOldestEntry();
    }

    this.cache.set(discoveryUrl, cachedData);

    if (this.config.persistToDisk) {
      await this.saveToDisk();
    }

    console.log(`Cached discovery data for ${discoveryUrl}, expires at ${new Date(expiresAt).toISOString()}`);
  }

  get(discoveryUrl: string): CachedDiscoveryData | null {
    const cached = this.cache.get(discoveryUrl);
    
    if (!cached) {
      return null;
    }

    if (this.isExpired(cached)) {
      this.cache.delete(discoveryUrl);
      console.log(`Expired cache entry removed for ${discoveryUrl}`);
      return null;
    }

    console.log(`Cache hit for ${discoveryUrl}`);
    return cached;
  }

  has(discoveryUrl: string): boolean {
    const cached = this.cache.get(discoveryUrl);
    return cached !== undefined && !this.isExpired(cached);
  }

  delete(discoveryUrl: string): boolean {
    const deleted = this.cache.delete(discoveryUrl);
    if (deleted && this.config.persistToDisk) {
      this.saveToDisk().catch(console.error);
    }
    return deleted;
  }

  clear(): void {
    this.cache.clear();
    if (this.config.persistToDisk) {
      this.saveToDisk().catch(console.error);
    }
    console.log('Discovery cache cleared');
  }

  size(): number {
    return this.cache.size;
  }

  getStats(): {
    size: number;
    maxEntries: number;
    ttl: number;
    oldestEntry?: Date;
    newestEntry?: Date;
  } {
    let oldestTimestamp = Infinity;
    let newestTimestamp = 0;

    for (const cached of this.cache.values()) {
      if (cached.timestamp < oldestTimestamp) {
        oldestTimestamp = cached.timestamp;
      }
      if (cached.timestamp > newestTimestamp) {
        newestTimestamp = cached.timestamp;
      }
    }

    return {
      size: this.cache.size,
      maxEntries: this.config.maxEntries,
      ttl: this.config.ttl,
      oldestEntry: oldestTimestamp !== Infinity ? new Date(oldestTimestamp) : undefined,
      newestEntry: newestTimestamp !== 0 ? new Date(newestTimestamp) : undefined
    };
  }

  private isExpired(cached: CachedDiscoveryData): boolean {
    return Date.now() > cached.expiresAt;
  }

  private evictOldestEntry(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, cached] of this.cache.entries()) {
      if (cached.timestamp < oldestTimestamp) {
        oldestTimestamp = cached.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`Evicted oldest cache entry: ${oldestKey}`);
    }
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expiresAt) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`Cleaned up ${removedCount} expired discovery cache entries`);
      if (this.config.persistToDisk) {
        this.saveToDisk().catch(console.error);
      }
    }
  }

  private async loadFromDisk(): Promise<void> {
    try {
      if (await fs.pathExists(this.cacheFilePath)) {
        const data = await fs.readFile(this.cacheFilePath, 'utf-8');
        const cacheData = JSON.parse(data);

        for (const [key, value] of Object.entries(cacheData)) {
          const cached = value as CachedDiscoveryData;
          if (!this.isExpired(cached)) {
            this.cache.set(key, cached);
          }
        }

        console.log(`Loaded ${this.cache.size} discovery cache entries from disk`);
      }
    } catch (error) {
      console.error('Failed to load discovery cache from disk:', error);
    }
  }

  private async saveToDisk(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.cacheFilePath));
      
      const cacheData: Record<string, CachedDiscoveryData> = {};
      for (const [key, value] of this.cache.entries()) {
        cacheData[key] = value;
      }

      await fs.writeFile(this.cacheFilePath, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.error('Failed to save discovery cache to disk:', error);
    }
  }
}
