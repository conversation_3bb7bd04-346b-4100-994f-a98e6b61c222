import { MCPHost } from '../host/mcp-host.js';
import { MCPConfigurationManager } from '../config/mcp-configuration-manager.js';
import { OAuthDiscoveryClient } from '../discovery/oauth-discovery-client.js';
import { ServiceRegistry } from '../registry/service-registry.js';
import path from 'path';
import os from 'os';
import fs from 'fs-extra';

/**
 * Integration test for MCP system
 * This test demonstrates the complete OAuth discovery and HTTP/SSE communication flow
 */
export class MCPIntegrationTest {
  private mcpHost: MCPHost;
  private testConfigPath: string;

  constructor() {
    this.testConfigPath = path.join(os.tmpdir(), 'mcp-test-config.json');
    this.mcpHost = new MCPHost({
      configPath: this.testConfigPath,
      enableDiscovery: true,
      enableMetrics: true
    });
  }

  async runTests(): Promise<void> {
    console.log('🧪 Starting MCP Integration Tests...');

    try {
      await this.setupTestConfiguration();
      await this.testConfigurationLoading();
      await this.testOAuthDiscovery();
      await this.testServiceRegistry();
      await this.testMCPHostInitialization();
      await this.testServiceManagement();
      await this.testHealthChecks();
      
      console.log('✅ All MCP Integration Tests passed!');
    } catch (error) {
      console.error('❌ MCP Integration Tests failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  private async setupTestConfiguration(): Promise<void> {
    console.log('📝 Setting up test configuration...');

    const testConfig = {
      "$schema": "https://schemas.alpine-intellect.com/mcp-config.schema.json",
      "version": "1.0",
      "servers": {
        "github": {
          "transport": "http-sse",
          "enabled": true,
          "endpoints": {
            "http": "https://api.githubcopilot.com/mcp/",
            "sse": "https://api.githubcopilot.com/mcp/events",
            "discovery": "https://api.github.com/mcp/.well-known/oauth-authorization-server"
          },
          "auth": {
            "type": "oauth",
            "client_id": "test-github-client-id",
            "redirect_uri": "thealpinecode.alpineintellect://auth-callback/github",
            "scopes": ["repo", "user"],
            "pkce": true
          },
          "capabilities": ["tools", "notifications"],
          "tools": ["create_repository", "get_user"],
          "connection": {
            "timeout": 30000,
            "retry_attempts": 3,
            "retry_delay": 1000
          },
          "cache": {
            "enabled": true,
            "ttl": 300000
          }
        },
        "figma": {
          "transport": "http-sse",
          "enabled": false,
          "endpoints": {
            "http": "https://api.figma.com/mcp/v1",
            "sse": "https://api.figma.com/mcp/v1/events",
            "discovery": "https://api.figma.com/mcp/.well-known/oauth-authorization-server"
          },
          "auth": {
            "type": "oauth",
            "client_id": "test-figma-client-id",
            "redirect_uri": "thealpinecode.alpineintellect://auth-callback/figma",
            "scopes": ["file:read"],
            "pkce": true
          },
          "capabilities": ["tools", "resources"],
          "tools": ["get_file_content"],
          "connection": {
            "timeout": 30000,
            "retry_attempts": 3,
            "retry_delay": 1000
          },
          "cache": {
            "enabled": true,
            "ttl": 300000
          }
        }
      },
      "global": {
        "discovery": {
          "enabled": true,
          "timeout": 10000,
          "cache_duration": 3600000
        },
        "security": {
          "validate_certificates": true,
          "require_pkce": true,
          "allowed_redirect_schemes": ["thealpinecode.alpineintellect"]
        }
      }
    };

    await fs.writeFile(this.testConfigPath, JSON.stringify(testConfig, null, 2));
    console.log('✅ Test configuration created');
  }

  private async testConfigurationLoading(): Promise<void> {
    console.log('📋 Testing configuration loading...');

    const configManager = new MCPConfigurationManager(this.testConfigPath);
    await configManager.loadConfiguration();

    const config = configManager.getConfiguration();
    
    // Validate configuration structure
    if (!config.servers.github) {
      throw new Error('GitHub server configuration not found');
    }

    if (!config.servers.figma) {
      throw new Error('Figma server configuration not found');
    }

    if (config.servers.github.enabled !== true) {
      throw new Error('GitHub server should be enabled');
    }

    if (config.servers.figma.enabled !== false) {
      throw new Error('Figma server should be disabled');
    }

    const enabledServers = configManager.getEnabledServers();
    if (enabledServers.length !== 1 || enabledServers[0] !== 'github') {
      throw new Error('Expected only GitHub to be enabled');
    }

    console.log('✅ Configuration loading test passed');
  }

  private async testOAuthDiscovery(): Promise<void> {
    console.log('🔍 Testing OAuth discovery...');

    const discoveryClient = new OAuthDiscoveryClient({
      timeout: 5000,
      cache_duration: 300000,
      validate_certificates: false // For testing
    });

    try {
      // Test discovery with a mock endpoint (this will fail, but we test the structure)
      await discoveryClient.discoverAuthorizationServer(
        'https://httpbin.org/json'
      );
    } catch (error) {
      // Expected to fail with real endpoints, but we test the error handling
      console.log('Expected discovery failure (testing error handling):', error instanceof Error ? error.message : error);
    }

    // Test cache functionality
    const cacheSize = discoveryClient.getCacheSize();
    console.log(`Discovery cache size: ${cacheSize}`);

    console.log('✅ OAuth discovery test completed');
  }

  private async testServiceRegistry(): Promise<void> {
    console.log('📊 Testing service registry...');

    const configManager = new MCPConfigurationManager(this.testConfigPath);
    await configManager.loadConfiguration();

    const serviceRegistry = ServiceRegistry.getInstance(configManager);
    await serviceRegistry.initializeFromConfiguration();

    // Test service registration
    const allServices = serviceRegistry.getAllServices();
    if (allServices.length === 0) {
      throw new Error('No services registered');
    }

    const enabledServices = serviceRegistry.getEnabledServices();
    if (enabledServices.length !== 1) {
      throw new Error('Expected exactly one enabled service');
    }

    const githubService = serviceRegistry.getService('github');
    if (!githubService) {
      throw new Error('GitHub service not found');
    }

    if (githubService.serviceId !== 'github') {
      throw new Error('GitHub service ID mismatch');
    }

    // Test service stats
    const stats = await serviceRegistry.getServiceStats();
    if (stats.totalServices === 0) {
      throw new Error('Service stats show no services');
    }

    console.log('Service registry stats:', stats);
    console.log('✅ Service registry test passed');
  }

  private async testMCPHostInitialization(): Promise<void> {
    console.log('🏠 Testing MCP Host initialization...');

    await this.mcpHost.initialize();

    if (!this.mcpHost.isInitialized()) {
      throw new Error('MCP Host not initialized');
    }

    // Test host stats
    const stats = await this.mcpHost.getHostStats();
    if (stats.uptime <= 0) {
      throw new Error('Invalid uptime in host stats');
    }

    console.log('MCP Host stats:', stats);
    console.log('✅ MCP Host initialization test passed');
  }

  private async testServiceManagement(): Promise<void> {
    console.log('⚙️ Testing service management...');

    // Test getting service status
    const githubStatus = await this.mcpHost.getServiceStatus('github');
    if (githubStatus.serviceId !== 'github') {
      throw new Error('GitHub service status mismatch');
    }

    // Test getting all services status
    const allStatuses = await this.mcpHost.getAllServicesStatus();
    if (allStatuses.length === 0) {
      throw new Error('No service statuses returned');
    }

    // Test enabling/disabling services
    await this.mcpHost.disableService('github');
    const disabledStatus = await this.mcpHost.getServiceStatus('github');
    if (disabledStatus.enabled !== false) {
      throw new Error('GitHub service should be disabled');
    }

    await this.mcpHost.enableService('github');
    const enabledStatus = await this.mcpHost.getServiceStatus('github');
    if (enabledStatus.enabled !== true) {
      throw new Error('GitHub service should be enabled');
    }

    console.log('✅ Service management test passed');
  }

  private async testHealthChecks(): Promise<void> {
    console.log('🏥 Testing health checks...');

    const healthCheck = await this.mcpHost.performHealthCheck();
    
    if (typeof healthCheck.healthy !== 'boolean') {
      throw new Error('Health check should return boolean healthy status');
    }

    if (!Array.isArray(healthCheck.services)) {
      throw new Error('Health check should return services array');
    }

    console.log('Health check result:', healthCheck);
    console.log('✅ Health check test passed');
  }

  private async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up test resources...');

    try {
      await this.mcpHost.shutdown();
      await fs.remove(this.testConfigPath);
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error);
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new MCPIntegrationTest();
  test.runTests().catch(console.error);
}
