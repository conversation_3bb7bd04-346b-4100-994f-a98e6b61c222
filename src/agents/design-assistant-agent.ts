import { MCPHost } from '../electron/mcp/host/mcp-host.js';

export interface DesignAssistantCapabilities {
  fileAccess: boolean;
  assetExport: boolean;
  componentManagement: boolean;
  teamCollaboration: boolean;
}

export interface DesignAssistantRequest {
  type: 'get_file' | 'export_assets' | 'create_component' | 'get_team_projects' | 'analyze_design';
  parameters: any;
  context?: {
    designSystem?: string;
    brand?: string;
    platform?: 'web' | 'mobile' | 'desktop';
  };
}

export interface DesignAssistantResponse {
  success: boolean;
  data?: any;
  error?: string;
  suggestions?: string[];
}

export class DesignAssistantAgent {
  private mcpHost: MCPHost;
  private capabilities: DesignAssistantCapabilities;

  constructor(mcpHost: MCPHost) {
    this.mcpHost = mcpHost;
    this.capabilities = {
      fileAccess: false,
      assetExport: false,
      componentManagement: false,
      teamCollaboration: false
    };
  }

  async initialize(): Promise<void> {
    console.log('Initializing Design Assistant Agent...');
    
    try {
      // Check Figma service availability
      const figmaStatus = await this.mcpHost.getServiceStatus('figma');
      
      if (figmaStatus.authenticated && figmaStatus.healthy) {
        this.capabilities = {
          fileAccess: figmaStatus.availableTools.includes('get_file_content'),
          assetExport: figmaStatus.availableTools.includes('export_assets'),
          componentManagement: figmaStatus.availableTools.includes('create_component'),
          teamCollaboration: figmaStatus.availableTools.includes('get_team_projects')
        };
        
        console.log('✅ Design Assistant Agent initialized with Figma integration');
      } else {
        console.log('⚠️ Design Assistant Agent initialized without Figma integration');
      }
    } catch (error) {
      console.error('❌ Failed to initialize Design Assistant Agent:', error);
    }
  }

  async processRequest(request: DesignAssistantRequest): Promise<DesignAssistantResponse> {
    console.log(`Processing design assistant request: ${request.type}`);

    try {
      switch (request.type) {
        case 'get_file':
          return await this.getDesignFile(request.parameters);
        
        case 'export_assets':
          return await this.exportAssets(request.parameters);
        
        case 'create_component':
          return await this.createComponent(request.parameters);
        
        case 'get_team_projects':
          return await this.getTeamProjects(request.parameters);
        
        case 'analyze_design':
          return await this.analyzeDesign(request.parameters);
        
        default:
          return {
            success: false,
            error: `Unknown request type: ${request.type}`
          };
      }
    } catch (error) {
      console.error(`Design assistant request failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async getDesignFile(params: any): Promise<DesignAssistantResponse> {
    if (!this.capabilities.fileAccess) {
      return {
        success: false,
        error: 'Design file access not available. Please connect to Figma.'
      };
    }

    const result = await this.mcpHost.executeTool('figma', 'get_file_content', {
      file_key: params.fileKey,
      version: params.version,
      depth: params.depth || 1
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Analyze the design structure',
        'Extract design tokens',
        'Review component hierarchy',
        'Check for design system compliance'
      ]
    };
  }

  private async exportAssets(params: any): Promise<DesignAssistantResponse> {
    if (!this.capabilities.assetExport) {
      return {
        success: false,
        error: 'Asset export not available. Please connect to Figma.'
      };
    }

    const result = await this.mcpHost.executeTool('figma', 'export_assets', {
      file_key: params.fileKey,
      ids: params.nodeIds,
      format: params.format || 'png',
      scale: params.scale || 1
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Optimize exported assets for web',
        'Generate multiple resolutions',
        'Consider SVG format for icons',
        'Compress images for production'
      ]
    };
  }

  private async createComponent(params: any): Promise<DesignAssistantResponse> {
    if (!this.capabilities.componentManagement) {
      return {
        success: false,
        error: 'Component management not available. Please connect to Figma.'
      };
    }

    const result = await this.mcpHost.executeTool('figma', 'create_component', {
      file_key: params.fileKey,
      node_id: params.nodeId,
      name: params.name,
      description: params.description || 'Created with Alpine Intellect'
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Add component documentation',
        'Define component variants',
        'Set up auto-layout properties',
        'Create component instances'
      ]
    };
  }

  private async getTeamProjects(params: any): Promise<DesignAssistantResponse> {
    if (!this.capabilities.teamCollaboration) {
      return {
        success: false,
        error: 'Team collaboration not available. Please connect to Figma.'
      };
    }

    const result = await this.mcpHost.executeTool('figma', 'get_team_projects', {
      team_id: params.teamId
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Review project organization',
        'Check team permissions',
        'Analyze project activity',
        'Identify collaboration opportunities'
      ]
    };
  }

  private async analyzeDesign(params: any): Promise<DesignAssistantResponse> {
    // This would be a more complex operation that combines multiple tools
    const analyses = [];

    try {
      // Get design file structure
      const fileContent = await this.mcpHost.executeTool('figma', 'get_file_content', {
        file_key: params.fileKey,
        depth: 2
      });
      analyses.push({ type: 'structure', data: fileContent });

      // If team ID is provided, get team context
      if (params.teamId) {
        const teamProjects = await this.mcpHost.executeTool('figma', 'get_team_projects', {
          team_id: params.teamId
        });
        analyses.push({ type: 'team_context', data: teamProjects });
      }

      return {
        success: true,
        data: {
          fileKey: params.fileKey,
          analyses,
          insights: this.generateDesignInsights(analyses)
        },
        suggestions: [
          'Review design consistency',
          'Check accessibility compliance',
          'Analyze component usage',
          'Optimize design system'
        ]
      };
    } catch (error) {
      return {
        success: false,
        error: `Design analysis failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  private generateDesignInsights(analyses: any[]): string[] {
    const insights: string[] = [];

    for (const analysis of analyses) {
      if (analysis.type === 'structure') {
        // Analyze design structure
        const content = analysis.data?.content?.[0]?.text;
        if (content) {
          try {
            const data = JSON.parse(content);
            if (data.document?.children) {
              insights.push(`Design contains ${data.document.children.length} top-level frames`);
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }

      if (analysis.type === 'team_context') {
        // Analyze team context
        const content = analysis.data?.content?.[0]?.text;
        if (content) {
          try {
            const data = JSON.parse(content);
            if (data.projects) {
              insights.push(`Team has ${data.projects.length} active projects`);
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }
    }

    return insights;
  }

  getCapabilities(): DesignAssistantCapabilities {
    return { ...this.capabilities };
  }

  async refreshCapabilities(): Promise<void> {
    await this.initialize();
  }

  isAvailable(): boolean {
    return Object.values(this.capabilities).some(capability => capability);
  }

  getSupportedOperations(): string[] {
    const operations: string[] = [];
    
    if (this.capabilities.fileAccess) {
      operations.push('get_file', 'analyze_design');
    }
    
    if (this.capabilities.assetExport) {
      operations.push('export_assets');
    }
    
    if (this.capabilities.componentManagement) {
      operations.push('create_component');
    }
    
    if (this.capabilities.teamCollaboration) {
      operations.push('get_team_projects');
    }
    
    return operations;
  }

  async getDesignTokens(fileKey: string): Promise<DesignAssistantResponse> {
    if (!this.capabilities.fileAccess) {
      return {
        success: false,
        error: 'Design file access not available. Please connect to Figma.'
      };
    }

    try {
      const fileContent = await this.mcpHost.executeTool('figma', 'get_file_content', {
        file_key: fileKey,
        depth: 3
      });

      // Extract design tokens from the file structure
      const tokens = this.extractDesignTokens(fileContent);

      return {
        success: true,
        data: { tokens },
        suggestions: [
          'Export tokens as CSS variables',
          'Generate design system documentation',
          'Sync tokens with development tools',
          'Validate token consistency'
        ]
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to extract design tokens: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  private extractDesignTokens(fileContent: any): any {
    // This would implement actual design token extraction logic
    // For now, return a placeholder structure
    return {
      colors: {},
      typography: {},
      spacing: {},
      shadows: {}
    };
  }
}
