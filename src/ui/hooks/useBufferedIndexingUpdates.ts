import { useEffect, useRef } from "react";
import {
  safeIdleCallback,
  cancelSafeIdleCallback,
  safeAnimationFrame,
  IdleCallbackHandle,
} from "@/utils/safeCallbacks";

export interface ProgressUpdate {
  path: string;
  progress: number; // 0–1
  desc?: string;
  status?: "indexing" | "done" | "paused" | "failed";
}

export interface MetaUpdate {
  [dirKey: string]: {
    totalSize?: number;
    techStack?: string[];
    filesCount?: number;
    status?: string;
    progress?: number;
    lastIndexed?: string;
    indexingStartedAt?: number;
    indexingFinishedAt?: number;
    indexingDuration?: string;
    name?: string;
    color?: string;
    createdAt?: string;
  };
}

export function useBufferedIndexingUpdates(
  setProjects: React.Dispatch<React.SetStateAction<Record<string, any>>>
) {
  const flushHandleRef = useRef<IdleCallbackHandle | null>(null);
  const latestProgressRef = useRef<ProgressUpdate | null>(null);
  const latestMetaRef = useRef<MetaUpdate | null>(null);

  useEffect(() => {
    const flushBufferedUpdates = () => {
      const progress = latestProgressRef.current;
      const meta = latestMetaRef.current;
      latestProgressRef.current = null;
      latestMetaRef.current = null;

      if (progress && progress.path) {
        const { path, progress: pct, desc, status } = progress;
        const dirKey = path;

        setProjects((prev) => {
          const existing = prev[dirKey] || {
            name: dirKey.split("/").pop(),
            color: "blue",
            createdAt: new Date().toISOString(),
            totalSize: 0,
            techStack: [],
            filesCount: 0,
            lastIndexed: "Indexing...",
            indexingStartedAt: Date.now(),
            indexingFinishedAt: Date.now(),
            indexingDuration: "In Progress",
            progress: 0,
            status: "indexing",
          };

          return {
            ...prev,
            [dirKey]: {
              ...existing,
              progress: Math.min(100, Math.round(pct * 100)),
              status: status ?? existing.status,
              lastIndexed: existing.lastIndexed?? "Indexing...",
              indexingFinishedAt: existing.indexingFinishedAt,
              indexingDuration: existing.indexingDuration,
              color: existing.color,
              createdAt: existing.createdAt,
            },
          };
        });
      }

      if (meta && typeof meta === "object") {
        setProjects((prev) => {
          const updated = { ...prev };

          for (const [dirKey, metaPatch] of Object.entries(meta)) {
            if (!dirKey || typeof metaPatch !== "object") continue;

            const existing = updated[dirKey] || {
              name: dirKey.split("/").pop(),
              color: "yellow",
              createdAt: new Date().toISOString(),
              totalSize: 0,
              techStack: [],
              filesCount: 0,
              lastIndexed: "Indexing...",
              indexingStartedAt: Date.now(),
              indexingFinishedAt: Date.now(),
              indexingDuration: "In Progress",
              progress: 0,
              status: "indexing",
            };

            updated[dirKey] = {
              ...existing,
              ...metaPatch,
              progress:
                metaPatch.progress !== undefined
                  ? Math.min(100, Math.round(metaPatch.progress * 100))
                  : existing.progress,
              status: metaPatch.status ?? existing.status,
              lastIndexed: metaPatch.lastIndexed ?? existing.lastIndexed,
              indexingStartedAt:
                metaPatch.indexingStartedAt ?? existing.indexingStartedAt,
              indexingFinishedAt:
                metaPatch.indexingFinishedAt ?? existing.indexingFinishedAt,
              indexingDuration:
                metaPatch.indexingDuration ?? existing.indexingDuration,
              color: metaPatch.color ?? existing.color,
              createdAt: metaPatch.createdAt ?? existing.createdAt,
            };
          }

          return updated;
        });
      }

      flushHandleRef.current = null;
    };

    const scheduleFlush = () => {
      if (flushHandleRef.current !== null) return;
      flushHandleRef.current = safeIdleCallback(flushBufferedUpdates, {
        timeout: 200,
      });
      safeAnimationFrame(flushBufferedUpdates);
    };

    const onProgress = (data: ProgressUpdate) => {
      if (data && typeof data === "object") {
        latestProgressRef.current = data;
        scheduleFlush();
      }
    };

    const onMeta = (data: MetaUpdate) => {
      if (data && typeof data === "object") {
        latestMetaRef.current = data;
        scheduleFlush();
      }
    };

    window.api?.onIndexingProgress?.(onProgress);
    window.api?.onIndexingMeta?.(onMeta);

    // Optional cleanup (if needed in future):
    // return () => {
    //   if (flushHandleRef.current !== null) {
    //     cancelSafeIdleCallback(flushHandleRef.current);
    //   }
    //   window.api?.removeIndexingListeners?.();
    // };
  }, [setProjects]);
}
