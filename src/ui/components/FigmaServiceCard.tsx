import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Switch } from './ui/switch';
import { Alert, AlertDescription } from './ui/alert';
import { Separator } from './ui/separator';
import { MCPServiceStatus, FigmaToolParams } from '../types/mcp-types';
import { 
  Figma, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Settings, 
  ExternalLink,
  FileImage,
  Download,
  Users,
  Layers
} from 'lucide-react';

interface FigmaServiceCardProps {
  service: MCPServiceStatus;
  onAuth: () => void;
  onToggle: (enabled: boolean) => void;
}

export function FigmaServiceCard({ service, onAuth, onToggle }: FigmaServiceCardProps) {
  const [executing, setExecuting] = useState<string | null>(null);
  const [toolResults, setToolResults] = useState<Record<string, any>>({});

  const getStatusIcon = () => {
    if (!service.enabled) return <AlertCircle className="h-4 w-4 text-gray-400" />;
    if (!service.authenticated) return <Clock className="h-4 w-4 text-yellow-500" />;
    if (!service.healthy) return <AlertCircle className="h-4 w-4 text-red-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!service.enabled) return 'Disabled';
    if (!service.authenticated) return 'Authentication Required';
    if (!service.healthy) return 'Connection Issues';
    return 'Connected';
  };

  const getStatusVariant = () => {
    if (!service.enabled) return 'secondary';
    if (!service.authenticated) return 'outline';
    if (!service.healthy) return 'destructive';
    return 'default';
  };

  const executeTool = async (toolName: string, parameters: any) => {
    try {
      setExecuting(toolName);
      const result = await window.api.mcpExecuteTool('figma', toolName, parameters);
      
      if (result.success && result.result) {
        setToolResults(prev => ({
          ...prev,
          [toolName]: result.result
        }));
      } else {
        console.error('Tool execution failed:', result.error);
      }
    } catch (error) {
      console.error('Tool execution error:', error);
    } finally {
      setExecuting(null);
    }
  };

  const handleQuickAction = async (action: string) => {
    switch (action) {
      case 'get_file':
        // Example with a demo file key - in real usage, this would come from user input
        await executeTool('get_file_content', {
          file_key: 'demo-file-key',
          depth: 1
        } as FigmaToolParams['getFileContent']);
        break;
      
      case 'get_team_projects':
        // Example with a demo team ID - in real usage, this would come from user input
        await executeTool('get_team_projects', {
          team_id: 'demo-team-id'
        } as FigmaToolParams['getTeamProjects']);
        break;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Figma className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <CardTitle className="flex items-center gap-2">
                Figma
                {getStatusIcon()}
              </CardTitle>
              <CardDescription>
                Design file access and team collaboration
              </CardDescription>
            </div>
          </div>
          <Switch
            checked={service.enabled}
            onCheckedChange={onToggle}
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status Section */}
        <div className="flex items-center justify-between">
          <Badge variant={getStatusVariant()}>
            {getStatusText()}
          </Badge>
          {service.authStatus.expiresAt && (
            <span className="text-xs text-muted-foreground">
              Expires: {new Date(service.authStatus.expiresAt).toLocaleDateString()}
            </span>
          )}
        </div>

        {/* Authentication Section */}
        {service.enabled && !service.authenticated && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Connect your Figma account to access design files and team resources.
            </AlertDescription>
          </Alert>
        )}

        {service.enabled && (
          <div className="space-y-3">
            {!service.authenticated ? (
              <Button onClick={onAuth} className="w-full">
                <Figma className="w-4 h-4 mr-2" />
                Connect to Figma
              </Button>
            ) : (
              <div className="space-y-3">
                {/* Scopes Display */}
                {service.authStatus.scopes && (
                  <div>
                    <div className="text-sm font-medium mb-2">Granted Permissions:</div>
                    <div className="flex flex-wrap gap-1">
                      {service.authStatus.scopes.map((scope) => (
                        <Badge key={scope} variant="outline" className="text-xs">
                          {scope}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Quick Actions */}
                <div>
                  <div className="text-sm font-medium mb-2">Quick Actions:</div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAction('get_file')}
                      disabled={executing === 'get_file_content'}
                    >
                      <FileImage className="w-3 h-3 mr-1" />
                      {executing === 'get_file_content' ? 'Loading...' : 'Get File'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAction('get_team_projects')}
                      disabled={executing === 'get_team_projects'}
                    >
                      <Users className="w-3 h-3 mr-1" />
                      {executing === 'get_team_projects' ? 'Loading...' : 'Team Projects'}
                    </Button>
                  </div>
                </div>

                {/* Available Tools */}
                <div>
                  <div className="text-sm font-medium mb-2">Available Tools:</div>
                  <div className="grid grid-cols-2 gap-1 text-xs">
                    {service.availableTools.map((tool) => (
                      <div key={tool} className="flex items-center gap-1 text-muted-foreground">
                        {tool === 'get_file_content' && <FileImage className="w-3 h-3" />}
                        {tool === 'export_assets' && <Download className="w-3 h-3" />}
                        {tool === 'create_component' && <Layers className="w-3 h-3" />}
                        {tool === 'get_team_projects' && <Users className="w-3 h-3" />}
                        <span>{tool.replace(/_/g, ' ')}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tool Results */}
                {Object.keys(toolResults).length > 0 && (
                  <div>
                    <div className="text-sm font-medium mb-2">Recent Results:</div>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {Object.entries(toolResults).map(([tool, result]) => (
                        <div key={tool} className="p-2 bg-muted rounded text-xs">
                          <div className="font-medium">{tool}:</div>
                          <div className="text-muted-foreground truncate">
                            {result.content?.[0]?.text || 'Success'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Connection Info */}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Endpoint: {service.endpoints.http}</span>
                    <Button variant="ghost" size="sm" className="h-auto p-0">
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {service.authStatus.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{service.authStatus.error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
