
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { TitleBar } from "@/components/TitleBar";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
}

const Layout = ({ children, title }: LayoutProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const isMobile = useIsMobile();

  // Auto-collapse sidebar on mobile/small screens
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  const menuItems = [
    { label: "Dashboard", path: "/dashboard", icon: "🏠" },
    { label: "IDE Integrator", path: "/ide-integrator", icon: "🔧" },
    { label: "Model Management", path: "/models", icon: "🤖" },
    { label: "MCP Services", path: "/mcp", icon: "🔗" },
    { label: "Projects", path: "/projects", icon: "📂" },
    { label: "Settings", path: "/settings", icon: "⚙️" },
  ];

  const handleLogout = () => {
    navigate("/");
  };

  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      {/* Fixed Title Bar */}
      <div className="fixed top-0 left-0 right-0 z-20">
        <TitleBar title={title} />
      </div>
      
      <div className="flex flex-1 pt-8">
        {/* Fixed Sidebar */}
        <div className={cn(
          "bg-slate-800 border-r border-slate-700 flex flex-col fixed left-0 top-8 h-[calc(100vh-2rem)] z-10 transition-all duration-300",
          collapsed ? "w-16" : "w-64"
        )}>
          {/* Header */}
          <div className="p-3 border-b border-slate-700 flex-shrink-0">
            <div className="flex items-center space-x-3">
              <div className="w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xs">AI</span>
              </div>
              {!collapsed && (
                <h1 className="text-white text-base font-semibold">Alpine Intellect</h1>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCollapsed(!collapsed)}
              className="mt-2 text-slate-400 hover:text-white h-7 w-7 p-0"
            >
              {collapsed ? "→" : "←"}
            </Button>
          </div>

          {/* Navigation - Scrollable */}
          <nav className="flex-1 p-3 overflow-y-auto">
            <div className="space-y-1">
              {menuItems.map((item) => (
                <Button
                  key={item.path}
                  variant="ghost"
                  onClick={() => navigate(item.path)}
                  className={cn(
                    "w-full justify-start text-slate-300 hover:text-white hover:bg-slate-700 h-9",
                    location.pathname === item.path && "bg-slate-700 text-white",
                    collapsed ? "px-2" : "px-3"
                  )}
                >
                  <span className="text-base mr-3">{item.icon}</span>
                  {!collapsed && item.label}
                </Button>
              ))}
            </div>
          </nav>

          {/* Logout - Fixed at bottom */}
          <div className="p-3 border-t border-slate-700 flex-shrink-0">
            <Button
              variant="ghost"
              onClick={handleLogout}
              className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-700 h-9"
            >
              <span className="text-base mr-3">🚪</span>
              {!collapsed && "Logout"}
            </Button>
          </div>
        </div>

        {/* Main Content with left margin to account for fixed sidebar */}
        <div className={cn(
          "flex-1 transition-all duration-300",
          collapsed ? "ml-16" : "ml-64"
        )}>
          <div className="h-[calc(100vh-2rem)] overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export { Layout };