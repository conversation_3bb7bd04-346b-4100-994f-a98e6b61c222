import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { MCP_SERVICE_CONFIGS } from '../types/mcp-types';
import { 
  ExternalLink, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Github,
  Figma
} from 'lucide-react';

interface ServiceAuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  serviceId: string | null;
  onSuccess: () => void;
}

export function ServiceAuthDialog({ open, onOpenChange, serviceId, onSuccess }: ServiceAuthDialogProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [step, setStep] = useState<'permissions' | 'authenticating' | 'waiting'>('permissions');

  const serviceConfig = serviceId ? MCP_SERVICE_CONFIGS[serviceId] : null;

  useEffect(() => {
    if (open && serviceId) {
      setStep('permissions');
      setError(null);
      setAuthUrl(null);
    }
  }, [open, serviceId]);

  useEffect(() => {
    // Listen for OAuth callback events
    const handleSuccess = () => {
      setStep('permissions');
      setLoading(false);
      onSuccess();
    };

    const handleError = (data: any) => {
      setError(data.error || 'Authentication failed');
      setStep('permissions');
      setLoading(false);
    };

    if (open) {
      window.api.onMcpOAuthCallbackSuccess(handleSuccess);
      window.api.onMcpOAuthCallbackError(handleError);
    }

    return () => {
      if (open) {
        window.api.removeMcpListeners();
      }
    };
  }, [open, onSuccess]);

  const handleAuthenticate = async () => {
    if (!serviceId) return;

    try {
      setLoading(true);
      setError(null);
      setStep('authenticating');

      const result = await window.api.mcpAuthenticateService(serviceId);
      
      if (result.success && result.authUrl) {
        setAuthUrl(result.authUrl);
        setStep('waiting');
        
        // Open the OAuth URL in the system browser
        window.api.openExternal(result.authUrl);
      } else {
        throw new Error(result.error || 'Failed to start authentication');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Authentication failed');
      setStep('permissions');
      setLoading(false);
    }
  };

  const getServiceIcon = () => {
    switch (serviceId) {
      case 'github':
        return <Github className="h-8 w-8" />;
      case 'figma':
        return <Figma className="h-8 w-8 text-purple-600" />;
      default:
        return <Shield className="h-8 w-8" />;
    }
  };

  const renderPermissionsStep = () => (
    <div className="space-y-4">
      <div className="text-center">
        <div className="mx-auto mb-4 p-3 bg-muted rounded-full w-fit">
          {getServiceIcon()}
        </div>
        <h3 className="text-lg font-semibold">Connect to {serviceConfig?.displayName}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {serviceConfig?.description}
        </p>
      </div>

      <Separator />

      <div>
        <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Permissions Required
        </h4>
        <div className="space-y-2">
          {serviceConfig?.scopes.map((scope) => (
            <div key={scope} className="flex items-center gap-2 text-sm">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <Badge variant="outline" className="text-xs">
                {scope}
              </Badge>
              <span className="text-muted-foreground">
                {getScopeDescription(serviceId, scope)}
              </span>
            </div>
          ))}
        </div>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your credentials are stored securely and never shared with Alpine Intellect.
          You can revoke access at any time from your {serviceConfig?.displayName} account settings.
        </AlertDescription>
      </Alert>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
          Cancel
        </Button>
        <Button onClick={handleAuthenticate} disabled={loading} className="flex-1">
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <ExternalLink className="h-4 w-4 mr-2" />
          )}
          Connect to {serviceConfig?.displayName}
        </Button>
      </div>
    </div>
  );

  const renderAuthenticatingStep = () => (
    <div className="text-center space-y-4">
      <div className="mx-auto mb-4 p-3 bg-muted rounded-full w-fit">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
      <h3 className="text-lg font-semibold">Preparing Authentication</h3>
      <p className="text-sm text-muted-foreground">
        Setting up secure connection to {serviceConfig?.displayName}...
      </p>
    </div>
  );

  const renderWaitingStep = () => (
    <div className="text-center space-y-4">
      <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
        <ExternalLink className="h-8 w-8 text-blue-600" />
      </div>
      <h3 className="text-lg font-semibold">Complete Authentication</h3>
      <p className="text-sm text-muted-foreground">
        A browser window has opened. Please complete the authentication process in your browser.
      </p>
      
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          If the browser didn't open automatically, you can{' '}
          <Button
            variant="link"
            className="p-0 h-auto text-blue-600"
            onClick={() => authUrl && window.api.openExternal(authUrl)}
          >
            click here to open it manually
          </Button>
        </AlertDescription>
      </Alert>

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
          Cancel
        </Button>
        <Button variant="outline" onClick={() => setStep('permissions')} className="flex-1">
          Try Again
        </Button>
      </div>
    </div>
  );

  if (!serviceConfig) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Service Authentication</DialogTitle>
          <DialogDescription>
            Connect your {serviceConfig.displayName} account to enable MCP tools and features.
          </DialogDescription>
        </DialogHeader>

        {step === 'permissions' && renderPermissionsStep()}
        {step === 'authenticating' && renderAuthenticatingStep()}
        {step === 'waiting' && renderWaitingStep()}
      </DialogContent>
    </Dialog>
  );
}

function getScopeDescription(serviceId: string | null, scope: string): string {
  const descriptions: Record<string, Record<string, string>> = {
    github: {
      'repo': 'Access to repositories',
      'user': 'Read user profile information',
      'read:org': 'Read organization membership'
    },
    figma: {
      'file:read': 'Read design files',
      'file:write': 'Modify design files',
      'team:read': 'Access team information'
    }
  };

  return descriptions[serviceId || '']?.[scope] || 'Required for service functionality';
}
