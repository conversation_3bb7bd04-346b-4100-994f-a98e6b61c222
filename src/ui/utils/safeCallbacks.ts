export type IdleCallbackHandle = number | NodeJS.Timeout;

export function safeIdleCallback(
  callback: () => void,
  options: { timeout?: number } = {}
): IdleCallbackHandle {
  if ('requestIdleCallback' in window) {
    return (window as any).requestIdleCallback(callback, options);
  }
  return setTimeout(callback, options.timeout ?? 200) as IdleCallbackHandle;
}

export function cancelSafeIdleCallback(handle: IdleCallbackHandle) {
  if ('cancelIdleCallback' in window) {
    (window as any).cancelIdleCallback(handle);
  } else {
    clearTimeout(handle as NodeJS.Timeout);
  }
}


/**
 * Safe version of requestAnimationFrame with fallback to setTimeout (~60fps).
 */
export function safeAnimationFrame(callback: FrameRequestCallback): number {
  return typeof requestAnimationFrame === 'function'
    ? requestAnimationFrame(callback)
    : setTimeout(() => callback(performance.now()), 16) as unknown as number; // fallback
}


/**
 * Safe version of cancelAnimationFrame with fallback to clearTimeout.
 */
export function cancelSafeAnimationFrame(id: number) {
  if (typeof cancelAnimationFrame === 'function') {
    cancelAnimationFrame(id);
  } else {
    clearTimeout(id);
  }
}