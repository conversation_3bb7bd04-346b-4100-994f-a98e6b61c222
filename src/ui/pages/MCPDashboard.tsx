import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Separator } from '../components/ui/separator';
import { Layout } from '../components/Layout';
import { GitHubServiceCard } from '../components/GitHubServiceCard';
import { FigmaServiceCard } from '../components/FigmaServiceCard';
import { ServiceAuthDialog } from '../components/ServiceAuthDialog';
import { MCPServiceStatus, MCPHostStats, MCPHealthCheck, MCP_SERVICE_CONFIGS } from '../types/mcp-types';
import { Activity, Server, Zap, AlertCircle, CheckCircle } from 'lucide-react';

export function MCPDashboard() {
  const [services, setServices] = useState<MCPServiceStatus[]>([]);
  const [hostStats, setHostStats] = useState<MCPHostStats | null>(null);
  const [healthCheck, setHealthCheck] = useState<MCPHealthCheck | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
    
    // Set up OAuth callback listeners
    window.api.onMcpOAuthCallbackSuccess((data) => {
      console.log('OAuth success:', data);
      loadDashboardData(); // Refresh data after successful auth
    });

    window.api.onMcpOAuthCallbackError((data) => {
      console.error('OAuth error:', data);
      setError(`Authentication failed: ${data.error}`);
    });

    return () => {
      window.api.removeMcpListeners();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all services status
      const servicesResult = await window.api.mcpGetAllServicesStatus();
      if (servicesResult.success && servicesResult.statuses) {
        setServices(servicesResult.statuses);
      } else {
        throw new Error(servicesResult.error || 'Failed to load services');
      }

      // Load host stats
      const statsResult = await window.api.mcpGetHostStats();
      if (statsResult.success && statsResult.stats) {
        setHostStats(statsResult.stats);
      }

      // Perform health check
      const healthResult = await window.api.mcpPerformHealthCheck();
      if (healthResult.success && healthResult.healthCheck) {
        setHealthCheck(healthResult.healthCheck);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleServiceAuth = async (serviceId: string) => {
    setSelectedService(serviceId);
    setAuthDialogOpen(true);
  };

  const handleServiceToggle = async (serviceId: string, enabled: boolean) => {
    try {
      const result = enabled 
        ? await window.api.mcpEnableService(serviceId)
        : await window.api.mcpDisableService(serviceId);
      
      if (result.success) {
        await loadDashboardData(); // Refresh data
      } else {
        setError(result.error || 'Failed to toggle service');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle service');
    }
  };

  const formatUptime = (uptime: number) => {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <Layout title="MCP Services - Alpine Intellect">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading MCP Dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="MCP Services - Alpine Intellect">
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white mb-1 font-titling">MCP Services</h1>
            <p className="text-slate-400 text-sm">
              Manage your Model Context Protocol service integrations
            </p>
          </div>
          <Button onClick={loadDashboardData} variant="outline">
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">System Status</CardTitle>
            {healthCheck?.healthy ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {healthCheck?.healthy ? 'Healthy' : 'Issues'}
            </div>
            <p className="text-xs text-slate-400">
              {hostStats && `Uptime: ${formatUptime(hostStats.uptime)}`}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Total Services</CardTitle>
            <Server className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {hostStats?.serviceStats.totalServices || 0}
            </div>
            <p className="text-xs text-slate-400">
              {hostStats?.serviceStats.enabledServices || 0} enabled
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Authenticated</CardTitle>
            <CheckCircle className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {hostStats?.serviceStats.authenticatedServices || 0}
            </div>
            <p className="text-xs text-slate-400">
              Services connected
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Active Tools</CardTitle>
            <Zap className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {services.reduce((total, service) => total + service.availableTools.length, 0)}
            </div>
            <p className="text-xs text-slate-400">
              Available for use
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {services.map((service) => {
              if (service.serviceId === 'github') {
                return (
                  <GitHubServiceCard
                    key={service.serviceId}
                    service={service}
                    onAuth={() => handleServiceAuth(service.serviceId)}
                    onToggle={(enabled) => handleServiceToggle(service.serviceId, enabled)}
                  />
                );
              } else if (service.serviceId === 'figma') {
                return (
                  <FigmaServiceCard
                    key={service.serviceId}
                    service={service}
                    onAuth={() => handleServiceAuth(service.serviceId)}
                    onToggle={(enabled) => handleServiceToggle(service.serviceId, enabled)}
                  />
                );
              }
              return null;
            })}
          </div>
        </TabsContent>

        <TabsContent value="tools" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.values(MCP_SERVICE_CONFIGS).map((config) => {
              const service = services.find(s => s.serviceId === config.serviceId);
              const isAuthenticated = service?.authenticated || false;
              
              return (
                <Card key={config.serviceId} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-white">
                      {config.displayName} Tools
                      <Badge variant={isAuthenticated ? "default" : "secondary"}>
                        {isAuthenticated ? "Available" : "Requires Auth"}
                      </Badge>
                    </CardTitle>
                    <CardDescription className="text-slate-400">{config.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {config.tools.map((tool) => (
                        <div key={tool.name} className="flex items-center justify-between p-2 border border-slate-600 rounded bg-slate-700">
                          <div>
                            <div className="font-medium text-white">{tool.name}</div>
                            <div className="text-sm text-slate-400">{tool.description}</div>
                          </div>
                          <Badge variant={isAuthenticated ? "default" : "outline"}>
                            {isAuthenticated ? "Ready" : "Locked"}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">System Monitoring</CardTitle>
              <CardDescription className="text-slate-400">Real-time status and performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              {hostStats && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-slate-300">Uptime</div>
                      <div className="text-2xl font-bold text-white">{formatUptime(hostStats.uptime)}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-slate-300">Cache Size</div>
                      <div className="text-2xl font-bold text-white">{hostStats.cacheStats.size}</div>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <div className="text-sm font-medium mb-2 text-slate-300">Service Health</div>
                    <div className="space-y-2">
                      {healthCheck?.services.map((service) => (
                        <div key={service.serviceId} className="flex items-center justify-between">
                          <span className="text-slate-300">{service.serviceId}</span>
                          <Badge variant={service.healthy ? "default" : "destructive"}>
                            {service.healthy ? "Healthy" : "Unhealthy"}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <ServiceAuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        serviceId={selectedService}
        onSuccess={() => {
          setAuthDialogOpen(false);
          loadDashboardData();
        }}
      />
      </div>
    </Layout>
  );
}

export default MCPDashboard;
