/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_CONSOLE_URL: string;
  // add other env variables here
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare global {
  interface Window {
    api: {
      createLoginSession: (source: string, redirectUri: string) => Promise<string>;
      openExternal: (url: string) => void;
      openFolderDialog: () => Promise<string | null>;
      getSourceFiles: (folderPath: string) => Promise<{ name: string; content: string; }[]>;
      saveFile: (folderPath: string, fileName: string, content: string) => Promise<{ success: boolean; path?: string; error?: string }>;
      onAuthSuccess: (callback: (token: string) => void) => void;
      removeAuthListeners: () => void;
      scanIDEs: () => Promise<{ name: string, version: string | null, status: string }[]>;
      getAuthToken: () => Promise<string | null>;
      logout: () => Promise<{ success: boolean; error?: any }>;
      getIDEPlugins: (ideName: string) => Promise<string[]>;
      checkPluginUpdate: (ide: string) => Promise<{
        installedVersion: string | null;
        latestVersion: string;
        updateAvailable: boolean;
        downloadUrl: string;
      }>;
      downloadPlugin: (url: string) => Promise<{ success: boolean; path?: string; error?: string }>;
      installPlugin: (filePath: string, ide: string) => Promise<{ success: boolean; error?: string }>;
      onIndexingProgress: (callback: (data: any) => void) => void;
      onIndexingMeta: (callback: (data: any) => void) => void;
      loadExistingMeta?: () => Promise<MetaUpdate>;
      removeIndex: (path: string) => Promise<{ success: boolean; error?: string }>;
      reindex: (dirPath: string) => Promise<{ success: boolean; error?: string }>;
      resumeIndexing: (dirPath: string) => Promise<{ success: boolean; error?: string }>;
      pauseIndexing: (dirPath: string, curProgress: number) => Promise<{ success: boolean; error?: string }>;
      getModelConfiguration: () => Promise<ModelConfiguration>;
      saveModelConfiguration: (config: ModelConfiguration) => Promise<{ success: boolean }>;
      toggleModel: (modelId: string, modelType: 'llm' | 'embedding') => Promise<{ success: boolean }>;
      setPreferredModel: (type: string, modelId: string) => Promise<{ success: boolean }>;
      syncWithPlugins: () => Promise<{ success: boolean }>;
      clearModelCache: (modelId: string) => Promise<{ success: boolean }>;

      // MCP APIs
      mcpAuthenticateService: (serviceId: string) => Promise<{ success: boolean; authUrl?: string; error?: string }>;
      mcpGetServiceStatus: (serviceId: string) => Promise<{ success: boolean; status?: any; error?: string }>;
      mcpGetAllServicesStatus: () => Promise<{ success: boolean; statuses?: any[]; error?: string }>;
      mcpExecuteTool: (serviceId: string, toolName: string, parameters: any) => Promise<{ success: boolean; result?: any; error?: string }>;
      mcpEnableService: (serviceId: string) => Promise<{ success: boolean; error?: string }>;
      mcpDisableService: (serviceId: string) => Promise<{ success: boolean; error?: string }>;
      mcpGetHostStats: () => Promise<{ success: boolean; stats?: any; error?: string }>;
      mcpPerformHealthCheck: () => Promise<{ success: boolean; healthCheck?: any; error?: string }>;
      mcpGetToolExecutionMetrics: (timeWindowMs?: number) => Promise<{ success: boolean; metrics?: any; error?: string }>;

      // MCP Event Listeners
      onMcpOAuthCallbackSuccess: (callback: (data: any) => void) => void;
      onMcpOAuthCallbackError: (callback: (data: any) => void) => void;
      removeMcpListeners: () => void;
    };
  }
}

export {};