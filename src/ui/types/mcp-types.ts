// Global TypeScript interfaces for MCP functionality accessible to frontend

export interface MCPServiceStatus {
  serviceId: string;
  displayName: string;
  enabled: boolean;
  authenticated: boolean;
  healthy: boolean;
  authStatus: {
    authenticated: boolean;
    expiresAt?: Date;
    scopes?: string[];
    error?: string;
  };
  availableTools: string[];
  endpoints: {
    http: string;
    sse: string;
    discovery: string;
  };
}

export interface MCPAuthResult {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface MCPToolResult {
  success: boolean;
  result?: {
    content: Array<{
      type: 'text' | 'image' | 'resource';
      text?: string;
      data?: string;
      mimeType?: string;
    }>;
    isError?: boolean;
  };
  error?: string;
}

export interface MCPHostStats {
  uptime: number;
  configPath: string;
  discoveryEnabled: boolean;
  serviceStats: {
    totalServices: number;
    enabledServices: number;
    authenticatedServices: number;
    healthyServices: number;
    services: Array<{
      serviceId: string;
      displayName: string;
      enabled: boolean;
      authenticated: boolean;
      healthy: boolean;
      lastError?: string;
    }>;
  };
  cacheStats: {
    size: number;
    maxEntries: number;
    ttl: number;
    oldestEntry?: Date;
    newestEntry?: Date;
  };
}

export interface MCPHealthCheck {
  healthy: boolean;
  services: Array<{
    serviceId: string;
    healthy: boolean;
    authenticated: boolean;
    error?: string;
  }>;
}

export interface MCPOAuthCallbackData {
  url: string;
  error?: string;
}

export interface MCPServiceConfig {
  serviceId: string;
  displayName: string;
  description: string;
  iconUrl?: string;
  scopes: string[];
  tools: Array<{
    name: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
}

// GitHub-specific types
export interface GitHubToolParams {
  createRepository: {
    name: string;
    description?: string;
    private?: boolean;
    auto_init?: boolean;
  };
  createIssue: {
    owner: string;
    repo: string;
    title: string;
    body?: string;
    labels?: string[];
    assignees?: string[];
  };
  searchCode: {
    query: string;
    sort?: 'indexed' | 'updated';
    order?: 'asc' | 'desc';
    per_page?: number;
    page?: number;
  };
  getFileContent: {
    owner: string;
    repo: string;
    path: string;
    ref?: string;
  };
}

// Figma-specific types
export interface FigmaToolParams {
  getFileContent: {
    file_key: string;
    version?: string;
    ids?: string[];
    depth?: number;
    geometry?: 'paths' | 'bounds';
  };
  exportAssets: {
    file_key: string;
    ids: string[];
    format: 'jpg' | 'png' | 'svg' | 'pdf';
    scale?: number;
  };
  createComponent: {
    file_key: string;
    node_id: string;
    name: string;
    description?: string;
  };
  getTeamProjects: {
    team_id: string;
  };
}

// Service-specific configurations
export const MCP_SERVICE_CONFIGS: Record<string, MCPServiceConfig> = {
  github: {
    serviceId: 'github',
    displayName: 'GitHub',
    description: 'Connect to GitHub for repository management, issue tracking, and code collaboration',
    iconUrl: '/icons/github.svg',
    scopes: ['repo', 'user', 'read:org'],
    tools: [
      {
        name: 'create_repository',
        description: 'Create a new GitHub repository',
        parameters: {
          name: { type: 'string', required: true },
          description: { type: 'string', required: false },
          private: { type: 'boolean', required: false }
        }
      },
      {
        name: 'create_issue',
        description: 'Create a new issue in a repository',
        parameters: {
          owner: { type: 'string', required: true },
          repo: { type: 'string', required: true },
          title: { type: 'string', required: true },
          body: { type: 'string', required: false }
        }
      },
      {
        name: 'search_code',
        description: 'Search for code across GitHub repositories',
        parameters: {
          query: { type: 'string', required: true },
          sort: { type: 'string', required: false },
          order: { type: 'string', required: false }
        }
      },
      {
        name: 'get_file_content',
        description: 'Get the content of a file from a repository',
        parameters: {
          owner: { type: 'string', required: true },
          repo: { type: 'string', required: true },
          path: { type: 'string', required: true }
        }
      }
    ]
  },
  figma: {
    serviceId: 'figma',
    displayName: 'Figma',
    description: 'Connect to Figma for design file access, asset export, and team collaboration',
    iconUrl: '/icons/figma.svg',
    scopes: ['file:read', 'file:write', 'team:read'],
    tools: [
      {
        name: 'get_file_content',
        description: 'Get the content and structure of a Figma file',
        parameters: {
          file_key: { type: 'string', required: true },
          version: { type: 'string', required: false }
        }
      },
      {
        name: 'export_assets',
        description: 'Export assets from a Figma file',
        parameters: {
          file_key: { type: 'string', required: true },
          ids: { type: 'array', required: true },
          format: { type: 'string', required: true }
        }
      },
      {
        name: 'create_component',
        description: 'Create a component from a node in Figma',
        parameters: {
          file_key: { type: 'string', required: true },
          node_id: { type: 'string', required: true },
          name: { type: 'string', required: true }
        }
      },
      {
        name: 'get_team_projects',
        description: 'Get projects from a Figma team',
        parameters: {
          team_id: { type: 'string', required: true }
        }
      }
    ]
  }
};

// Note: Window API types are defined in vite-env.d.ts to avoid conflicts
