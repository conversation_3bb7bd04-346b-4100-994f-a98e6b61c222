
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import IDEIntegrator from "./pages/IDEIntegrator";
import Onboarding from "./pages/Onboarding";
import ModelManagement from "./pages/ModelManagement";
import IndexProjects from "./pages/IndexProjects";
import Search from "./pages/Search";
import Settings from "./pages/Settings";
import MCPDashboard from "./pages/MCPDashboard";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <HashRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/ide-integrator" element={<IDEIntegrator />} />
          <Route path="/onboarding" element={<Onboarding />} />
          <Route path="/models" element={<ModelManagement />} />
          <Route path="/projects" element={<IndexProjects />} />
          <Route path="/search" element={<Search />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/mcp" element={<MCPDashboard />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </HashRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
