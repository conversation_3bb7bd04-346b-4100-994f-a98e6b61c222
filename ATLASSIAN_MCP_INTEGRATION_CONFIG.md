# Atlassian MCP Integration Configuration

## Overview

This document provides the complete configuration for integrating Atlassian services (Jira, Confluence, and Bitbucket) into the Alpine Intellect MCP (Model Configuration Package) system. The integration follows the same OAuth 2.0 + PKCE + HTTP/SSE pattern established for GitHub integration.

## Supported Atlassian Services

### 1. Jira (Issue Tracking & Project Management)
- **Scopes**: `read:jira-work`, `write:jira-work`, `read:jira-user`
- **Tools**: 
  - `create_jira_issue` - Create new issues
  - `update_jira_issue` - Update existing issues
  - `search_jira_issues` - Search issues with JQL
  - `get_jira_issue` - Get issue details

### 2. Confluence (Team Collaboration & Documentation)
- **Scopes**: `read:confluence-content.all`, `write:confluence-content`, `read:confluence-space.summary`
- **Tools**:
  - `create_confluence_page` - Create new pages
  - `update_confluence_page` - Update existing pages
  - `search_confluence_content` - Search content
  - `get_confluence_page` - Get page details

### 3. Bitbucket (Git Repository Hosting)
- **Scopes**: `repository:read`, `repository:write`, `pullrequest:read`, `pullrequest:write`, `issue:read`, `issue:write`
- **Tools**:
  - `create_bitbucket_repository` - Create repositories
  - `create_bitbucket_pull_request` - Create pull requests
  - `search_bitbucket_code` - Search code
  - `get_bitbucket_file_content` - Get file content

## MCP Configuration

### JSON Configuration (mcp-config.json)

```json
{
  "servers": {
    "atlassian": {
      "transport": "http-sse",
      "enabled": true,
      "endpoints": {
        "http": "https://api.atlassian.com/mcp/v1",
        "sse": "https://api.atlassian.com/mcp/v1/events",
        "discovery": "https://api.atlassian.com/mcp/.well-known/oauth-authorization-server"
      },
      "auth": {
        "type": "oauth",
        "client_id": "${ENV:ATLASSIAN_CLIENT_ID}",
        "redirect_uri": "thealpinecode.alpineintellect://auth-callback/atlassian",
        "scopes": [
          "read:jira-work",
          "write:jira-work",
          "read:jira-user",
          "read:confluence-content.all",
          "write:confluence-content",
          "read:confluence-space.summary",
          "repository:read",
          "repository:write",
          "pullrequest:read",
          "pullrequest:write",
          "issue:read",
          "issue:write"
        ],
        "pkce": true,
        "authorization_endpoint": "https://auth.atlassian.com/authorize",
        "token_endpoint": "https://auth.atlassian.com/oauth/token"
      },
      "capabilities": [
        "tools",
        "resources",
        "notifications"
      ],
      "tools": [
        "create_jira_issue",
        "update_jira_issue",
        "search_jira_issues",
        "get_jira_issue",
        "create_confluence_page",
        "update_confluence_page",
        "search_confluence_content",
        "get_confluence_page",
        "create_bitbucket_repository",
        "create_bitbucket_pull_request",
        "search_bitbucket_code",
        "get_bitbucket_file_content",
        "list_accessible_resources"
      ],
      "connection": {
        "timeout": 30000,
        "retry_attempts": 3,
        "retry_delay": 1000
      },
      "cache": {
        "enabled": true,
        "ttl": 300000
      }
    }
  }
}
```

## OAuth 2.0 Configuration

### Environment Variables Required

```bash
# Atlassian OAuth App Configuration
ATLASSIAN_CLIENT_ID=your_atlassian_client_id_here

# Client secret stored securely in Keytar
# Key: alpine-app:atlassian-client-secret
# Value: your_atlassian_client_secret_here
```

### OAuth Endpoints

- **Authorization Endpoint**: `https://auth.atlassian.com/authorize`
- **Token Endpoint**: `https://auth.atlassian.com/oauth/token`
- **Redirect URI**: `thealpinecode.alpineintellect://auth-callback/atlassian`

### OAuth Scopes Breakdown

#### Jira Scopes
- `read:jira-work` - Read issues, projects, and work items
- `write:jira-work` - Create and update issues, projects
- `read:jira-user` - Read user information and permissions

#### Confluence Scopes  
- `read:confluence-content.all` - Read all content across spaces
- `write:confluence-content` - Create and update pages and content
- `read:confluence-space.summary` - Read space information

#### Bitbucket Scopes
- `repository:read` - Read repository content and metadata
- `repository:write` - Create repositories and push content
- `pullrequest:read` - Read pull requests and reviews
- `pullrequest:write` - Create and update pull requests
- `issue:read` - Read repository issues
- `issue:write` - Create and update repository issues

## API Endpoints

### Base URLs
- **Atlassian API**: `https://api.atlassian.com`
- **MCP HTTP Endpoint**: `https://api.atlassian.com/mcp/v1`
- **MCP SSE Endpoint**: `https://api.atlassian.com/mcp/v1/events`

### Tool-to-API Mapping

#### Jira Tools
- `create_jira_issue` → `POST /ex/jira/{cloudId}/rest/api/3/issue`
- `update_jira_issue` → `PUT /ex/jira/{cloudId}/rest/api/3/issue/{issueIdOrKey}`
- `search_jira_issues` → `POST /ex/jira/{cloudId}/rest/api/3/search`
- `get_jira_issue` → `GET /ex/jira/{cloudId}/rest/api/3/issue/{issueIdOrKey}`

#### Confluence Tools
- `create_confluence_page` → `POST /ex/confluence/{cloudId}/wiki/rest/api/content`
- `update_confluence_page` → `PUT /ex/confluence/{cloudId}/wiki/rest/api/content/{id}`
- `search_confluence_content` → `GET /ex/confluence/{cloudId}/wiki/rest/api/search`
- `get_confluence_page` → `GET /ex/confluence/{cloudId}/wiki/rest/api/content/{id}`

#### Bitbucket Tools
- `create_bitbucket_repository` → `POST /2.0/repositories/{workspace}`
- `create_bitbucket_pull_request` → `POST /2.0/repositories/{workspace}/{repo_slug}/pullrequests`
- `search_bitbucket_code` → `GET /2.0/repositories/{workspace}/{repo_slug}/src/{commit}/{path}`
- `get_bitbucket_file_content` → `GET /2.0/repositories/{workspace}/{repo_slug}/src/{commit}/{path}`

#### Resource Discovery
- `list_accessible_resources` → `GET /oauth/token/accessible-resources`

## Security Considerations

### PKCE Implementation
- **Code Verifier**: 128-character cryptographically secure random string
- **Code Challenge**: SHA256 hash of code verifier, base64url encoded
- **Code Challenge Method**: `S256`

### Token Storage
- **Access Token**: Stored in Keytar as `alpine-app:atlassian-access-token`
- **Refresh Token**: Stored in Keytar as `alpine-app:atlassian-refresh-token`
- **Client Secret**: Stored in Keytar as `alpine-app:atlassian-client-secret`

### State Parameter
- **Purpose**: CSRF protection
- **Generation**: 32-character cryptographically secure random string
- **Validation**: Must match stored value during callback processing

## Error Handling

### OAuth Errors
- `access_denied` - User denied authorization
- `invalid_client` - Invalid client configuration
- `invalid_grant` - Invalid authorization code
- `invalid_scope` - Requested scope not available

### API Errors
- `401 Unauthorized` - Token expired or invalid
- `403 Forbidden` - Insufficient permissions
- `429 Too Many Requests` - Rate limit exceeded
- `503 Service Unavailable` - Atlassian service down

## Real-time Notifications (SSE)

### Supported Events
- `jira:issue:updated` - Jira issue changes
- `confluence:page:updated` - Confluence page changes
- `bitbucket:repository:push` - Repository push events
- `bitbucket:pullrequest:updated` - Pull request changes

### Event Handling
- **Auto-reconnection**: Enabled with exponential backoff
- **Max Reconnect Attempts**: 5
- **Reconnect Delay**: 2000ms initial, exponential backoff

## Integration with Code Assistant Agent

### Capability Detection
```typescript
const atlassianStatus = await this.mcpHost.getServiceStatus('atlassian');

if (atlassianStatus.authenticated && atlassianStatus.healthy) {
  this.capabilities = {
    issueTracking: atlassianStatus.availableTools.includes('create_jira_issue'),
    documentation: atlassianStatus.availableTools.includes('create_confluence_page'),
    repositoryManagement: atlassianStatus.availableTools.includes('create_bitbucket_repository'),
    codeSearch: atlassianStatus.availableTools.includes('search_bitbucket_code')
  };
}
```

### Tool Execution Flow
1. **Authentication Check**: Verify valid access token
2. **Scope Validation**: Ensure token has required scopes
3. **Resource Context**: Include accessible resource IDs
4. **API Call**: Execute tool with proper authentication
5. **Error Handling**: Handle rate limits and permissions
6. **Result Processing**: Format response for agent consumption

## Testing Strategy

### Unit Tests
- OAuth flow components (PKCE, token exchange, refresh)
- HTTP/SSE transport functionality
- Service client method implementations
- Error handling scenarios

### Integration Tests
- End-to-end OAuth flow with test credentials
- API tool execution against Atlassian test environment
- Real-time notification handling
- Multi-service coordination (Jira + Confluence + Bitbucket)

### Manual Testing
- User OAuth flow with real Atlassian account
- Code Assistant Agent tool usage
- Error recovery scenarios
- Performance under load

## Deployment Checklist

### Prerequisites
- [ ] Atlassian OAuth app registered
- [ ] Client ID and secret configured
- [ ] Redirect URI whitelisted
- [ ] Environment variables set

### Implementation Files
- [ ] `src/mcp/auth/atlassian-oauth-service.ts`
- [ ] `src/mcp/clients/atlassian-service.ts`
- [ ] `src/mcp/transport/atlassian-http-sse-transport.ts`
- [ ] `src/mcp/auth/atlassian-oauth-callback-processor.ts`

### Verification
- [ ] OAuth flow completes successfully
- [ ] All tools execute without errors
- [ ] Real-time notifications work
- [ ] Error handling functions properly
- [ ] Security audit passed
