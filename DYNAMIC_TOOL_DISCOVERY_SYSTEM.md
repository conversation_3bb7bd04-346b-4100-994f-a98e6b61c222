# Dynamic Tool Discovery and Execution System

## Overview

This document describes the comprehensive dynamic tool discovery and execution system implemented for the GitHub MCP service. The system provides a scalable, extensible architecture that can automatically discover, validate, and execute tools from MCP services without hardcoding tool definitions.

## Architecture Components

### 1. Core Types and Interfaces (`tool-discovery-types.ts`)

**Key Types:**
- `MCPToolDefinition`: Complete tool schema with input/output definitions
- `MCPToolMetadata`: Enhanced metadata including scopes, categories, examples
- `ToolExecutionContext`: Runtime context for tool execution
- `IDynamicToolService`: Interface for dynamic tool services
- `IToolDiscoveryClient`: Interface for tool discovery clients
- `IToolExecutor`: Interface for tool executors

**Features:**
- Comprehensive parameter validation schemas
- Tool categorization system (14 predefined categories)
- Deprecation tracking and warnings
- Rate limiting metadata
- Execution metrics and history

### 2. Abstract Base Classes

#### `AbstractToolDiscoveryClient` (`abstract-tool-discovery-client.ts`)
- **Purpose**: Base class for service-specific tool discovery
- **Features**:
  - Automatic retry logic with exponential backoff
  - Caching with configurable TTL
  - Parameter validation with detailed error reporting
  - Event-driven architecture for discovery updates
  - Schema validation for discovered tools

#### `AbstractToolExecutor` (`abstract-tool-executor.ts`)
- **Purpose**: Base class for service-specific tool execution
- **Features**:
  - Execution history tracking
  - Timeout handling
  - Parameter validation before execution
  - Standardized error handling
  - Metrics collection

### 3. GitHub-Specific Implementation

#### `GitHubToolDiscoveryClient` (`github-tool-discovery-client.ts`)
- **Purpose**: Discovers tools from GitHub MCP API endpoints
- **Features**:
  - Dynamic tool discovery from `/tools/list` endpoint
  - Automatic categorization of GitHub tools
  - Scope determination based on tool functionality
  - Rate limit metadata for GitHub API
  - Example generation for common tools

**Supported Tool Categories:**
- Repository Management: `create_repository`, `list_repositories`, etc.
- Issue Tracking: `create_issue`, `update_issue`, etc.
- Pull Requests: `create_pull_request`, `merge_pull_request`, etc.
- Code Search: `search_code`, `search_repositories`
- File Operations: `get_file_content`, `create_file`, etc.
- Branch Operations: `create_branch`, `merge_branch`, etc.
- Organization/Team Management: `list_organizations`, `add_team_member`, etc.

#### `GitHubToolExecutor` (`github-tool-executor.ts`)
- **Purpose**: Executes GitHub tools via MCP protocol
- **Features**:
  - GitHub-specific response formatting
  - Enhanced error handling for GitHub API errors
  - Authentication error detection and reporting
  - Rate limit handling
  - Response summarization for complex objects

### 4. Tool Metadata Management

#### `ToolMetadataManager` (`tool-metadata-manager.ts`)
- **Purpose**: Centralized metadata management across services
- **Features**:
  - Multi-service tool registry
  - Category-based tool organization
  - Search and filtering capabilities
  - Deprecation tracking
  - Auto-refresh scheduling
  - Event-driven updates

**Key Methods:**
- `registerService()`: Register a new service
- `updateServiceTools()`: Update tools for a service
- `searchTools()`: Search tools by query
- `getToolsByCategory()`: Get tools grouped by category
- `filterToolsByScopes()`: Filter tools by required OAuth scopes

### 5. Dynamic Tool Service Integration

#### `DynamicToolService` (`dynamic-tool-service.ts`)
- **Purpose**: Integrates discovery, metadata, and execution
- **Features**:
  - Automatic tool discovery on initialization
  - Scheduled tool refresh
  - Execution queue management
  - Cross-service compatibility
  - Event forwarding and aggregation

**Configuration Options:**
- `autoDiscovery`: Enable automatic tool discovery
- `discoveryInterval`: How often to refresh tools
- `maxConcurrentExecutions`: Limit concurrent tool executions
- `enableMetrics`: Track execution metrics
- `enableCaching`: Cache discovery results

### 6. Scalable Execution Engine

#### `ScalableToolExecutionEngine` (`scalable-tool-execution-engine.ts`)
- **Purpose**: High-performance, multi-service tool execution
- **Features**:
  - Concurrent execution with configurable limits
  - Priority-based execution queue
  - Rate limiting per service
  - Retry logic with exponential backoff
  - Comprehensive metrics and monitoring
  - Batch execution support
  - Cross-service tool search

**Advanced Features:**
- **Queue Management**: Priority-based execution queue with overflow handling
- **Rate Limiting**: Per-service rate limiting with sliding windows
- **Metrics Collection**: Detailed execution metrics and performance tracking
- **Error Handling**: Categorized error handling with retry strategies
- **Service Discovery**: Cross-service tool discovery and search

### 7. Enhanced GitHub Service

#### Updated `GitHubService` (`github-service.ts`)
- **Integration**: Now uses dynamic tool discovery system
- **Backward Compatibility**: Maintains existing tool methods
- **New Features**:
  - `getToolsByCategory()`: Get tools organized by category
  - `searchTools()`: Search available tools
  - `refreshTools()`: Manually refresh tool discovery
  - `isToolAvailable()`: Check tool availability
  - `getServiceStats()`: Get service statistics

## Usage Examples

### 1. Basic Tool Discovery

```typescript
// Initialize metadata manager
const metadataManager = new ToolMetadataManager({
  enableCategoryGrouping: true,
  enableDeprecationTracking: true
});

// Create GitHub discovery client
const discoveryClient = new GitHubToolDiscoveryClient({
  endpoints: {
    http: 'https://api.githubcopilot.com/mcp/',
    sse: 'https://api.githubcopilot.com/mcp/events'
  },
  getAccessToken: async () => await getGitHubToken()
});

// Discover tools
const result = await discoveryClient.discoverTools();
console.log(`Discovered ${result.tools?.length} tools`);
```

### 2. Tool Execution

```typescript
// Create execution engine
const executionEngine = new ScalableToolExecutionEngine(metadataManager, {
  maxConcurrentExecutions: 5,
  enableMetrics: true
});

// Execute a tool
const result = await executionEngine.executeTool(
  'github',
  'create_repository',
  {
    name: 'my-new-repo',
    description: 'A test repository',
    private: false
  }
);
```

### 3. Cross-Service Tool Search

```typescript
// Search tools across all services
const searchResults = executionEngine.searchTools('repository');
console.log(`Found ${searchResults.length} repository-related tools`);

// Get tools by category
const categories = executionEngine.getToolsByCategory();
for (const [category, tools] of categories) {
  console.log(`${category}: ${tools.length} tools`);
}
```

## Integration Testing

The system includes comprehensive integration tests in `integration-test.ts`:

### Test Coverage
- **Tool Metadata Manager**: Registry management, search, categorization
- **Dynamic Tool Discovery**: Discovery client functionality, validation
- **Tool Execution**: Parameter validation, execution flow
- **Scalable Execution Engine**: Queue management, metrics, rate limiting
- **Cross-Service Compatibility**: Multi-service operations
- **Error Handling**: Edge cases and error scenarios

### Running Tests

```typescript
// Run all integration tests
const test = new MCPIntegrationTest();
await test.runTests();

// Run only dynamic tool discovery tests
await runDynamicToolDiscoveryTests();
```

## Benefits

### 1. **Dynamic Discovery**
- No hardcoded tool lists
- Automatic discovery of new tools
- Real-time tool availability updates

### 2. **Scalability**
- Multi-service architecture
- Concurrent execution with queue management
- Rate limiting and resource management

### 3. **Extensibility**
- Abstract base classes for new services
- Plugin-like architecture for tool executors
- Configuration-driven behavior

### 4. **Reliability**
- Comprehensive error handling
- Retry logic with exponential backoff
- Health monitoring and metrics

### 5. **Developer Experience**
- Rich metadata for tools
- Search and categorization
- Detailed validation and error messages

## Future Enhancements

### 1. **Additional Services**
- Figma tool discovery and execution
- Atlassian/Jira integration
- Slack/Discord integrations

### 2. **Advanced Features**
- Tool composition and workflows
- Conditional execution based on context
- Tool recommendation system

### 3. **Performance Optimizations**
- Persistent caching layer
- Background tool discovery
- Predictive tool loading

### 4. **Monitoring and Analytics**
- Tool usage analytics
- Performance monitoring dashboard
- Error tracking and alerting

## Configuration

The system is highly configurable through various configuration objects:

```typescript
// Tool Discovery Configuration
const discoveryConfig = {
  enabled: true,
  discoveryInterval: 300000, // 5 minutes
  cacheEnabled: true,
  cacheTTL: 600000, // 10 minutes
  maxRetries: 3,
  retryDelay: 1000,
  validateSchemas: true,
  enableDeprecationWarnings: true
};

// Execution Engine Configuration
const executionConfig = {
  maxConcurrentExecutions: 10,
  maxQueueSize: 100,
  defaultTimeout: 30000,
  enableMetrics: true,
  enableRetries: true,
  enableRateLimiting: true,
  rateLimitWindow: 60000,
  rateLimitRequests: 100
};
```

This dynamic tool discovery and execution system provides a robust, scalable foundation for MCP service integration that can grow with your application's needs while maintaining high performance and reliability.
