# GitHub OAuth Authentication Flow - Technical Implementation Guide (Phase 1)

## Overview

This document provides a complete, stage-by-stage technical implementation guide for OAuth 2.0 with PKCE authentication flow for GitHub integration in Alpine Intellect MCP system. This implementation follows the corrected patterns from the existing Alpine Intellect codebase using the `thealpinecode.alpineintellect://` protocol.

## Stage 1: Initial Authentication Trigger

### User Experience Flow

```
User Action → UI Component → Auth Controller → OAuth Service → Browser Launch
```

### Implementation

#### 1.1 UI Components

**File**: `src/ui/components/GitHubServiceCard.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { But<PERSON>, Card, Alert, Spinner } from '@/ui/components';

interface GitHubServiceCardProps {
  isAuthenticated: boolean;
  onAuthSuccess: () => void;
}

export const GitHubServiceCard: React.FC<GitHubServiceCardProps> = ({
  isAuthenticated,
  onAuthSuccess
}) => {
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    // Listen for GitHub OAuth callback (same pattern as existing Alpine auth)
    const handleGitHubOAuthCallback = (event: any, data: any) => {
      setIsAuthenticating(false);
      
      if (data.error) {
        setAuthError(`Authentication failed: ${data.error_description || data.error}`);
        return;
      }

      if (data.code && data.state) {
        // Process the OAuth callback
        processGitHubOAuthCallback(data.code, data.state);
      }
    };

    // Use SAME event pattern as existing auth
    window.electronAPI?.onGitHubOAuthCallback?.(handleGitHubOAuthCallback);

    return () => {
      // Cleanup listener
    };
  }, []);

  const handleConnectToGitHub = async () => {
    setIsAuthenticating(true);
    setAuthError(null);

    try {
      // Generate OAuth URL using corrected service
      const authUrl = await window.electronAPI?.generateGitHubOAuthUrl?.();
      
      if (!authUrl) {
        throw new Error('Failed to generate GitHub OAuth URL');
      }
      
      // Open browser (same pattern as existing OAuth)
      await window.electronAPI?.openExternal?.(authUrl);
      
      // OAuth flow continues in browser, completion handled by deep link
    } catch (error) {
      setAuthError(error.message);
      setIsAuthenticating(false);
    }
  };

  const processGitHubOAuthCallback = async (code: string, state: string) => {
    try {
      const result = await window.electronAPI?.exchangeGitHubOAuthCode?.(code, state);
      
      if (result.success) {
        setIsAuthenticated(true);
        setAuthError(null);
        onAuthSuccess();
        console.log('GitHub authentication successful');
      } else {
        setAuthError(result.error);
      }
      
    } catch (error) {
      setAuthError(error.message);
    }
  };

  const handleDisconnect = async () => {
    try {
      await window.electronAPI?.revokeGitHubAuthentication?.();
      onAuthSuccess(); // Refresh parent component
    } catch (error) {
      setAuthError(error.message);
    }
  };

  return (
    <Card className="github-service-card">
      <div className="service-header">
        <img src="/icons/github-logo.svg" alt="GitHub" className="service-logo" />
        <div className="service-info">
          <h3>GitHub</h3>
          <p>Repository management, issue tracking, and code collaboration</p>
        </div>
      </div>

      <div className="service-status">
        {isAuthenticated ? (
          <div className="authenticated-status">
            <span className="status-indicator connected">●</span>
            <span>Connected</span>
            <Button variant="secondary" onClick={handleDisconnect}>
              Disconnect
            </Button>
          </div>
        ) : (
          <div className="unauthenticated-status">
            <span className="status-indicator disconnected">●</span>
            <span>Not connected</span>
            <Button 
              variant="primary" 
              onClick={handleConnectToGitHub}
              disabled={isAuthenticating}
            >
              {isAuthenticating ? (
                <>
                  <Spinner size="sm" />
                  Connecting...
                </>
              ) : (
                'Connect to GitHub'
              )}
            </Button>
          </div>
        )}
      </div>

      {authError && (
        <Alert variant="error" className="auth-error">
          {authError}
        </Alert>
      )}

      <div className="service-capabilities">
        <h4>Available Tools:</h4>
        <ul>
          <li>Create and manage repositories</li>
          <li>Create and track issues</li>
          <li>Create and manage pull requests</li>
          <li>Search code across repositories</li>
          <li>Manage branches and commits</li>
        </ul>
      </div>
    </Card>
  );
};
```

## Stage 2: OAuth Flow Implementation

### 2.1 GitHub OAuth Service

**File**: `src/mcp/auth/github-oauth-service.ts`

```typescript
import crypto from 'crypto';
import * as keytar from 'keytar';

export class GitHubOAuthService {
  private clientId: string;
  private redirectUri: string;
  private scopes: string[];

  constructor() {
    this.clientId = process.env.GITHUB_CLIENT_ID!;
    // Use the SAME redirect URI pattern as existing Alpine auth
    this.redirectUri = 'thealpinecode.alpineintellect://auth-callback';
    this.scopes = ['repo', 'user', 'read:org'];
  }

  async generateAuthorizationUrl(): Promise<string> {
    // Generate PKCE parameters
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    const state = this.generateState();

    // Store PKCE parameters using SAME keytar service as existing auth
    await keytar.setPassword('alpine-app', 'github-pkce-verifier', codeVerifier);
    await keytar.setPassword('alpine-app', 'github-oauth-state', state);

    // Build authorization URL with CORRECT redirect URI
    const authUrl = new URL('https://github.com/login/oauth/authorize');
    authUrl.searchParams.set('client_id', this.clientId);
    authUrl.searchParams.set('redirect_uri', this.redirectUri);
    authUrl.searchParams.set('scope', this.scopes.join(' '));
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    return authUrl.toString();
  }

  async exchangeCodeForTokens(
    authorizationCode: string, 
    receivedState: string
  ): Promise<TokenResponse> {
    // Validate state parameter using SAME keytar service
    const storedState = await keytar.getPassword('alpine-app', 'github-oauth-state');
    if (receivedState !== storedState) {
      throw new Error('Invalid state parameter - possible CSRF attack');
    }

    // Get stored PKCE verifier using SAME keytar service
    const codeVerifier = await keytar.getPassword('alpine-app', 'github-pkce-verifier');
    if (!codeVerifier) {
      throw new Error('PKCE code verifier not found');
    }

    // Get client secret using SAME keytar service pattern
    const clientSecret = await keytar.getPassword('alpine-app', 'github-client-secret');
    if (!clientSecret) {
      throw new Error('GitHub client secret not configured');
    }

    // Exchange authorization code for tokens
    const response = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: this.clientId,
        client_secret: clientSecret,
        code: authorizationCode,
        redirect_uri: this.redirectUri,
        code_verifier: codeVerifier
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Token exchange failed: ${response.status} - ${errorData.error_description || 'Unknown error'}`);
    }

    const tokenData = await response.json();
    
    // Store tokens using SAME keytar service as existing auth
    await this.storeTokens(tokenData);

    // Clean up temporary storage
    await keytar.deletePassword('alpine-app', 'github-oauth-state');
    await keytar.deletePassword('alpine-app', 'github-pkce-verifier');

    return tokenData;
  }

  private async storeTokens(tokenResponse: any): Promise<void> {
    // GitHub tokens don't expire automatically, but we store metadata
    const issuedAt = Date.now();

    // Use SAME keytar service name as existing auth
    await Promise.all([
      keytar.setPassword('alpine-app', 'github-access-token', tokenResponse.access_token),
      keytar.setPassword('alpine-app', 'github-token-metadata', JSON.stringify({
        token_type: tokenResponse.token_type,
        scope: tokenResponse.scope,
        issued_at: issuedAt
      }))
    ]);
  }

  async getAccessToken(): Promise<string | null> {
    try {
      const token = await keytar.getPassword('alpine-app', 'github-access-token');
      
      if (token) {
        // Validate token by making a test API call
        const isValid = await this.validateToken(token);
        if (isValid) {
          return token;
        } else {
          // Token is invalid, clear it
          await this.revokeTokens();
          return null;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get GitHub access token:', error);
      return null;
    }
  }

  private async validateToken(token: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        }
      });
      
      return response.ok;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  async revokeTokens(): Promise<void> {
    try {
      // Clear all GitHub-related tokens
      await Promise.all([
        keytar.deletePassword('alpine-app', 'github-access-token'),
        keytar.deletePassword('alpine-app', 'github-token-metadata')
      ]);
      
      console.log('GitHub tokens revoked successfully');
    } catch (error) {
      console.error('Failed to revoke GitHub tokens:', error);
    }
  }

  private generateCodeVerifier(): string {
    const buffer = crypto.randomBytes(96);
    return buffer.toString('base64url');
  }

  private async generateCodeChallenge(codeVerifier: string): Promise<string> {
    const hash = crypto.createHash('sha256');
    hash.update(codeVerifier);
    return hash.digest('base64url');
  }

  private generateState(): string {
    const buffer = crypto.randomBytes(24);
    return buffer.toString('base64url');
  }
}

interface TokenResponse {
  access_token: string;
  token_type: string;
  scope: string;
}
```

## Stage 3: IDE Redirect Handling

### 3.1 Integration with Existing Deep Link Handler

**File**: `src/electron/main.ts` (Extend existing handleDeepLink function)

```typescript
// MODIFY existing handleDeepLink function in main.ts to support GitHub OAuth

function handleDeepLink(url: string) {
  try {
    const parsedUrl = new URL(url);
    
    // Handle existing Alpine auth token (keep existing functionality)
    const token = parsedUrl.searchParams.get("token");
    if (token) {
      console.log("🔐 Received Alpine Auth Token:", token);
      import('keytar').then(mod => {
        const keytar = mod.default;
        keytar.setPassword('alpine-app', 'auth-token', token);
      }).catch(console.error);

      if (mainWindow) {
        mainWindow.webContents.send("auth-success", token);
      } else {
        pendingAuthToken = token;
      }
      return; // Exit early for existing auth flow
    }

    // NEW: Handle GitHub OAuth callback
    const githubCode = parsedUrl.searchParams.get("code");
    const githubState = parsedUrl.searchParams.get("state");
    const githubError = parsedUrl.searchParams.get("error");
    
    // Check if this is a GitHub OAuth callback (has code/error but no token)
    if (githubCode || githubError) {
      console.log("🐙 Received GitHub OAuth callback");
      
      if (mainWindow) {
        // Show and focus the window (same pattern as existing auth)
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        mainWindow.setAlwaysOnTop(true);
        setTimeout(() => {
          if(mainWindow) mainWindow.setAlwaysOnTop(false)
        }, 1000);

        // Send GitHub OAuth result to renderer
        mainWindow.webContents.send("github-oauth-callback", {
          code: githubCode,
          state: githubState,
          error: githubError,
          error_description: parsedUrl.searchParams.get("error_description")
        });
      }
      return; // Exit early for GitHub OAuth flow
    }

    // Handle other potential OAuth callbacks here (Figma, etc.)
    
  } catch (error) {
    console.error("Deep link handling error:", error);
  }
}
```

### 3.2 IPC Handler Registration

**File**: `src/electron/main.ts` (Add to existing IPC handlers)

```typescript
// ADD to existing IPC handlers in main.ts

// GitHub OAuth URL generation
ipcMain.handle('generate-github-oauth-url', async () => {
  try {
    const { GitHubOAuthService } = await import('./mcp/auth/github-oauth-service');
    const githubOAuth = new GitHubOAuthService();
    return await githubOAuth.generateAuthorizationUrl();
  } catch (error) {
    console.error('Failed to generate GitHub OAuth URL:', error);
    throw error;
  }
});

// GitHub OAuth code exchange
ipcMain.handle('exchange-github-oauth-code', async (event, code: string, state: string) => {
  try {
    const { GitHubOAuthService } = await import('./mcp/auth/github-oauth-service');
    const githubOAuth = new GitHubOAuthService();
    const tokens = await githubOAuth.exchangeCodeForTokens(code, state);
    return { success: true, tokens };
  } catch (error) {
    console.error('Failed to exchange GitHub OAuth code:', error);
    return { success: false, error: error.message };
  }
});

// GitHub authentication revocation
ipcMain.handle('revoke-github-authentication', async () => {
  try {
    const { GitHubOAuthService } = await import('./mcp/auth/github-oauth-service');
    const githubOAuth = new GitHubOAuthService();
    await githubOAuth.revokeTokens();
    return { success: true };
  } catch (error) {
    console.error('Failed to revoke GitHub authentication:', error);
    return { success: false, error: error.message };
  }
});

// Check GitHub authentication status
ipcMain.handle('check-github-auth-status', async () => {
  try {
    const { GitHubOAuthService } = await import('./mcp/auth/github-oauth-service');
    const githubOAuth = new GitHubOAuthService();
    const token = await githubOAuth.getAccessToken();
    return { authenticated: !!token };
  } catch (error) {
    console.error('Failed to check GitHub auth status:', error);
    return { authenticated: false };
  }
});
```

### 3.3 Preload Script Updates

**File**: `src/electron/preload.cts` (Add to existing preload)

```typescript
// ADD to existing contextBridge.exposeInMainWorld("electronAPI", { ... });

contextBridge.exposeInMainWorld("electronAPI", {
  // ... existing methods ...

  // NEW: GitHub OAuth support
  generateGitHubOAuthUrl: () => ipcRenderer.invoke('generate-github-oauth-url'),

  exchangeGitHubOAuthCode: (code: string, state: string) =>
    ipcRenderer.invoke('exchange-github-oauth-code', code, state),

  revokeGitHubAuthentication: () =>
    ipcRenderer.invoke('revoke-github-authentication'),

  checkGitHubAuthStatus: () =>
    ipcRenderer.invoke('check-github-auth-status'),

  onGitHubOAuthCallback: (callback: (event: any, data: any) => void) => {
    ipcRenderer.on('github-oauth-callback', callback);
  },

  // Use existing openExternal method (already exists in codebase)
  openExternal: (url: string) => ipcRenderer.invoke('open-external', url),
});
```

## Stage 4: GitHub Remote Client Implementation

### 4.1 GitHub Remote Client with Authentication

**File**: `src/mcp/clients/github-remote-client.ts`

```typescript
import { MCPClient } from './base-mcp-client';
import { GitHubOAuthService } from '@/mcp/auth/github-oauth-service';

export class GitHubRemoteClient implements MCPClient {
  private githubOAuthService: GitHubOAuthService;
  private baseUrl = 'https://api.github.com';

  constructor() {
    this.githubOAuthService = new GitHubOAuthService();
  }

  async initialize(): Promise<void> {
    // Verify authentication on initialization
    const token = await this.githubOAuthService.getAccessToken();
    if (!token) {
      throw new Error('GitHub authentication required');
    }
  }

  async executeTool(toolName: string, parameters: any, token?: string): Promise<ToolResult> {
    const accessToken = token || await this.githubOAuthService.getAccessToken();
    if (!accessToken) {
      throw new Error('GitHub authentication required');
    }

    switch (toolName) {
      case 'create_repository':
        return await this.createRepository(parameters, accessToken);
      case 'create_issue':
        return await this.createIssue(parameters, accessToken);
      case 'create_pull_request':
        return await this.createPullRequest(parameters, accessToken);
      case 'search_code':
        return await this.searchCode(parameters, accessToken);
      case 'manage_branches':
        return await this.manageBranches(parameters, accessToken);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  private async createRepository(params: any, token: string): Promise<ToolResult> {
    try {
      const response = await fetch(`${this.baseUrl}/user/repos`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        },
        body: JSON.stringify({
          name: params.name,
          description: params.description,
          private: params.private || false,
          auto_init: params.auto_init || true,
          gitignore_template: params.gitignore_template
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`GitHub API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: {
          repository: {
            id: data.id,
            name: data.name,
            full_name: data.full_name,
            html_url: data.html_url,
            clone_url: data.clone_url,
            ssh_url: data.ssh_url
          },
          metadata: {
            created_at: data.created_at,
            private: data.private,
            default_branch: data.default_branch
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  private async createIssue(params: any, token: string): Promise<ToolResult> {
    try {
      const response = await fetch(`${this.baseUrl}/repos/${params.owner}/${params.repo}/issues`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        },
        body: JSON.stringify({
          title: params.title,
          body: params.body,
          labels: params.labels || [],
          assignees: params.assignees || []
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`GitHub API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: {
          issue: {
            id: data.id,
            number: data.number,
            title: data.title,
            html_url: data.html_url,
            state: data.state
          },
          metadata: {
            created_at: data.created_at,
            labels: data.labels,
            assignees: data.assignees
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  private async searchCode(params: any, token: string): Promise<ToolResult> {
    try {
      const query = encodeURIComponent(`${params.query} repo:${params.repo}`);
      const response = await fetch(`${this.baseUrl}/search/code?q=${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`GitHub API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: {
          total_count: data.total_count,
          items: data.items.map((item: any) => ({
            name: item.name,
            path: item.path,
            html_url: item.html_url,
            repository: item.repository.full_name,
            score: item.score
          })),
          metadata: {
            searched_at: Date.now(),
            query: params.query
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

## Stage 5: Error Handling

### 5.1 GitHub OAuth Error Handler

**File**: `src/mcp/error/github-oauth-error-handler.ts`

```typescript
export class GitHubOAuthErrorHandler {
  async handleAuthenticationError(error: AuthError, context: ErrorContext): Promise<ErrorRecovery> {
    switch (error.type) {
      case 'USER_DENIED_ACCESS':
        return this.handleUserDeniedAccess(error, context);
      case 'INVALID_CLIENT':
        return this.handleInvalidClient(error, context);
      case 'SERVER_ERROR':
        return this.handleServerError(error, context);
      case 'TOKEN_REVOKED':
        return this.handleTokenRevoked(error, context);
      case 'INSUFFICIENT_SCOPE':
        return this.handleInsufficientScope(error, context);
      case 'RATE_LIMITED':
        return this.handleRateLimit(error, context);
      case 'NETWORK_ERROR':
        return this.handleNetworkError(error, context);
      default:
        return this.handleGenericError(error, context);
    }
  }

  private handleUserDeniedAccess(error: AuthError, context: ErrorContext): ErrorRecovery {
    return {
      recovered: false,
      userMessage: 'GitHub access was denied. Please try connecting again if you want to use GitHub tools.',
      actionRequired: 'user_retry',
      retryable: true,
      suggestedActions: [
        'Click "Connect to GitHub" again',
        'Ensure you click "Authorize" on the GitHub authorization page'
      ]
    };
  }

  private handleInvalidClient(error: AuthError, context: ErrorContext): ErrorRecovery {
    return {
      recovered: false,
      userMessage: 'Configuration error with GitHub integration. Please contact support.',
      actionRequired: 'contact_support',
      retryable: false,
      technicalDetails: 'Invalid client credentials or configuration',
      suggestedActions: [
        'Contact technical support',
        'Check GitHub app configuration'
      ]
    };
  }

  private async handleTokenRevoked(error: AuthError, context: ErrorContext): Promise<ErrorRecovery> {
    // Clear stored tokens
    await context.githubOAuthService.revokeTokens();

    return {
      recovered: false,
      userMessage: 'GitHub access has been revoked. Please reconnect to GitHub.',
      actionRequired: 'reauthenticate',
      retryable: true,
      suggestedActions: [
        'Click "Connect to GitHub" to re-authenticate',
        'Check your GitHub account permissions'
      ]
    };
  }

  private async handleRateLimit(error: AuthError, context: ErrorContext): Promise<ErrorRecovery> {
    // Extract rate limit information from GitHub API response
    const resetTime = error.metadata?.resetTime || Date.now() + (60 * 60 * 1000); // 1 hour default
    const waitTime = resetTime - Date.now();

    return {
      recovered: false,
      userMessage: `GitHub API rate limit exceeded. Please wait ${Math.ceil(waitTime / 60000)} minutes.`,
      actionRequired: 'wait_and_retry',
      retryable: true,
      retryAfter: waitTime,
      suggestedActions: [
        `Wait until ${new Date(resetTime).toLocaleTimeString()}`,
        'Consider upgrading GitHub plan for higher rate limits'
      ]
    };
  }

  private async handleNetworkError(error: AuthError, context: ErrorContext): Promise<ErrorRecovery> {
    // Check network connectivity
    const isOnline = await this.checkNetworkConnectivity();

    if (!isOnline) {
      return {
        recovered: false,
        userMessage: 'Network connection required for GitHub integration.',
        actionRequired: 'check_network',
        retryable: true,
        suggestedActions: [
          'Check your internet connection',
          'Try again when network is available'
        ]
      };
    }

    return {
      recovered: false,
      userMessage: 'Connection to GitHub failed. Please try again.',
      actionRequired: 'retry',
      retryable: true,
      suggestedActions: [
        'Try again in a moment',
        'Check if GitHub service is available'
      ]
    };
  }

  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      const response = await fetch('https://api.github.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      return true;
    } catch {
      return false;
    }
  }
}

interface AuthError {
  type: string;
  message: string;
  code?: string;
  statusCode?: number;
  metadata?: any;
}

interface ErrorContext {
  githubOAuthService: any;
  retryOperation: () => Promise<void>;
}

interface ErrorRecovery {
  recovered: boolean;
  userMessage: string;
  actionRequired: string;
  retryable: boolean;
  technicalDetails?: string;
  suggestedActions?: string[];
  retryAfter?: number;
}
```

## Complete User Experience Flow

### End-to-End User Journey

```
1. User Trigger
   ├── User clicks "Connect to GitHub" in MCP Dashboard
   ├── System shows "Connecting to GitHub..." indicator
   └── Browser opens to GitHub OAuth page

2. GitHub Authentication
   ├── User logs into GitHub (if not already logged in)
   ├── User reviews requested permissions:
   │   ├── Repository access (read/write)
   │   ├── User profile access
   │   └── Organization membership access
   ├── User clicks "Authorize" or "Cancel"
   └── Browser redirects to thealpinecode.alpineintellect://auth-callback

3. IDE Callback Processing
   ├── Alpine Intellect captures the redirect
   ├── IDE comes to foreground
   ├── Shows "Processing authentication..." message
   ├── Validates OAuth parameters
   ├── Exchanges code for tokens
   └── Stores tokens securely in Keytar

4. Success Confirmation
   ├── Shows "Successfully connected to GitHub!" message
   ├── Updates service status to "Connected"
   ├── Enables GitHub tools in Code Assistant Agent
   └── User can now use GitHub workflows

5. Ongoing Usage
   ├── Code Assistant Agent can access GitHub tools
   ├── Tokens validated before each API call
   ├── Real-time error handling for any issues
   └── Seamless tool execution with authentication
```

### GitHub-Specific Considerations

#### **API Rate Limits**:
- **Authenticated requests**: 5,000 requests per hour
- **Rate limit headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`
- **Handling strategy**: Respect rate limits, implement exponential backoff

#### **Token Characteristics**:
- **No refresh tokens**: GitHub doesn't provide refresh tokens for OAuth apps
- **No expiration**: GitHub tokens don't expire automatically
- **Revocation detection**: Monitor for 401 responses indicating revoked tokens

#### **Required Scopes**:
- **`repo`**: Full control of private repositories
- **`user`**: Read/write access to profile info
- **`read:org`**: Read org and team membership

### Security Best Practices Implemented

1. **PKCE (Proof Key for Code Exchange)**:
   - Prevents authorization code interception attacks
   - Uses SHA256 code challenge method
   - Cryptographically secure code verifier generation

2. **State Parameter Validation**:
   - Prevents CSRF attacks
   - Validates state on callback
   - Secure random state generation

3. **Secure Token Storage**:
   - Uses OS-level keychain/credential manager
   - Same `alpine-app` service as existing auth
   - No tokens stored in plain text

4. **Integration with Existing Patterns**:
   - Uses existing `thealpinecode.alpineintellect://` protocol
   - Extends existing `handleDeepLink` function
   - Follows same IPC event patterns
   - Uses same keytar service name

This comprehensive GitHub OAuth implementation provides a secure, user-friendly authentication flow that seamlessly integrates with the existing Alpine Intellect codebase while following modern security best practices and providing excellent error handling and user experience.
