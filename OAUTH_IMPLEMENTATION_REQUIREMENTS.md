# OAuth Implementation Requirements - Critical Guidelines

## 🚨 CRITICAL: Infrastructure Preservation Requirements

### 1. **PRESERVE ALL EXISTING FUNCTIONALITY**
- ❌ **DO NOT MODIFY** existing `handleDeepLink` function logic
- ❌ **DO NOT CHANGE** existing Alpine auth token handling
- ❌ **DO NOT ALTER** existing protocol handler registration
- ❌ **DO NOT BREAK** any current authentication flows

### 2. **EXTEND, NEVER REPLACE**
- ✅ **ADD** new OAuth callback handling alongside existing logic
- ✅ **EXTEND** existing functions with new capabilities
- ✅ **PRESERVE** all current URL patterns and handling
- ✅ **MAINTAIN** backward compatibility at all times

## Implementation Strategy

### Phase 1: Foundation (No Breaking Changes)
```typescript
// EXISTING handleDeepLink function (DO NOT MODIFY)
function handleDeepLink(url: string) {
  try {
    const parsedUrl = new URL(url);
    
    // EXISTING Alpine auth logic (PRESERVE UNCHANGED)
    const token = parsedUrl.searchParams.get("token");
    if (token) {
      // ... existing Alpine auth handling (DO NOT TOUCH)
      return;
    }
    
    // NEW: OAuth callback routing (ADD WITHOUT MODIFYING ABOVE)
    if (parsedUrl.pathname.startsWith('/auth-callback/')) {
      handleOAuthCallback(url); // NEW function
      return;
    }
    
  } catch (err) {
    console.error("Failed to parse auth callback URL:", url, err);
  }
}
```

### Phase 2: OAuth Service Architecture
```typescript
// NEW: OAuth Service Interface
interface IOAuthService {
  readonly serviceId: string;
  readonly displayName: string;
  readonly callbackPath: string;
  
  initializeOAuth(): Promise<string>;
  handleCallback(params: URLSearchParams): Promise<OAuthResult>;
  refreshTokens(): Promise<void>;
  revokeAccess(): Promise<void>;
  getAuthStatus(): Promise<AuthStatus>;
}

// NEW: Service Registry
class OAuthServiceRegistry {
  private services = new Map<string, IOAuthService>();
  
  registerService(service: IOAuthService): void;
  getServiceByCallbackPath(path: string): IOAuthService | undefined;
}
```

### Phase 3: Service Implementations
```typescript
// GitHub OAuth Service
class GitHubOAuthService implements IOAuthService {
  readonly serviceId = 'github';
  readonly callbackPath = '/auth-callback/github';
  
  async initializeOAuth(): Promise<string> {
    // GitHub-specific implementation
    return 'https://github.com/login/oauth/authorize?...';
  }
}

// Figma OAuth Service
class FigmaOAuthService implements IOAuthService {
  readonly serviceId = 'figma';
  readonly callbackPath = '/auth-callback/figma';
  
  async initializeOAuth(): Promise<string> {
    // Figma-specific implementation
    return 'https://www.figma.com/oauth?...';
  }
}
```

## URL Pattern Strategy

### Current (Preserved)
```
thealpinecode.alpineintellect://?token=abc123
```

### New OAuth Patterns (Added)
```
thealpinecode.alpineintellect://auth-callback/github?code=...&state=...
thealpinecode.alpineintellect://auth-callback/figma?code=...&state=...
thealpinecode.alpineintellect://auth-callback/{service}?code=...&state=...
```

## Token Storage Strategy

### Keytar Service Name: `"alpine-app"` (Consistent)
```typescript
// GitHub tokens
await keytar.setPassword('alpine-app', 'github-access-token', token);
await keytar.setPassword('alpine-app', 'github-token-metadata', metadata);

// Figma tokens
await keytar.setPassword('alpine-app', 'figma-access-token', token);
await keytar.setPassword('alpine-app', 'figma-refresh-token', refreshToken);
await keytar.setPassword('alpine-app', 'figma-token-expires-at', expiresAt);

// Future services follow same pattern
await keytar.setPassword('alpine-app', '{service}-access-token', token);
```

## Service Detection Logic

### Callback URL Analysis
```typescript
function detectOAuthService(url: string): string | null {
  const parsedUrl = new URL(url);
  
  // Alpine auth (existing pattern)
  if (parsedUrl.searchParams.has('token')) {
    return 'alpine';
  }
  
  // OAuth services (new pattern)
  const pathMatch = parsedUrl.pathname.match(/^\/auth-callback\/(.+)$/);
  if (pathMatch) {
    return pathMatch[1]; // Returns 'github', 'figma', etc.
  }
  
  return null;
}
```

## Independent Service Management

### Enable/Disable Services
```typescript
class OAuthManager {
  private enabledServices = new Set<string>();
  
  enableService(serviceId: string): void {
    this.enabledServices.add(serviceId);
  }
  
  disableService(serviceId: string): void {
    this.enabledServices.delete(serviceId);
  }
  
  isServiceEnabled(serviceId: string): boolean {
    return this.enabledServices.has(serviceId);
  }
}
```

## Future Provider Template

### Adding New OAuth Provider (e.g., Slack)
```typescript
// 1. Create service class
class SlackOAuthService implements IOAuthService {
  readonly serviceId = 'slack';
  readonly callbackPath = '/auth-callback/slack';
  
  // Implement interface methods...
}

// 2. Register service
oauthRegistry.registerService(new SlackOAuthService());

// 3. Add UI components (following existing patterns)
// 4. Configure redirect URI: thealpinecode.alpineintellect://auth-callback/slack
// 5. Test independent operation
```

## Critical Implementation Rules

### ❌ NEVER DO
- Modify existing `handleDeepLink` function logic
- Change existing Alpine auth token handling
- Alter existing protocol handler registration
- Break any current authentication flows
- Create new protocol handlers or schemes

### ✅ ALWAYS DO
- Preserve all existing functionality
- Add new features alongside existing ones
- Use consistent keytar service name ("alpine-app")
- Follow established URL patterns
- Implement service detection for routing
- Enable independent service management
- Design for future scalability

## Testing Requirements

### Backward Compatibility Tests
- [ ] Existing Alpine auth continues to work unchanged
- [ ] Current deep link handling preserved
- [ ] All existing token storage patterns maintained
- [ ] Current UI components function normally

### New Functionality Tests
- [ ] GitHub OAuth works independently
- [ ] Figma OAuth works independently
- [ ] Services can be enabled/disabled independently
- [ ] Service detection routes callbacks correctly
- [ ] Token storage uses consistent patterns

### Integration Tests
- [ ] Multiple OAuth services can coexist
- [ ] No interference between different auth systems
- [ ] Consistent user experience across all services
- [ ] Error handling works for all scenarios

## Success Criteria

1. **Zero Breaking Changes**: All existing functionality works unchanged
2. **Independent Operation**: Each OAuth service operates independently
3. **Scalable Architecture**: New providers can be added easily
4. **Consistent Patterns**: All services follow the same implementation patterns
5. **Future-Proof Design**: Architecture supports unlimited OAuth providers

## Documentation Requirements

- [ ] Update implementation guides with preservation requirements
- [ ] Document service interface for future providers
- [ ] Create templates for adding new OAuth services
- [ ] Maintain backward compatibility documentation
- [ ] Document testing procedures for new providers
