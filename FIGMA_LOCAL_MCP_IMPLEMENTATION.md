# Figma Local MCP Server Implementation

This document describes the implementation of the transition from remote HTTP/SSE MCP server to a local MCP server architecture using stdio transport for the Figma integration in Alpine Intellect.

## Overview

The implementation switches the Figma MCP integration from a remote HTTP/SSE server to a local Node.js process that communicates via stdin/stdout (stdio transport). This provides better security, lower latency, and more control over the MCP server lifecycle while preserving all existing OAuth 2.0 with PKCE functionality.

## Key Components

### 1. Stdio Transport (`src/electron/mcp/transports/stdio-transport.ts`)

A new transport implementation that communicates with local MCP server processes via stdin/stdout:

- **JSON-RPC over stdio**: Messages are sent as JSON-RPC 2.0 over stdin/stdout
- **Message queuing**: Handles request/response correlation with timeouts
- **Process lifecycle**: Monitors process health and handles errors
- **MCP handshake**: Performs proper MCP protocol initialization

### 2. Local Service Client (`src/electron/mcp/clients/local-service-client.ts`)

Base class for managing local MCP server processes:

- **Process spawning**: Launches Node.js MCP server processes
- **Environment injection**: Securely injects OAuth tokens via environment variables
- **Health monitoring**: Periodic health checks and automatic restart logic
- **Graceful shutdown**: Proper cleanup and process termination

### 3. Figma Local Service (`src/electron/mcp/clients/figma-local-service.ts`)

Figma-specific implementation extending LocalServiceClient:

- **OAuth integration**: Preserves existing OAuth 2.0 with PKCE flow
- **Token injection**: Injects Figma access tokens into server environment
- **Tool discovery**: Dynamically discovers available Figma tools
- **Health monitoring**: Figma-specific health checks and status reporting

### 4. Updated Configuration Types (`src/electron/mcp/config/mcp-config-types.ts`)

Extended configuration to support stdio transport:

```typescript
interface ServerConfiguration {
  transport: 'http-sse' | 'stdio' | 'websocket';
  stdio?: {
    executable: string;
    args?: string[];
    cwd?: string;
    env?: Record<string, string>;
    restart_delay?: number;
    max_restarts?: number;
  };
  // ... existing fields
}
```

### 5. Service Registry Updates (`src/electron/mcp/registry/service-registry.ts`)

Enhanced to support both remote and local services:

- **Transport detection**: Automatically chooses service type based on transport
- **Unified interface**: BaseService interface for both remote and local services
- **OAuth handling**: Conditional OAuth callback registration for remote services only

## Configuration Changes

### Before (HTTP/SSE):
```json
{
  "figma": {
    "transport": "http-sse",
    "endpoints": {
      "http": "https://api.figma.com/mcp/v1",
      "sse": "https://api.figma.com/mcp/v1/events"
    },
    "auth": { /* OAuth config */ }
  }
}
```

### After (Stdio):
```json
{
  "figma": {
    "transport": "stdio",
    "stdio": {
      "executable": "node",
      "args": ["figma-mcp-server.js"],
      "env": {
        "FIGMA_CLIENT_ID": "your_figma_client_id_here"
      },
      "restart_delay": 2000,
      "max_restarts": 3
    },
    "auth": { /* Same OAuth config preserved */ }
  }
}
```

## OAuth Token Flow

1. **Authentication**: User authenticates via existing OAuth 2.0 with PKCE flow
2. **Token Storage**: Tokens stored securely in system keychain (unchanged)
3. **Process Startup**: When starting MCP server process:
   - Retrieve access token from secure storage
   - Inject token into process environment variables
   - Start MCP server with token access
4. **Token Refresh**: Automatic token refresh handled by OAuth service
5. **Process Restart**: On token refresh, restart MCP server with new tokens

## Environment Variables Injected

The local MCP server process receives these environment variables:

```bash
# OAuth tokens
FIGMA_ACCESS_TOKEN=<access_token>
FIGMA_TOKEN_TYPE=Bearer
FIGMA_TOKEN_EXPIRY=<iso_date>
FIGMA_SCOPES=file:read,file:write,team:read

# Service configuration
FIGMA_CLIENT_ID=<client_id>
FIGMA_API_BASE_URL=https://api.figma.com/v1
MCP_SERVER_NAME=figma
MCP_LOG_LEVEL=info
```

## Process Lifecycle Management

### Startup Sequence:
1. OAuth authentication (if needed)
2. Retrieve access tokens
3. Spawn MCP server process with environment variables
4. Initialize stdio transport
5. Perform MCP handshake
6. Discover available tools
7. Start health monitoring

### Health Monitoring:
- Periodic health checks every 30 seconds
- Process monitoring (PID, exit status)
- Transport connectivity checks
- MCP server ping requests

### Error Handling:
- Automatic restart on process crashes (up to max_restarts)
- Exponential backoff on restart attempts
- Graceful degradation on repeated failures
- Proper cleanup on application shutdown

## Security Improvements

1. **No Network Exposure**: MCP server runs locally, no network endpoints
2. **Token Isolation**: Tokens only exist in process memory, never written to disk
3. **Process Isolation**: Each service runs in its own process
4. **Secure Communication**: stdio transport is inherently secure

## Performance Benefits

1. **Lower Latency**: No network round-trips for tool execution
2. **Better Reliability**: No dependency on external network connectivity
3. **Resource Control**: Better control over process resources and lifecycle
4. **Faster Startup**: No need to establish network connections

## Testing

Run the integration tests to validate the implementation:

```bash
npm run test:figma-local
```

The test suite validates:
- Configuration loading and parsing
- Service registry creation
- OAuth configuration preservation
- Stdio transport setup
- Process lifecycle management
- Health monitoring
- Error handling and restart logic

## Migration Notes

### Backward Compatibility:
- Existing OAuth tokens and user authentication are preserved
- UI components remain unchanged
- Tool execution interface is identical
- Configuration can be switched back to HTTP/SSE if needed

### MCP Server Requirements:
- The local MCP server must be a Node.js executable
- Must implement MCP protocol over stdio
- Must read OAuth tokens from environment variables
- Must handle graceful shutdown signals

### Deployment:
- Bundle the Figma MCP server with the desktop application
- Ensure Node.js runtime is available
- Configure proper file permissions for executable

## Future Enhancements

1. **Native Executables**: Support for compiled MCP servers (Go, Rust, etc.)
2. **WebSocket Transport**: Alternative transport for local servers
3. **Process Pooling**: Reuse processes for multiple tool executions
4. **Advanced Monitoring**: Detailed metrics and logging
5. **Hot Reloading**: Update MCP servers without restart

## Troubleshooting

### Common Issues:

1. **Process Won't Start**: Check executable path and permissions
2. **Token Injection Failed**: Verify OAuth authentication status
3. **Health Check Failures**: Check MCP server implementation
4. **Restart Loops**: Review error logs and increase restart delay

### Debug Mode:
Enable detailed logging by setting `MCP_LOG_LEVEL=debug` in the configuration.

### Log Locations:
- Main application logs: Console output
- MCP server logs: Process stderr
- OAuth logs: Secure keychain access logs
