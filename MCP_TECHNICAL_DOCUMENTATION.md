# MCP Technical Documentation - GitHub & Figma Integration

## Overview

This document provides comprehensive technical documentation for implementing MCP (Model Context Protocol) integration in Alpine Intellect with GitHub (local PAT) and Figma (SSE OAuth) services. The implementation is built from scratch with scalable, reliable architecture.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Alpine Intellect MCP Host                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Code Assistant  │  │Design Assistant │  │  Workflow       │ │
│  │     Agent       │  │     Agent       │  │   Engine        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ GitHub Local    │  │ Figma Remote    │  │   Message       │ │
│  │    Client       │  │    Client       │  │   Router        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ stdio Transport │  │ SSE Transport   │  │ Auth Manager    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
           │                           │
           ▼                           ▼
┌─────────────────┐         ┌─────────────────┐
│ GitHub Local    │         │ Figma Remote    │
│  MCP Server     │         │  MCP Server     │
│   (stdio)       │         │    (SSE)        │
└─────────────────┘         └─────────────────┘
```

## 1. MCP Host Implementation

### 1.1 Desktop MCP Host

**File**: `src/mcp/host/desktop-mcp-host.ts`

```typescript
interface MCPHost {
  id: string;
  name: string;
  version: string;
  capabilities: string[];
  clients: Map<string, MCPClient>;
  agents: Map<string, AIAgent>;
  messageRouter: MessageRouter;
  authManager: AuthManager;
}

class DesktopMCPHost implements MCPHost {
  private clients = new Map<string, MCPClient>();
  private agents = new Map<string, AIAgent>();
  private messageRouter: MessageRouter;
  private authManager: AuthManager;
  private workflowEngine: WorkflowEngine;
  
  async initialize(config: MCPHostConfig): Promise<void> {
    // Initialize core components
    this.messageRouter = new MessageRouter();
    this.authManager = new AuthManager();
    this.workflowEngine = new WorkflowEngine();
    
    // Initialize clients
    await this.initializeClients(config.clients);
    
    // Initialize agents
    await this.initializeAgents(config.agents);
    
    // Start message processing
    await this.startMessageProcessing();
  }
  
  private async initializeClients(clientConfigs: ClientConfig[]): Promise<void> {
    for (const config of clientConfigs) {
      let client: MCPClient;
      
      switch (config.transport) {
        case 'stdio':
          client = new GitHubLocalClient(config);
          break;
        case 'sse':
          client = new FigmaRemoteClient(config);
          break;
        default:
          throw new Error(`Unsupported transport: ${config.transport}`);
      }
      
      await client.initialize();
      this.clients.set(config.id, client);
    }
  }
  
  private async initializeAgents(agentConfigs: AgentConfig[]): Promise<void> {
    for (const config of agentConfigs) {
      let agent: AIAgent;
      
      switch (config.type) {
        case 'code_assistant':
          agent = new CodeAssistantAgent(config, this.clients);
          break;
        case 'design_assistant':
          agent = new DesignAssistantAgent(config, this.clients);
          break;
        default:
          throw new Error(`Unsupported agent type: ${config.type}`);
      }
      
      await agent.initialize();
      this.agents.set(config.id, agent);
    }
  }
  
  async executeAgentWorkflow(
    agentId: string, 
    request: AgentRequest
  ): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }
    
    return await this.workflowEngine.executeWorkflow(agent, request);
  }
}
```

### 1.2 VS Code MCP Host

**File**: `src/mcp/host/vscode-mcp-host.ts`

```typescript
class VSCodeMCPHost {
  private desktopHostConnection: DesktopHostConnection;
  private extensionContext: vscode.ExtensionContext;
  private webviewProvider: MCPWebviewProvider;
  
  async initialize(context: vscode.ExtensionContext): Promise<void> {
    this.extensionContext = context;
    
    // Connect to desktop MCP host
    this.desktopHostConnection = new DesktopHostConnection();
    await this.desktopHostConnection.connect();
    
    // Initialize webview provider
    this.webviewProvider = new MCPWebviewProvider(context, this.desktopHostConnection);
    
    // Register commands
    this.registerCommands();
  }
  
  private registerCommands(): void {
    const commands = [
      vscode.commands.registerCommand('mcp.executeGitHubTool', this.executeGitHubTool.bind(this)),
      vscode.commands.registerCommand('mcp.executeFigmaTool', this.executeFigmaTool.bind(this)),
      vscode.commands.registerCommand('mcp.openAgentChat', this.openAgentChat.bind(this))
    ];
    
    commands.forEach(cmd => this.extensionContext.subscriptions.push(cmd));
  }
  
  private async executeGitHubTool(toolName: string, parameters: any): Promise<any> {
    return await this.desktopHostConnection.executeAgentWorkflow('code_assistant', {
      type: 'tool_execution',
      tool: toolName,
      parameters,
      context: this.getIDEContext()
    });
  }
}
```

## 2. MCP Client Implementation

### 2.1 GitHub Local Client (stdio)

**File**: `src/mcp/clients/github-local-client.ts`

```typescript
class GitHubLocalClient implements MCPClient {
  private serverProcess: ChildProcess | null = null;
  private stdioTransport: StdioTransport;
  private authService: PATAuthService;
  private messageQueue: MessageQueue;
  
  async initialize(): Promise<void> {
    // Initialize authentication
    this.authService = new PATAuthService('github');
    await this.authService.initialize();
    
    // Start local server process
    await this.startLocalServer();
    
    // Initialize transport
    this.stdioTransport = new StdioTransport(this.serverProcess!);
    await this.stdioTransport.initialize();
    
    // Initialize message queue
    this.messageQueue = new MessageQueue();
    
    // Discover available tools
    await this.discoverTools();
  }
  
  private async startLocalServer(): Promise<void> {
    const serverPath = path.join(__dirname, '../servers/github-local-server.js');
    const pat = await this.authService.getToken();
    
    this.serverProcess = spawn('node', [serverPath], {
      env: {
        ...process.env,
        GITHUB_PAT: pat,
        MCP_SERVER_NAME: 'github-local',
        LOG_LEVEL: 'info'
      },
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // Handle process events
    this.serverProcess.on('error', this.handleProcessError.bind(this));
    this.serverProcess.on('exit', this.handleProcessExit.bind(this));
  }
  
  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    const request: MCPRequest = {
      id: generateId(),
      type: 'request',
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: parameters
      }
    };
    
    try {
      const response = await this.stdioTransport.sendRequest(request);
      return this.processToolResponse(response);
    } catch (error) {
      return this.handleToolError(error, toolName, parameters);
    }
  }
  
  private async discoverTools(): Promise<void> {
    const request: MCPRequest = {
      id: generateId(),
      type: 'request',
      method: 'tools/list',
      params: {}
    };
    
    const response = await this.stdioTransport.sendRequest(request);
    this.availableTools = response.result.tools;
  }
  
  // GitHub-specific tool implementations
  async createRepository(params: CreateRepositoryParams): Promise<ToolResult> {
    return await this.executeTool('create_repository', params);
  }
  
  async createIssue(params: CreateIssueParams): Promise<ToolResult> {
    return await this.executeTool('create_issue', params);
  }
  
  async searchCode(params: SearchCodeParams): Promise<ToolResult> {
    return await this.executeTool('search_code', params);
  }
}
```

### 2.2 Figma Remote Client (SSE)

**File**: `src/mcp/clients/figma-remote-client.ts`

```typescript
class FigmaRemoteClient implements MCPClient {
  private sseTransport: SSETransport;
  private authService: OAuthAuthService;
  private connectionManager: ConnectionManager;
  private eventProcessor: EventProcessor;
  
  async initialize(): Promise<void> {
    // Initialize OAuth authentication
    this.authService = new OAuthAuthService('figma');
    await this.authService.initialize();
    
    // Initialize connection manager
    this.connectionManager = new ConnectionManager({
      endpoint: 'https://api.figma.com/mcp/v1/events',
      authService: this.authService,
      reconnectionConfig: {
        maxAttempts: 5,
        initialDelay: 1000,
        maxDelay: 30000,
        backoffFactor: 2
      }
    });
    
    // Initialize SSE transport
    this.sseTransport = new SSETransport(this.connectionManager);
    await this.sseTransport.initialize();
    
    // Initialize event processor
    this.eventProcessor = new EventProcessor();
    this.sseTransport.onMessage(this.eventProcessor.processEvent.bind(this.eventProcessor));
    
    // Discover available tools
    await this.discoverTools();
  }
  
  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    const request: MCPRequest = {
      id: generateId(),
      type: 'request',
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: parameters
      }
    };
    
    try {
      // Send request via HTTP API (not SSE for requests)
      const response = await this.sendHTTPRequest(request);
      return this.processToolResponse(response);
    } catch (error) {
      return this.handleToolError(error, toolName, parameters);
    }
  }
  
  private async sendHTTPRequest(request: MCPRequest): Promise<MCPResponse> {
    const token = await this.authService.getAccessToken();
    
    const response = await fetch('https://api.figma.com/mcp/v1/tools', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  // Figma-specific tool implementations
  async getFileContent(params: GetFileContentParams): Promise<ToolResult> {
    return await this.executeTool('get_file_content', params);
  }
  
  async exportAssets(params: ExportAssetsParams): Promise<ToolResult> {
    return await this.executeTool('export_assets', params);
  }
  
  async createComponent(params: CreateComponentParams): Promise<ToolResult> {
    return await this.executeTool('create_component', params);
  }
}
```

## 3. Transport Protocol Implementation

### 3.1 stdio Transport

**File**: `src/mcp/transports/stdio-transport.ts`

```typescript
class StdioTransport implements Transport {
  private process: ChildProcess;
  private messageQueue: Map<string, PendingRequest> = new Map();
  private responseHandlers: Map<string, (response: MCPResponse) => void> = new Map();
  
  constructor(process: ChildProcess) {
    this.process = process;
  }
  
  async initialize(): Promise<void> {
    // Set up stdin/stdout communication
    this.process.stdout?.on('data', this.handleStdoutData.bind(this));
    this.process.stderr?.on('data', this.handleStderrData.bind(this));
    
    // Initialize handshake
    await this.performHandshake();
  }
  
  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(request.id);
        reject(new Error(`Request timeout: ${request.id}`));
      }, 30000);
      
      this.messageQueue.set(request.id, { resolve, reject, timeout });
      
      const message = JSON.stringify(request) + '\n';
      this.process.stdin?.write(message);
    });
  }
  
  private handleStdoutData(data: Buffer): void {
    const lines = data.toString().split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      try {
        const message = JSON.parse(line) as MCPMessage;
        this.processMessage(message);
      } catch (error) {
        console.error('Failed to parse message:', line, error);
      }
    }
  }
  
  private processMessage(message: MCPMessage): void {
    if (message.type === 'response' && message.id) {
      const pending = this.messageQueue.get(message.id);
      if (pending) {
        clearTimeout(pending.timeout);
        this.messageQueue.delete(message.id);
        
        if (message.error) {
          pending.reject(new Error(message.error.message));
        } else {
          pending.resolve(message as MCPResponse);
        }
      }
    }
  }
  
  private async performHandshake(): Promise<void> {
    const initRequest: MCPRequest = {
      id: generateId(),
      type: 'request',
      method: 'initialize',
      params: {
        protocolVersion: '1.0',
        capabilities: {
          tools: true,
          resources: false,
          prompts: false
        },
        clientInfo: {
          name: 'Alpine Intellect',
          version: '1.0.0'
        }
      }
    };
    
    const response = await this.sendRequest(initRequest);
    if (response.error) {
      throw new Error(`Handshake failed: ${response.error.message}`);
    }
  }
}
```

### 3.2 SSE Transport

**File**: `src/mcp/transports/sse-transport.ts`

```typescript
class SSETransport implements Transport {
  private eventSource: EventSource | null = null;
  private connectionManager: ConnectionManager;
  private eventHandlers: Map<string, (event: MessageEvent) => void> = new Map();
  private reconnectionTimer: NodeJS.Timeout | null = null;
  
  constructor(connectionManager: ConnectionManager) {
    this.connectionManager = connectionManager;
  }
  
  async initialize(): Promise<void> {
    await this.connect();
  }
  
  private async connect(): Promise<void> {
    const token = await this.connectionManager.getAuthToken();
    const endpoint = this.connectionManager.getEndpoint();
    
    this.eventSource = new EventSource(endpoint, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/event-stream'
      }
    });
    
    this.eventSource.onopen = this.handleOpen.bind(this);
    this.eventSource.onerror = this.handleError.bind(this);
    this.eventSource.onmessage = this.handleMessage.bind(this);
    
    // Set up custom event handlers
    this.eventSource.addEventListener('tool_result', this.handleToolResult.bind(this));
    this.eventSource.addEventListener('error', this.handleServerError.bind(this));
  }
  
  private handleOpen(event: Event): void {
    console.log('SSE connection opened');
    this.clearReconnectionTimer();
  }
  
  private handleError(event: Event): void {
    console.error('SSE connection error:', event);
    this.scheduleReconnection();
  }
  
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data) as MCPMessage;
      this.processMessage(message);
    } catch (error) {
      console.error('Failed to parse SSE message:', event.data, error);
    }
  }
  
  private scheduleReconnection(): void {
    if (this.reconnectionTimer) return;
    
    const delay = this.connectionManager.getNextReconnectionDelay();
    this.reconnectionTimer = setTimeout(async () => {
      this.reconnectionTimer = null;
      await this.reconnect();
    }, delay);
  }
  
  private async reconnect(): Promise<void> {
    this.disconnect();
    
    try {
      await this.connect();
    } catch (error) {
      console.error('Reconnection failed:', error);
      this.scheduleReconnection();
    }
  }
  
  onMessage(handler: (message: MCPMessage) => void): void {
    this.eventHandlers.set('message', handler);
  }
  
  private processMessage(message: MCPMessage): void {
    const handler = this.eventHandlers.get('message');
    if (handler) {
      handler(message);
    }
  }
  
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.clearReconnectionTimer();
  }
}
```

## 4. Authentication Implementation

### 4.1 PAT Authentication Service (GitHub)

**File**: `src/mcp/auth/pat-auth-service.ts`

```typescript
class PATAuthService implements AuthService {
  private serviceName: string;
  private tokenStorage: TokenStorage;
  private validationEndpoint: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
    this.tokenStorage = new TokenStorage();
    this.validationEndpoint = 'https://api.github.com/user';
  }

  async initialize(): Promise<void> {
    await this.tokenStorage.initialize();
  }

  async getToken(): Promise<string> {
    const token = await this.tokenStorage.getToken(`${this.serviceName}-pat`);
    if (!token) {
      throw new Error(`No PAT found for service: ${this.serviceName}`);
    }

    // Validate token
    const isValid = await this.validateToken(token);
    if (!isValid) {
      throw new Error(`Invalid PAT for service: ${this.serviceName}`);
    }

    return token;
  }

  async setToken(token: string): Promise<void> {
    // Validate token before storing
    const isValid = await this.validateToken(token);
    if (!isValid) {
      throw new Error('Invalid GitHub PAT provided');
    }

    await this.tokenStorage.setToken(`${this.serviceName}-pat`, token);
  }

  private async validateToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(this.validationEndpoint, {
        headers: {
          'Authorization': `token ${token}`,
          'User-Agent': 'Alpine-Intellect-MCP/1.0'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  async revokeToken(): Promise<void> {
    await this.tokenStorage.deleteToken(`${this.serviceName}-pat`);
  }
}
```

### 4.2 OAuth Authentication Service (Figma)

**File**: `src/mcp/auth/oauth-auth-service.ts`

```typescript
class OAuthAuthService implements AuthService {
  private serviceName: string;
  private tokenStorage: TokenStorage;
  private oauthConfig: OAuthConfig;
  private tokenRefreshTimer: NodeJS.Timeout | null = null;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
    this.tokenStorage = new TokenStorage();
    this.oauthConfig = {
      authorizationEndpoint: 'https://www.figma.com/oauth',
      tokenEndpoint: 'https://www.figma.com/api/oauth/token',
      clientId: process.env.FIGMA_CLIENT_ID!,
      scopes: ['file:read', 'file:write', 'team:read'],
      redirectUri: 'alpine://mcp-auth/figma'
    };
  }

  async initialize(): Promise<void> {
    await this.tokenStorage.initialize();
    await this.scheduleTokenRefresh();
  }

  async initiateOAuthFlow(): Promise<string> {
    const state = generateRandomString(32);
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = await generateCodeChallenge(codeVerifier);

    // Store PKCE parameters
    await this.tokenStorage.setToken(`${this.serviceName}-pkce-verifier`, codeVerifier);
    await this.tokenStorage.setToken(`${this.serviceName}-oauth-state`, state);

    const authUrl = new URL(this.oauthConfig.authorizationEndpoint);
    authUrl.searchParams.set('client_id', this.oauthConfig.clientId);
    authUrl.searchParams.set('redirect_uri', this.oauthConfig.redirectUri);
    authUrl.searchParams.set('scope', this.oauthConfig.scopes.join(' '));
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    return authUrl.toString();
  }

  async handleAuthCallback(code: string, state: string): Promise<void> {
    // Validate state
    const storedState = await this.tokenStorage.getToken(`${this.serviceName}-oauth-state`);
    if (state !== storedState) {
      throw new Error('Invalid OAuth state parameter');
    }

    // Get PKCE verifier
    const codeVerifier = await this.tokenStorage.getToken(`${this.serviceName}-pkce-verifier`);
    if (!codeVerifier) {
      throw new Error('PKCE verifier not found');
    }

    // Exchange code for tokens
    const tokenResponse = await this.exchangeCodeForTokens(code, codeVerifier);

    // Store tokens
    await this.storeTokens(tokenResponse);

    // Clean up temporary storage
    await this.tokenStorage.deleteToken(`${this.serviceName}-oauth-state`);
    await this.tokenStorage.deleteToken(`${this.serviceName}-pkce-verifier`);

    // Schedule token refresh
    await this.scheduleTokenRefresh();
  }

  async getAccessToken(): Promise<string> {
    const token = await this.tokenStorage.getToken(`${this.serviceName}-access-token`);
    if (!token) {
      throw new Error(`No access token found for service: ${this.serviceName}`);
    }

    // Check if token is expired
    const expiresAt = await this.tokenStorage.getToken(`${this.serviceName}-expires-at`);
    if (expiresAt && Date.now() >= parseInt(expiresAt)) {
      await this.refreshAccessToken();
      return await this.tokenStorage.getToken(`${this.serviceName}-access-token`)!;
    }

    return token;
  }
}
```

## 5. AI Agent Implementation

### 5.1 Code Assistant Agent

**File**: `src/agents/code-assistant-agent.ts`

```typescript
class CodeAssistantAgent extends BaseAgent {
  private githubClient: GitHubLocalClient;
  private toolCapabilities: Map<string, ToolCapability> = new Map();

  constructor(config: AgentConfig, clients: Map<string, MCPClient>) {
    super(config);
    this.githubClient = clients.get('github_local_client') as GitHubLocalClient;
  }

  async initialize(): Promise<void> {
    await super.initialize();
    await this.discoverToolCapabilities();
  }

  private async discoverToolCapabilities(): Promise<void> {
    const tools = await this.githubClient.listTools();

    for (const tool of tools) {
      this.toolCapabilities.set(tool.name, {
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
        outputSchema: tool.outputSchema,
        examples: tool.examples || []
      });
    }
  }

  async processRequest(request: AgentRequest): Promise<AgentResponse> {
    // Analyze request and determine tool usage plan
    const toolPlan = await this.planToolUsage(request);

    // Execute tool chain
    const executionResults = await this.executeToolChain(toolPlan);

    // Generate final response
    return await this.generateResponse(request, executionResults);
  }

  private async planToolUsage(request: AgentRequest): Promise<ToolPlan> {
    const prompt = this.buildPlanningPrompt(request);
    const llmResponse = await this.callLLM(prompt);

    return this.parseToolPlan(llmResponse);
  }

  private buildPlanningPrompt(request: AgentRequest): string {
    const availableTools = Array.from(this.toolCapabilities.values())
      .map(tool => `- ${tool.name}: ${tool.description}`)
      .join('\n');

    return `
You are a code assistant agent. Analyze the user request and create a plan using available GitHub tools.

User Request: ${request.message}

Available Tools:
${availableTools}

Context:
- Current workspace: ${request.context?.workspace || 'unknown'}
- Active files: ${request.context?.activeFiles?.join(', ') || 'none'}

Create a JSON plan with the following structure:
{
  "reasoning": "explanation of your approach",
  "tools": [
    {
      "tool": "tool_name",
      "parameters": {...},
      "reasoning": "why this tool is needed"
    }
  ]
}

Focus on:
1. Repository management and setup
2. Issue tracking and bug reporting
3. Pull request creation and management
4. Code search and analysis
5. Branch management and Git operations
`;
  }

  private async executeToolChain(plan: ToolPlan): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];

    for (const toolCall of plan.tools) {
      try {
        const result = await this.executeToolWithRetry(toolCall);
        results.push({
          tool: toolCall.tool,
          success: true,
          result: result.data,
          metadata: result.metadata
        });

        // Update context for subsequent tools
        this.updateExecutionContext(result);

      } catch (error) {
        results.push({
          tool: toolCall.tool,
          success: false,
          error: error.message,
          metadata: { timestamp: Date.now() }
        });

        // Decide whether to continue or abort
        if (this.shouldAbortOnError(toolCall, error)) {
          break;
        }
      }
    }

    return results;
  }

  // Specific GitHub workflow methods
  async createRepositoryWorkflow(params: CreateRepositoryWorkflowParams): Promise<AgentResponse> {
    const plan: ToolPlan = {
      reasoning: "Create a new repository with initial setup",
      tools: [
        {
          tool: "create_repository",
          parameters: {
            name: params.name,
            description: params.description,
            private: params.private || false,
            auto_init: true,
            gitignore_template: params.language || "Node"
          },
          reasoning: "Create the repository structure"
        }
      ]
    };

    if (params.setupIssues) {
      plan.tools.push({
        tool: "create_issue",
        parameters: {
          owner: params.owner,
          repo: params.name,
          title: "Initial project setup",
          body: "Set up project structure and development environment",
          labels: ["enhancement", "setup"]
        },
        reasoning: "Create initial setup issue"
      });
    }

    const results = await this.executeToolChain(plan);
    return this.generateWorkflowResponse("Repository Creation", results);
  }
}
```

### 5.2 Design Assistant Agent

**File**: `src/agents/design-assistant-agent.ts`

```typescript
class DesignAssistantAgent extends BaseAgent {
  private figmaClient: FigmaRemoteClient;
  private toolCapabilities: Map<string, ToolCapability> = new Map();

  constructor(config: AgentConfig, clients: Map<string, MCPClient>) {
    super(config);
    this.figmaClient = clients.get('figma_remote_client') as FigmaRemoteClient;
  }

  async initialize(): Promise<void> {
    await super.initialize();
    await this.discoverToolCapabilities();
  }

  async processRequest(request: AgentRequest): Promise<AgentResponse> {
    // Analyze request and determine tool usage plan
    const toolPlan = await this.planToolUsage(request);

    // Execute tool chain
    const executionResults = await this.executeToolChain(toolPlan);

    // Generate final response
    return await this.generateResponse(request, executionResults);
  }

  // Specific Figma workflow methods
  async designSystemAuditWorkflow(params: DesignSystemAuditParams): Promise<AgentResponse> {
    const plan: ToolPlan = {
      reasoning: "Audit design system for consistency and standards",
      tools: [
        {
          tool: "get_file_content",
          parameters: {
            file_key: params.fileKey
          },
          reasoning: "Get current design file structure"
        },
        {
          tool: "export_assets",
          parameters: {
            file_key: params.fileKey,
            format: "svg",
            scale: 1
          },
          reasoning: "Export assets for analysis"
        }
      ]
    };

    const results = await this.executeToolChain(plan);
    return this.generateWorkflowResponse("Design System Audit", results);
  }

  async componentStandardizationWorkflow(params: ComponentStandardizationParams): Promise<AgentResponse> {
    const plan: ToolPlan = {
      reasoning: "Standardize components across design system",
      tools: [
        {
          tool: "get_file_content",
          parameters: {
            file_key: params.fileKey
          },
          reasoning: "Analyze existing components"
        },
        {
          tool: "create_component",
          parameters: {
            file_key: params.fileKey,
            name: params.componentName,
            description: params.description
          },
          reasoning: "Create standardized component"
        },
        {
          tool: "manage_styles",
          parameters: {
            file_key: params.fileKey,
            action: "update",
            styles: params.styles
          },
          reasoning: "Apply consistent styling"
        }
      ]
    };

    const results = await this.executeToolChain(plan);
    return this.generateWorkflowResponse("Component Standardization", results);
  }
}
```

## 6. Message Flow Diagrams

### 6.1 GitHub Local Tool Execution Flow

```
User Request
     │
     ▼
┌─────────────────┐
│ Code Assistant  │
│     Agent       │
└─────────────────┘
     │ 1. Plan tools
     ▼
┌─────────────────┐
│ GitHub Local    │
│    Client       │
└─────────────────┘
     │ 2. Execute tool
     ▼
┌─────────────────┐
│ stdio Transport │
└─────────────────┘
     │ 3. JSON-RPC over stdin/stdout
     ▼
┌─────────────────┐
│ GitHub Local    │
│  MCP Server     │
│   (Process)     │
└─────────────────┘
     │ 4. GitHub API call
     ▼
┌─────────────────┐
│   GitHub API    │
└─────────────────┘
     │ 5. Response
     ▼
┌─────────────────┐
│ GitHub Local    │
│  MCP Server     │
└─────────────────┘
     │ 6. JSON-RPC response
     ▼
┌─────────────────┐
│ stdio Transport │
└─────────────────┘
     │ 7. Tool result
     ▼
┌─────────────────┐
│ GitHub Local    │
│    Client       │
└─────────────────┘
     │ 8. Processed result
     ▼
┌─────────────────┐
│ Code Assistant  │
│     Agent       │
└─────────────────┘
     │ 9. Agent response
     ▼
    User
```

### 6.2 Figma Remote Tool Execution Flow

```
User Request
     │
     ▼
┌─────────────────┐
│Design Assistant │
│     Agent       │
└─────────────────┘
     │ 1. Plan tools
     ▼
┌─────────────────┐
│ Figma Remote    │
│    Client       │
└─────────────────┘
     │ 2. Execute tool
     ▼
┌─────────────────┐
│ OAuth Auth      │
│   Service       │
└─────────────────┘
     │ 3. Get access token
     ▼
┌─────────────────┐
│ HTTP Request    │
│  (with token)   │
└─────────────────┘
     │ 4. Tool execution request
     ▼
┌─────────────────┐
│ Figma Remote    │
│  MCP Server     │
│   (Cloud)       │
└─────────────────┘
     │ 5. Figma API call
     ▼
┌─────────────────┐
│   Figma API     │
└─────────────────┘
     │ 6. Response
     ▼
┌─────────────────┐
│ Figma Remote    │
│  MCP Server     │
└─────────────────┘
     │ 7. Tool result
     ▼
┌─────────────────┐
│ HTTP Response   │
└─────────────────┘
     │ 8. Result data
     ▼
┌─────────────────┐
│ Figma Remote    │
│    Client       │
└─────────────────┘
     │ 9. Processed result
     ▼
┌─────────────────┐
│Design Assistant │
│     Agent       │
└─────────────────┘
     │ 10. Agent response
     ▼
    User

    ┌─────────────────┐
    │ SSE Transport   │ ← Real-time events
    │ (Background)    │   (file changes, etc.)
    └─────────────────┘
```

## 7. Error Handling & Recovery

### 7.1 GitHub Local Error Handling

**File**: `src/mcp/error/github-error-handler.ts`

```typescript
class GitHubErrorHandler implements ErrorHandler {
  async handleError(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    switch (error.type) {
      case 'PROCESS_DIED':
        return await this.handleProcessDeath(error, context);
      case 'AUTHENTICATION_FAILED':
        return await this.handleAuthFailure(error, context);
      case 'RATE_LIMITED':
        return await this.handleRateLimit(error, context);
      case 'NETWORK_ERROR':
        return await this.handleNetworkError(error, context);
      default:
        return await this.handleGenericError(error, context);
    }
  }

  private async handleProcessDeath(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const client = context.client as GitHubLocalClient;

    // Attempt to restart the local server
    try {
      await client.restartLocalServer();
      return {
        recovered: true,
        action: 'server_restarted',
        retryable: true
      };
    } catch (restartError) {
      return {
        recovered: false,
        action: 'server_restart_failed',
        retryable: false,
        fallbackAction: 'disable_github_tools'
      };
    }
  }

  private async handleAuthFailure(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const authService = context.authService as PATAuthService;

    // Validate current token
    try {
      const currentToken = await authService.getToken();
      const isValid = await authService.validateToken(currentToken);

      if (!isValid) {
        return {
          recovered: false,
          action: 'token_invalid',
          retryable: false,
          userAction: 'Please update your GitHub Personal Access Token'
        };
      }
    } catch (tokenError) {
      return {
        recovered: false,
        action: 'token_missing',
        retryable: false,
        userAction: 'Please configure your GitHub Personal Access Token'
      };
    }

    return {
      recovered: false,
      action: 'auth_failure_unknown',
      retryable: true
    };
  }

  private async handleRateLimit(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const resetTime = error.metadata?.resetTime || Date.now() + (60 * 60 * 1000); // 1 hour default
    const waitTime = resetTime - Date.now();

    return {
      recovered: false,
      action: 'rate_limited',
      retryable: true,
      retryAfter: waitTime,
      userAction: `GitHub API rate limit exceeded. Retry after ${new Date(resetTime).toLocaleTimeString()}`
    };
  }
}
```

### 7.2 Figma Remote Error Handling

**File**: `src/mcp/error/figma-error-handler.ts`

```typescript
class FigmaErrorHandler implements ErrorHandler {
  async handleError(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    switch (error.type) {
      case 'CONNECTION_LOST':
        return await this.handleConnectionLoss(error, context);
      case 'TOKEN_EXPIRED':
        return await this.handleTokenExpiry(error, context);
      case 'OAUTH_ERROR':
        return await this.handleOAuthError(error, context);
      case 'RATE_LIMITED':
        return await this.handleRateLimit(error, context);
      default:
        return await this.handleGenericError(error, context);
    }
  }

  private async handleConnectionLoss(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const client = context.client as FigmaRemoteClient;

    // Attempt reconnection with exponential backoff
    const maxAttempts = 5;
    let attempt = 1;

    while (attempt <= maxAttempts) {
      try {
        await this.delay(Math.pow(2, attempt) * 1000);
        await client.reconnect();

        return {
          recovered: true,
          action: 'reconnected',
          retryable: true,
          metadata: { attempts: attempt }
        };
      } catch (reconnectError) {
        attempt++;
      }
    }

    return {
      recovered: false,
      action: 'reconnection_failed',
      retryable: false,
      fallbackAction: 'use_cached_data'
    };
  }

  private async handleTokenExpiry(error: MCPError, context: ErrorContext): Promise<ErrorRecovery> {
    const authService = context.authService as OAuthAuthService;

    try {
      await authService.refreshAccessToken();
      return {
        recovered: true,
        action: 'token_refreshed',
        retryable: true
      };
    } catch (refreshError) {
      return {
        recovered: false,
        action: 'token_refresh_failed',
        retryable: false,
        userAction: 'Please re-authenticate with Figma'
      };
    }
  }
}
```

## 8. Scalability Architecture

### 8.1 Concurrent Execution Manager

**File**: `src/mcp/execution/concurrent-execution-manager.ts`

```typescript
class ConcurrentExecutionManager {
  private maxConcurrentAgents = 5;
  private maxConcurrentToolsPerAgent = 3;
  private maxTotalConcurrentTools = 10;
  private executionQueue = new PriorityQueue<ExecutionTask>();
  private activeExecutions = new Map<string, ExecutionContext>();
  private resourceMonitor: ResourceMonitor;

  constructor() {
    this.resourceMonitor = new ResourceMonitor();
  }

  async executeAgentWorkflow(
    agentId: string,
    request: AgentRequest
  ): Promise<AgentResponse> {
    // Check resource limits
    if (!this.canExecute(agentId)) {
      return await this.queueExecution(agentId, request);
    }

    const executionId = generateId();
    const context = this.createExecutionContext(executionId, agentId, request);

    try {
      this.activeExecutions.set(executionId, context);

      // Monitor resource usage
      this.resourceMonitor.startMonitoring(executionId);

      // Execute workflow
      const result = await this.executeWorkflow(context);

      return result;
    } finally {
      this.activeExecutions.delete(executionId);
      this.resourceMonitor.stopMonitoring(executionId);
      this.processQueue();
    }
  }

  private canExecute(agentId: string): boolean {
    const activeAgents = new Set(
      Array.from(this.activeExecutions.values()).map(ctx => ctx.agentId)
    ).size;

    const totalActiveTools = Array.from(this.activeExecutions.values())
      .reduce((sum, ctx) => sum + ctx.activeToolCount, 0);

    const agentActiveTools = Array.from(this.activeExecutions.values())
      .filter(ctx => ctx.agentId === agentId)
      .reduce((sum, ctx) => sum + ctx.activeToolCount, 0);

    return (
      activeAgents < this.maxConcurrentAgents &&
      totalActiveTools < this.maxTotalConcurrentTools &&
      agentActiveTools < this.maxConcurrentToolsPerAgent
    );
  }

  private async queueExecution(
    agentId: string,
    request: AgentRequest
  ): Promise<AgentResponse> {
    return new Promise((resolve, reject) => {
      const task: ExecutionTask = {
        id: generateId(),
        agentId,
        request,
        priority: request.priority || 'normal',
        queuedAt: Date.now(),
        resolve,
        reject
      };

      this.executionQueue.enqueue(task);
    });
  }
}
```

### 8.2 Resource Monitor

**File**: `src/mcp/monitoring/resource-monitor.ts`

```typescript
class ResourceMonitor {
  private activeMonitors = new Map<string, ResourceUsage>();
  private memoryLimits = {
    perAgent: 256 * 1024 * 1024, // 256MB
    perTool: 128 * 1024 * 1024,  // 128MB
    total: 2 * 1024 * 1024 * 1024 // 2GB
  };

  startMonitoring(executionId: string): void {
    const usage: ResourceUsage = {
      executionId,
      startTime: Date.now(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };

    this.activeMonitors.set(executionId, usage);

    // Set up periodic monitoring
    const interval = setInterval(() => {
      this.updateResourceUsage(executionId);
    }, 1000);

    usage.monitoringInterval = interval;
  }

  stopMonitoring(executionId: string): ResourceReport {
    const usage = this.activeMonitors.get(executionId);
    if (!usage) {
      throw new Error(`No monitoring data for execution: ${executionId}`);
    }

    if (usage.monitoringInterval) {
      clearInterval(usage.monitoringInterval);
    }

    const report: ResourceReport = {
      executionId,
      duration: Date.now() - usage.startTime,
      peakMemoryUsage: usage.peakMemoryUsage || usage.memoryUsage,
      averageCpuUsage: usage.averageCpuUsage || 0,
      resourceViolations: usage.violations || []
    };

    this.activeMonitors.delete(executionId);
    return report;
  }

  private updateResourceUsage(executionId: string): void {
    const usage = this.activeMonitors.get(executionId);
    if (!usage) return;

    const currentMemory = process.memoryUsage();
    const currentCpu = process.cpuUsage(usage.cpuUsage);

    // Update peak memory usage
    if (!usage.peakMemoryUsage || currentMemory.heapUsed > usage.peakMemoryUsage.heapUsed) {
      usage.peakMemoryUsage = currentMemory;
    }

    // Check for resource violations
    if (currentMemory.heapUsed > this.memoryLimits.perAgent) {
      usage.violations = usage.violations || [];
      usage.violations.push({
        type: 'memory_limit_exceeded',
        timestamp: Date.now(),
        value: currentMemory.heapUsed,
        limit: this.memoryLimits.perAgent
      });
    }
  }
}
```

## 9. Monitoring and Telemetry

### 9.1 Metrics Collector

**File**: `src/mcp/monitoring/metrics-collector.ts`

```typescript
class MetricsCollector {
  private metrics = new Map<string, Metric>();
  private prometheusRegistry: Registry;

  constructor() {
    this.prometheusRegistry = new Registry();
    this.initializeMetrics();
  }

  private initializeMetrics(): void {
    // Tool execution metrics
    this.metrics.set('tool_execution_duration', new Histogram({
      name: 'mcp_tool_execution_duration_seconds',
      help: 'Duration of tool executions',
      labelNames: ['tool_name', 'service', 'status'],
      registers: [this.prometheusRegistry]
    }));

    this.metrics.set('tool_execution_count', new Counter({
      name: 'mcp_tool_execution_total',
      help: 'Total number of tool executions',
      labelNames: ['tool_name', 'service', 'status'],
      registers: [this.prometheusRegistry]
    }));

    // Agent workflow metrics
    this.metrics.set('agent_workflow_duration', new Histogram({
      name: 'mcp_agent_workflow_duration_seconds',
      help: 'Duration of agent workflows',
      labelNames: ['agent_type', 'status'],
      registers: [this.prometheusRegistry]
    }));

    // Authentication metrics
    this.metrics.set('auth_success_rate', new Counter({
      name: 'mcp_auth_attempts_total',
      help: 'Total authentication attempts',
      labelNames: ['service', 'auth_type', 'status'],
      registers: [this.prometheusRegistry]
    }));

    // Resource usage metrics
    this.metrics.set('memory_usage', new Gauge({
      name: 'mcp_memory_usage_bytes',
      help: 'Current memory usage',
      labelNames: ['component'],
      registers: [this.prometheusRegistry]
    }));
  }

  recordToolExecution(
    toolName: string,
    service: string,
    duration: number,
    success: boolean
  ): void {
    const status = success ? 'success' : 'failure';

    (this.metrics.get('tool_execution_duration') as Histogram)
      .labels(toolName, service, status)
      .observe(duration / 1000);

    (this.metrics.get('tool_execution_count') as Counter)
      .labels(toolName, service, status)
      .inc();
  }

  recordAgentWorkflow(
    agentType: string,
    duration: number,
    success: boolean
  ): void {
    const status = success ? 'success' : 'failure';

    (this.metrics.get('agent_workflow_duration') as Histogram)
      .labels(agentType, status)
      .observe(duration / 1000);
  }

  recordAuthAttempt(
    service: string,
    authType: string,
    success: boolean
  ): void {
    const status = success ? 'success' : 'failure';

    (this.metrics.get('auth_success_rate') as Counter)
      .labels(service, authType, status)
      .inc();
  }

  updateMemoryUsage(component: string, usage: number): void {
    (this.metrics.get('memory_usage') as Gauge)
      .labels(component)
      .set(usage);
  }

  getMetrics(): string {
    return this.prometheusRegistry.metrics();
  }
}
```

### 9.2 Structured Logger

**File**: `src/mcp/utils/logger.ts`

```typescript
class StructuredLogger {
  private winston: Winston.Logger;

  constructor() {
    this.winston = Winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: Winston.format.combine(
        Winston.format.timestamp(),
        Winston.format.errors({ stack: true }),
        Winston.format.json()
      ),
      transports: [
        new Winston.transports.File({
          filename: 'mcp-error.log',
          level: 'error'
        }),
        new Winston.transports.File({
          filename: 'mcp-combined.log'
        }),
        new Winston.transports.Console({
          format: Winston.format.simple()
        })
      ]
    });
  }

  logToolExecution(
    toolName: string,
    service: string,
    parameters: any,
    result: ToolResult,
    duration: number,
    correlationId: string
  ): void {
    this.winston.info('Tool execution completed', {
      event: 'tool_execution',
      tool_name: toolName,
      service,
      duration_ms: duration,
      success: result.success,
      correlation_id: correlationId,
      parameters: this.sanitizeParameters(parameters),
      result_metadata: result.metadata
    });
  }

  logAgentWorkflow(
    agentId: string,
    agentType: string,
    request: AgentRequest,
    response: AgentResponse,
    duration: number,
    correlationId: string
  ): void {
    this.winston.info('Agent workflow completed', {
      event: 'agent_workflow',
      agent_id: agentId,
      agent_type: agentType,
      duration_ms: duration,
      success: response.success,
      correlation_id: correlationId,
      tools_used: response.toolsUsed?.length || 0,
      request_type: request.type
    });
  }

  logAuthEvent(
    service: string,
    authType: string,
    event: string,
    success: boolean,
    correlationId: string,
    metadata?: any
  ): void {
    this.winston.info('Authentication event', {
      event: 'auth_event',
      service,
      auth_type: authType,
      auth_event: event,
      success,
      correlation_id: correlationId,
      metadata: this.sanitizeMetadata(metadata)
    });
  }

  logError(
    error: Error,
    context: any,
    correlationId: string
  ): void {
    this.winston.error('MCP error occurred', {
      event: 'error',
      error_message: error.message,
      error_stack: error.stack,
      correlation_id: correlationId,
      context: this.sanitizeContext(context)
    });
  }

  private sanitizeParameters(parameters: any): any {
    // Remove sensitive data from parameters
    const sanitized = { ...parameters };
    const sensitiveKeys = ['token', 'password', 'secret', 'key'];

    for (const key of sensitiveKeys) {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}
```

## 10. Security Implementation

### 10.1 Security Validator

**File**: `src/mcp/utils/security-validator.ts`

```typescript
class SecurityValidator {
  private allowedOrigins = ['alpine://', 'vscode://'];
  private maxParameterSize = 1024 * 1024; // 1MB
  private maxToolChainLength = 10;

  validateToolParameters(toolName: string, parameters: any): ValidationResult {
    // Check parameter size
    const parameterSize = JSON.stringify(parameters).length;
    if (parameterSize > this.maxParameterSize) {
      return {
        valid: false,
        error: 'Parameter size exceeds maximum allowed size',
        code: 'PARAMETER_TOO_LARGE'
      };
    }

    // Validate against tool schema
    const schema = this.getToolSchema(toolName);
    if (schema) {
      const ajv = new Ajv();
      const validate = ajv.compile(schema);

      if (!validate(parameters)) {
        return {
          valid: false,
          error: 'Parameters do not match tool schema',
          code: 'SCHEMA_VALIDATION_FAILED',
          details: validate.errors
        };
      }
    }

    // Check for potential security issues
    const securityCheck = this.checkForSecurityIssues(parameters);
    if (!securityCheck.valid) {
      return securityCheck;
    }

    return { valid: true };
  }

  validateOAuthRedirect(redirectUri: string): boolean {
    try {
      const url = new URL(redirectUri);
      return this.allowedOrigins.some(origin =>
        redirectUri.startsWith(origin)
      );
    } catch {
      return false;
    }
  }

  validateToolChain(toolChain: ToolCall[]): ValidationResult {
    if (toolChain.length > this.maxToolChainLength) {
      return {
        valid: false,
        error: 'Tool chain exceeds maximum allowed length',
        code: 'TOOL_CHAIN_TOO_LONG'
      };
    }

    // Check for potential infinite loops
    const toolNames = toolChain.map(call => call.tool);
    const uniqueTools = new Set(toolNames);

    if (toolNames.length > uniqueTools.size * 2) {
      return {
        valid: false,
        error: 'Potential infinite loop detected in tool chain',
        code: 'POTENTIAL_INFINITE_LOOP'
      };
    }

    return { valid: true };
  }

  private checkForSecurityIssues(parameters: any): ValidationResult {
    const parameterString = JSON.stringify(parameters).toLowerCase();

    // Check for potential injection attacks
    const dangerousPatterns = [
      /\beval\b/,
      /\bexec\b/,
      /\bsystem\b/,
      /\bshell\b/,
      /<script/,
      /javascript:/,
      /data:text\/html/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(parameterString)) {
        return {
          valid: false,
          error: 'Potentially dangerous content detected in parameters',
          code: 'SECURITY_VIOLATION'
        };
      }
    }

    return { valid: true };
  }
}
```

This comprehensive technical documentation provides:

1. **Complete MCP Host Implementation** - Desktop and VS Code hosts with agent orchestration
2. **Detailed Client Architecture** - GitHub (stdio) and Figma (SSE) clients with transport protocols
3. **Authentication Systems** - PAT for GitHub and OAuth with PKCE for Figma
4. **AI Agent Implementation** - Code and Design assistant agents with tool planning
5. **Message Flow Diagrams** - Visual representation of request/response cycles
6. **Error Handling & Recovery** - Comprehensive error handling for both services
7. **Scalability Architecture** - Concurrent execution management and resource monitoring
8. **Monitoring & Telemetry** - Metrics collection and structured logging
9. **Security Implementation** - Parameter validation and security checks

The implementation is designed to be scalable, reliable, and secure, with proper separation of concerns and comprehensive error handling throughout the system.
