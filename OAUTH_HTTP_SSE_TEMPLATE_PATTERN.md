# OAuth + HTTP/SSE Template <PERSON><PERSON> for Remote Service Integration

## Overview
This document defines the standardized OAuth + HTTP/SSE template pattern established for all remote service integrations in Alpine Intellect. This pattern was first implemented for GitHub and Figma, and serves as the template for all future service integrations (Slack, Discord, etc.).

## Core Pattern Components

### 1. **OAuth Server Discovery (RFC 8414)**
All remote services support OAuth server metadata discovery for dynamic configuration:

```typescript
interface AuthorizationServerMetadata {
  issuer: string;
  authorization_endpoint: string;
  token_endpoint: string;
  scopes_supported: string[];
  response_types_supported: string[];
  grant_types_supported: string[];
  code_challenge_methods_supported: string[];
  mcp_capabilities: {
    tools: boolean;
    resources: boolean;
    prompts: boolean;
    notifications: boolean;
  };
  mcp_endpoints: {
    http: string;
    sse: string;
  };
}

class OAuthDiscoveryService {
  async discoverServer(discoveryUrl: string): Promise<AuthorizationServerMetadata> {
    const response = await fetch(`${discoveryUrl}/.well-known/oauth-authorization-server`);
    const metadata = await response.json();

    // Validate required fields and PKCE support
    this.validateMetadata(metadata);
    return metadata;
  }
}
```

### 2. **OAuth 2.0 + PKCE Authentication**
All remote services use OAuth 2.0 with PKCE for secure authentication:

```typescript
interface OAuthServiceConfig {
  serviceId: string;
  displayName: string;
  clientId: string;
  authorizationEndpoint: string;
  tokenEndpoint: string;
  scopes: string[];
  redirectUri: string; // thealpinecode.alpineintellect://auth-callback/{service}
}

class OAuthService {
  async initializeOAuth(): Promise<string> {
    // Generate PKCE parameters
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    const state = this.generateState();
    
    // Store PKCE parameters securely
    await keytar.setPassword('alpine-app', `${this.serviceId}-code-verifier`, codeVerifier);
    await keytar.setPassword('alpine-app', `${this.serviceId}-oauth-state`, state);
    
    // Build authorization URL
    const authUrl = new URL(this.config.authorizationEndpoint);
    authUrl.searchParams.set('client_id', this.config.clientId);
    authUrl.searchParams.set('redirect_uri', this.config.redirectUri);
    authUrl.searchParams.set('scope', this.config.scopes.join(' '));
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');
    
    return authUrl.toString();
  }
  
  async exchangeCodeForTokens(code: string, state: string): Promise<TokenResponse> {
    // Validate state parameter
    const storedState = await keytar.getPassword('alpine-app', `${this.serviceId}-oauth-state`);
    if (state !== storedState) {
      throw new Error('Invalid state parameter');
    }
    
    // Get stored code verifier
    const codeVerifier = await keytar.getPassword('alpine-app', `${this.serviceId}-code-verifier`);
    
    // Exchange code for tokens
    const response = await fetch(this.config.tokenEndpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: this.config.clientId,
        client_secret: await this.getClientSecret(),
        code,
        redirect_uri: this.config.redirectUri,
        code_verifier: codeVerifier
      })
    });
    
    return await response.json();
  }
}
```

### 2. **HTTP/SSE Transport Layer**
All services use HTTP for requests and SSE for real-time events:

```typescript
class HTTPSSEClient {
  private httpClient: HTTPClient;
  private eventSource: EventSource;
  private oauthService: OAuthService;
  
  async initialize(): Promise<void> {
    // Ensure valid OAuth token
    await this.oauthService.ensureValidToken();
    
    // Initialize HTTP client
    this.httpClient = new HTTPClient({
      baseURL: this.config.httpEndpoint,
      headers: {
        'Authorization': `Bearer ${await this.oauthService.getAccessToken()}`
      }
    });
    
    // Initialize SSE connection
    this.eventSource = new EventSource(this.config.sseEndpoint, {
      headers: {
        'Authorization': `Bearer ${await this.oauthService.getAccessToken()}`
      }
    });
    
    this.setupEventHandlers();
  }
  
  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    const request: MCPRequest = {
      id: generateId(),
      type: 'request',
      method: 'tools/call',
      params: { name: toolName, arguments: parameters }
    };
    
    const response = await this.httpClient.post('/mcp/v1/tools', request);
    return this.processToolResponse(response.data);
  }
  
  private setupEventHandlers(): void {
    this.eventSource.onmessage = (event) => {
      const message = JSON.parse(event.data) as MCPMessage;
      this.handleNotification(message);
    };
    
    this.eventSource.onerror = (error) => {
      this.handleConnectionError(error);
    };
  }
}
```

### 3. **MCP Configuration Integration**
All services support configuration-driven initialization with discovery:

```typescript
interface ServiceConfiguration {
  transport: 'http-sse';
  enabled: boolean;
  endpoints: {
    http: string;
    sse: string;
    discovery: string;
  };
  auth: {
    type: 'oauth';
    client_id: string;
    redirect_uri: string;
    scopes: string[];
    pkce: boolean;
  };
  capabilities: string[];
  tools: string[];
}

class ConfigurationDrivenService {
  constructor(
    private serviceId: string,
    private config: ServiceConfiguration,
    private discoveryClient: OAuthDiscoveryService
  ) {}

  async initialize(): Promise<void> {
    // Discover OAuth metadata if discovery endpoint provided
    if (this.config.endpoints.discovery) {
      try {
        this.metadata = await this.discoveryClient.discoverServer(
          this.config.endpoints.discovery
        );
        this.validateConfigurationAgainstMetadata();
      } catch (error) {
        console.warn(`Discovery failed for ${this.serviceId}, using static config`);
      }
    }

    // Initialize OAuth and HTTP/SSE components
    await this.initializeOAuthService();
    await this.initializeHTTPSSEClient();
  }
}
```

### 4. **Service Registration Pattern**
All services register with the central service registry:

```typescript
interface RemoteService {
  readonly serviceId: string;
  readonly displayName: string;
  readonly callbackPath: string;
  readonly endpoints: ServiceEndpoints;
  
  initialize(): Promise<void>;
  authenticate(): Promise<string>; // Returns auth URL
  handleCallback(params: URLSearchParams): Promise<void>;
  executeTool(toolName: string, parameters: any): Promise<ToolResult>;
  getAuthStatus(): Promise<AuthStatus>;
}

class ServiceRegistry {
  private services = new Map<string, RemoteService>();
  
  registerService(service: RemoteService): void {
    this.services.set(service.serviceId, service);
  }
  
  getServiceByCallbackPath(path: string): RemoteService | undefined {
    for (const service of this.services.values()) {
      if (path.startsWith(service.callbackPath)) {
        return service;
      }
    }
    return undefined;
  }
}
```

## Implementation Template for New Services

### Step 1: Add Service to MCP Configuration
```json
{
  "servers": {
    "slack": {
      "transport": "http-sse",
      "enabled": true,
      "endpoints": {
        "http": "https://api.slack.com/mcp/v1",
        "sse": "https://api.slack.com/mcp/v1/events",
        "discovery": "https://api.slack.com/mcp/.well-known/oauth-authorization-server"
      },
      "auth": {
        "type": "oauth",
        "client_id": "${ENV:SLACK_CLIENT_ID}",
        "redirect_uri": "thealpinecode.alpineintellect://auth-callback/slack",
        "scopes": ["channels:read", "chat:write", "files:read"],
        "pkce": true
      },
      "capabilities": ["tools", "notifications"],
      "tools": ["send_message", "create_channel", "upload_file"],
      "connection": {
        "timeout": 30000,
        "retry_attempts": 3,
        "retry_delay": 1000
      }
    }
  }
}
```

### Step 2: Implement Configuration-Driven Service Class
```typescript
class SlackService extends ConfigurationDrivenService implements RemoteService {
  readonly serviceId = 'slack';
  readonly displayName = 'Slack';
  readonly callbackPath = '/auth-callback/slack';

  constructor(config: ServiceConfiguration, discoveryClient: OAuthDiscoveryService) {
    super('slack', config, discoveryClient);
  }

  async initialize(): Promise<void> {
    // Perform discovery and initialize OAuth + HTTP/SSE
    await super.initialize();

    // Service-specific initialization
    await this.setupSlackSpecificFeatures();
  }

  async authenticate(): Promise<string> {
    return await this.oauthService.initializeOAuth();
  }

  async handleCallback(params: URLSearchParams): Promise<void> {
    const code = params.get('code');
    const state = params.get('state');

    if (code && state) {
      await this.oauthService.exchangeCodeForTokens(code, state);
      await this.initialize();
    }
  }

  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    return await this.httpSseClient.executeTool(toolName, parameters);
  }

  async getAuthStatus(): Promise<AuthStatus> {
    return await this.oauthService.getAuthStatus();
  }

  private async setupSlackSpecificFeatures(): Promise<void> {
    // Slack-specific initialization logic
    await this.subscribeToSlackEvents();
  }
}
```

### Step 3: Register Service via Configuration
```typescript
// Services are automatically registered from configuration
const configManager = new MCPConfigurationManager();
await configManager.loadConfiguration('./mcp-config.json');

const serviceRegistry = new EnhancedServiceRegistry();
await serviceRegistry.initializeFromConfiguration('./mcp-config.json');

// Slack service is automatically created and registered based on configuration
```

### Step 4: Add UI Components
```typescript
// Service card component following established pattern
const SlackServiceCard: React.FC = () => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>('disconnected');
  
  const handleConnect = async () => {
    const authUrl = await window.electronAPI.authenticateService('slack');
    shell.openExternal(authUrl);
  };
  
  return (
    <ServiceCard
      serviceName="Slack"
      status={authStatus}
      onConnect={handleConnect}
      onDisconnect={() => window.electronAPI.disconnectService('slack')}
    />
  );
};
```

## Benefits of This Pattern

### 1. **Consistency**
- All services use the same OAuth + HTTP/SSE pattern
- Consistent authentication flow across all integrations
- Uniform error handling and recovery mechanisms

### 2. **Security**
- OAuth 2.0 + PKCE for all services
- Secure token storage using keytar
- Consistent redirect URI validation

### 3. **Scalability**
- Easy to add new services following the template
- Shared infrastructure reduces development time
- Consistent user experience across all services

### 4. **Maintainability**
- Single pattern to maintain and update
- Shared components reduce code duplication
- Consistent debugging and monitoring

## Service Integration Checklist

When adding a new service, ensure:

- [ ] OAuth 2.0 + PKCE configuration created
- [ ] Service class implements RemoteService interface
- [ ] HTTP/SSE endpoints configured
- [ ] Callback URL follows pattern: `thealpinecode.alpineintellect://auth-callback/{service}`
- [ ] Service registered with ServiceRegistry
- [ ] UI components follow established patterns
- [ ] Token storage uses 'alpine-app' keytar service
- [ ] Error handling follows established patterns
- [ ] Health monitoring implemented
- [ ] Documentation updated

## Future Extensibility

This pattern supports:
- **Unlimited Services**: Any OAuth-enabled service can be integrated
- **Consistent UX**: All services follow the same authentication flow
- **Shared Infrastructure**: HTTP/SSE transport, OAuth management, error handling
- **Easy Maintenance**: Single pattern to update and maintain
- **Rapid Development**: New services can be added in hours, not days

The OAuth + HTTP/SSE template pattern ensures that Alpine Intellect can rapidly integrate with any OAuth-enabled service while maintaining consistency, security, and reliability across all integrations.
