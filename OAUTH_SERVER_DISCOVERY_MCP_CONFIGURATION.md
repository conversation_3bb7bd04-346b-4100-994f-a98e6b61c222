# OAuth Server Discovery and MCP Configuration for HTTP/SSE Remote Services

## Overview

This document defines the implementation of OAuth Server Discovery and MCP Configuration mechanisms for Alpine Intellect's HTTP/SSE remote service integrations. This standardizes how MCP clients discover OAuth endpoints, capabilities, and configuration for remote services (GitHub, Figma, and future integrations).

## 1. OAuth Server Metadata Discovery

### 1.1 Authorization Server Metadata (RFC 8414)

Each MCP server exposes OAuth authorization server metadata at the well-known endpoint:

**Endpoint**: `/.well-known/oauth-authorization-server`

#### GitHub Example Response:
```json
{
  "issuer": "https://api.github.com/mcp",
  "authorization_endpoint": "https://github.com/login/oauth/authorize",
  "token_endpoint": "https://github.com/login/oauth/access_token",
  "scopes_supported": [
    "repo", "user", "read:org", "write:org", "admin:org",
    "public_repo", "repo:status", "repo_deployment"
  ],
  "response_types_supported": ["code"],
  "grant_types_supported": ["authorization_code", "refresh_token"],
  "code_challenge_methods_supported": ["S256"],
  "token_endpoint_auth_methods_supported": ["client_secret_post"],
  "revocation_endpoint": "https://github.com/settings/connections/applications/{client_id}",
  "mcp_capabilities": {
    "tools": true,
    "resources": false,
    "prompts": false,
    "notifications": true
  },
  "mcp_endpoints": {
    "http": "https://api.github.com/mcp/v1",
    "sse": "https://api.github.com/mcp/v1/events"
  }
}
```

#### Figma Example Response:
```json
{
  "issuer": "https://api.figma.com/mcp",
  "authorization_endpoint": "https://www.figma.com/oauth",
  "token_endpoint": "https://www.figma.com/api/oauth/token",
  "scopes_supported": [
    "file:read", "file:write", "team:read", "webhooks:write"
  ],
  "response_types_supported": ["code"],
  "grant_types_supported": ["authorization_code", "refresh_token"],
  "code_challenge_methods_supported": ["S256"],
  "token_endpoint_auth_methods_supported": ["client_secret_post"],
  "revocation_endpoint": "https://www.figma.com/api/oauth/revoke",
  "mcp_capabilities": {
    "tools": true,
    "resources": true,
    "prompts": false,
    "notifications": true
  },
  "mcp_endpoints": {
    "http": "https://api.figma.com/mcp/v1",
    "sse": "https://api.figma.com/mcp/v1/events"
  }
}
```

### 1.2 Protected Resource Metadata (RFC 8707)

Each MCP server exposes protected resource metadata at:

**Endpoint**: `/.well-known/oauth-protected-resource`

#### GitHub Example Response:
```json
{
  "resource": "https://api.github.com/mcp",
  "authorization_servers": ["https://api.github.com/mcp"],
  "scopes_supported": [
    "repo", "user", "read:org", "write:org"
  ],
  "bearer_methods_supported": ["header"],
  "resource_documentation": "https://docs.github.com/en/developers/apps/mcp-integration",
  "mcp_tools": [
    {
      "name": "create_repository",
      "required_scopes": ["repo"],
      "description": "Create a new repository"
    },
    {
      "name": "create_issue",
      "required_scopes": ["repo"],
      "description": "Create an issue in a repository"
    },
    {
      "name": "search_code",
      "required_scopes": ["repo"],
      "description": "Search for code in repositories"
    }
  ]
}
```

## 2. MCP Configuration Schema

### 2.1 Configuration File Structure

**File**: `mcp-config.json`

```json
{
  "$schema": "https://schemas.alpine-intellect.com/mcp-config.schema.json",
  "version": "1.0",
  "servers": {
    "github": {
      "transport": "http-sse",
      "enabled": true,
      "endpoints": {
        "http": "https://api.github.com/mcp/v1",
        "sse": "https://api.github.com/mcp/v1/events",
        "discovery": "https://api.github.com/mcp/.well-known/oauth-authorization-server"
      },
      "auth": {
        "type": "oauth",
        "client_id": "${ENV:GITHUB_CLIENT_ID}",
        "redirect_uri": "thealpinecode.alpineintellect://auth-callback/github",
        "scopes": ["repo", "user", "read:org"],
        "pkce": true
      },
      "capabilities": ["tools", "notifications"],
      "tools": ["create_repository", "create_issue", "search_code"],
      "connection": {
        "timeout": 30000,
        "retry_attempts": 3,
        "retry_delay": 1000
      },
      "cache": {
        "enabled": true,
        "ttl": 300000
      }
    },
    "figma": {
      "transport": "http-sse",
      "enabled": true,
      "endpoints": {
        "http": "https://api.figma.com/mcp/v1",
        "sse": "https://api.figma.com/mcp/v1/events",
        "discovery": "https://api.figma.com/mcp/.well-known/oauth-authorization-server"
      },
      "auth": {
        "type": "oauth",
        "client_id": "${ENV:FIGMA_CLIENT_ID}",
        "redirect_uri": "thealpinecode.alpineintellect://auth-callback/figma",
        "scopes": ["file:read", "file:write", "team:read"],
        "pkce": true
      },
      "capabilities": ["tools", "resources", "notifications"],
      "tools": ["get_file_content", "export_assets", "create_component"],
      "connection": {
        "timeout": 30000,
        "retry_attempts": 3,
        "retry_delay": 1000
      },
      "cache": {
        "enabled": true,
        "ttl": 300000
      }
    }
  },
  "global": {
    "discovery": {
      "enabled": true,
      "timeout": 10000,
      "cache_duration": 3600000
    },
    "security": {
      "validate_certificates": true,
      "require_pkce": true,
      "allowed_redirect_schemes": ["thealpinecode.alpineintellect"]
    }
  }
}
```

### 2.2 Configuration Schema Definition

```typescript
interface MCPConfiguration {
  $schema?: string;
  version: string;
  servers: Record<string, ServerConfiguration>;
  global: GlobalConfiguration;
}

interface ServerConfiguration {
  transport: 'http-sse' | 'stdio' | 'websocket';
  enabled: boolean;
  endpoints: {
    http: string;
    sse: string;
    discovery: string;
  };
  auth: {
    type: 'oauth' | 'api-key' | 'none';
    client_id: string;
    redirect_uri: string;
    scopes: string[];
    pkce: boolean;
  };
  capabilities: ('tools' | 'resources' | 'prompts' | 'notifications')[];
  tools: string[];
  connection: {
    timeout: number;
    retry_attempts: number;
    retry_delay: number;
  };
  cache: {
    enabled: boolean;
    ttl: number;
  };
}

interface GlobalConfiguration {
  discovery: {
    enabled: boolean;
    timeout: number;
    cache_duration: number;
  };
  security: {
    validate_certificates: boolean;
    require_pkce: boolean;
    allowed_redirect_schemes: string[];
  };
}
```

## 3. Implementation Components

### 3.1 OAuth Discovery Client

```typescript
class OAuthDiscoveryClient {
  private cache = new Map<string, DiscoveryMetadata>();
  
  async discoverAuthorizationServer(discoveryUrl: string): Promise<AuthorizationServerMetadata> {
    const cached = this.cache.get(discoveryUrl);
    if (cached && !this.isExpired(cached)) {
      return cached.authServerMetadata;
    }
    
    const response = await fetch(discoveryUrl, {
      headers: { 'Accept': 'application/json' },
      timeout: this.config.discovery.timeout
    });
    
    if (!response.ok) {
      throw new Error(`Discovery failed: ${response.status} ${response.statusText}`);
    }
    
    const metadata = await response.json() as AuthorizationServerMetadata;
    this.validateMetadata(metadata);
    
    this.cache.set(discoveryUrl, {
      authServerMetadata: metadata,
      timestamp: Date.now()
    });
    
    return metadata;
  }
  
  async discoverProtectedResource(resourceUrl: string): Promise<ProtectedResourceMetadata> {
    const discoveryUrl = `${resourceUrl}/.well-known/oauth-protected-resource`;
    
    const response = await fetch(discoveryUrl, {
      headers: { 'Accept': 'application/json' },
      timeout: this.config.discovery.timeout
    });
    
    if (!response.ok) {
      throw new Error(`Resource discovery failed: ${response.status}`);
    }
    
    return await response.json() as ProtectedResourceMetadata;
  }
  
  private validateMetadata(metadata: AuthorizationServerMetadata): void {
    const required = ['issuer', 'authorization_endpoint', 'token_endpoint'];
    for (const field of required) {
      if (!metadata[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    if (!metadata.code_challenge_methods_supported?.includes('S256')) {
      throw new Error('PKCE S256 not supported');
    }
  }
}
```

### 3.2 MCP Configuration Manager

```typescript
class MCPConfigurationManager {
  private config: MCPConfiguration;
  private discoveryClient: OAuthDiscoveryClient;
  
  async loadConfiguration(configPath: string): Promise<void> {
    const configContent = await fs.readFile(configPath, 'utf-8');
    const rawConfig = JSON.parse(configContent);
    
    // Substitute environment variables
    this.config = this.substituteEnvironmentVariables(rawConfig);
    
    // Validate configuration
    await this.validateConfiguration();
    
    // Perform discovery for enabled servers
    if (this.config.global.discovery.enabled) {
      await this.performDiscovery();
    }
  }
  
  private substituteEnvironmentVariables(config: any): MCPConfiguration {
    const configStr = JSON.stringify(config);
    const substituted = configStr.replace(/\$\{ENV:([^}]+)\}/g, (match, envVar) => {
      const value = process.env[envVar];
      if (!value) {
        throw new Error(`Environment variable not found: ${envVar}`);
      }
      return value;
    });
    
    return JSON.parse(substituted);
  }
  
  private async performDiscovery(): Promise<void> {
    for (const [serverId, serverConfig] of Object.entries(this.config.servers)) {
      if (!serverConfig.enabled) continue;
      
      try {
        const metadata = await this.discoveryClient.discoverAuthorizationServer(
          serverConfig.endpoints.discovery
        );
        
        // Validate discovered metadata against configuration
        this.validateDiscoveredMetadata(serverConfig, metadata);
        
        console.log(`Discovery successful for ${serverId}`);
      } catch (error) {
        console.warn(`Discovery failed for ${serverId}:`, error.message);
        // Continue with static configuration
      }
    }
  }
  
  private validateDiscoveredMetadata(
    config: ServerConfiguration,
    metadata: AuthorizationServerMetadata
  ): void {
    // Validate scopes
    const unsupportedScopes = config.auth.scopes.filter(
      scope => !metadata.scopes_supported.includes(scope)
    );
    
    if (unsupportedScopes.length > 0) {
      throw new Error(`Unsupported scopes: ${unsupportedScopes.join(', ')}`);
    }
    
    // Validate PKCE requirement
    if (config.auth.pkce && !metadata.code_challenge_methods_supported?.includes('S256')) {
      throw new Error('PKCE S256 required but not supported');
    }
  }
  
  getServerConfiguration(serverId: string): ServerConfiguration | undefined {
    return this.config.servers[serverId];
  }
  
  getEnabledServers(): string[] {
    return Object.entries(this.config.servers)
      .filter(([_, config]) => config.enabled)
      .map(([serverId, _]) => serverId);
  }
}
```

## 4. Integration with Existing Architecture

### 4.1 Enhanced Service Registration

```typescript
class EnhancedServiceRegistry {
  private configManager: MCPConfigurationManager;
  private discoveryClient: OAuthDiscoveryClient;
  
  async initializeFromConfiguration(configPath: string): Promise<void> {
    await this.configManager.loadConfiguration(configPath);
    
    const enabledServers = this.configManager.getEnabledServers();
    
    for (const serverId of enabledServers) {
      const config = this.configManager.getServerConfiguration(serverId);
      await this.createAndRegisterService(serverId, config);
    }
  }
  
  private async createAndRegisterService(
    serverId: string,
    config: ServerConfiguration
  ): Promise<void> {
    const service = new RemoteOAuthService({
      serviceId: serverId,
      config,
      discoveryClient: this.discoveryClient
    });
    
    await service.initialize();
    this.registerService(service);
  }
}
```

### 4.2 Configuration-Driven OAuth Service

```typescript
class RemoteOAuthService implements RemoteService {
  constructor(
    private options: {
      serviceId: string;
      config: ServerConfiguration;
      discoveryClient: OAuthDiscoveryClient;
    }
  ) {}
  
  async initialize(): Promise<void> {
    // Discover OAuth metadata if enabled
    if (this.options.config.endpoints.discovery) {
      try {
        this.metadata = await this.options.discoveryClient.discoverAuthorizationServer(
          this.options.config.endpoints.discovery
        );
      } catch (error) {
        console.warn(`Discovery failed for ${this.serviceId}, using static config`);
      }
    }
    
    // Initialize HTTP/SSE client with configuration
    this.httpSseClient = new HTTPSSEClient({
      httpEndpoint: this.options.config.endpoints.http,
      sseEndpoint: this.options.config.endpoints.sse,
      oauthConfig: this.options.config.auth,
      connectionConfig: this.options.config.connection,
      cacheConfig: this.options.config.cache
    });
    
    await this.httpSseClient.initialize();
  }
}
```

## 5. Security Considerations

### 5.1 Configuration Security
- **No Secrets in Config**: Client secrets stored in keytar, not configuration files
- **Environment Variables**: Sensitive data via environment variables only
- **Validation**: Strict validation of discovered metadata against expected values
- **Certificate Validation**: Always validate TLS certificates for discovery endpoints

### 5.2 Discovery Security
- **Timeout Protection**: Prevent hanging on discovery requests
- **Fallback Mechanism**: Continue with static configuration if discovery fails
- **Metadata Validation**: Validate all discovered metadata before use
- **Cache Security**: Secure caching of discovery metadata with appropriate TTL

## 6. Testing and Validation

### 6.1 Discovery Endpoint Testing
```bash
# Test GitHub discovery
curl -H "Accept: application/json" \
  https://api.github.com/mcp/.well-known/oauth-authorization-server

# Test Figma discovery
curl -H "Accept: application/json" \
  https://api.figma.com/mcp/.well-known/oauth-authorization-server
```

### 6.2 Configuration Validation
```typescript
// Validate configuration schema
const validator = new ConfigurationValidator();
const isValid = await validator.validate(config);

// Test environment variable substitution
const substituted = configManager.substituteEnvironmentVariables(rawConfig);

// Test discovery integration
const discoveryResult = await discoveryClient.discoverAuthorizationServer(discoveryUrl);
```

This implementation provides a robust, secure, and scalable foundation for OAuth server discovery and MCP configuration that integrates seamlessly with the existing OAuth + HTTP/SSE template pattern.
