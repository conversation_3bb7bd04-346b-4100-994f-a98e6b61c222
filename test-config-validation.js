/**
 * Simple Node.js script to test configuration validation
 * Run with: node test-config-validation.js
 */

const fs = require('fs');
const path = require('path');

// Test the current mcp-config.json file
async function testCurrentConfig() {
  try {
    console.log('🧪 Testing current mcp-config.json validation...\n');

    // Read the current config file
    const configPath = path.join(__dirname, 'mcp-config.json');
    const configContent = fs.readFileSync(configPath, 'utf-8');
    const config = JSON.parse(configContent);

    console.log('📋 Configuration loaded successfully');
    console.log(`   Servers: ${Object.keys(config.servers).join(', ')}`);

    // Check each server configuration
    for (const [serverId, serverConfig] of Object.entries(config.servers)) {
      console.log(`\n🔍 Checking server: ${serverId}`);
      console.log(`   Transport: ${serverConfig.transport}`);
      
      if (serverConfig.transport === 'stdio') {
        if (serverConfig.stdio) {
          console.log(`   ✅ Stdio configuration present`);
          console.log(`   Executable: ${serverConfig.stdio.executable}`);
          console.log(`   Args: ${serverConfig.stdio.args ? serverConfig.stdio.args.join(', ') : 'none'}`);
        } else {
          console.log(`   ❌ Missing stdio configuration for stdio transport`);
        }
      } else if (serverConfig.transport === 'http-sse' || serverConfig.transport === 'websocket') {
        if (serverConfig.endpoints) {
          console.log(`   ✅ Endpoints configuration present`);
          console.log(`   HTTP: ${serverConfig.endpoints.http}`);
          if (serverConfig.endpoints.sse) {
            console.log(`   SSE: ${serverConfig.endpoints.sse}`);
          }
        } else {
          console.log(`   ❌ Missing endpoints configuration for ${serverConfig.transport} transport`);
        }
      }

      // Check auth configuration
      if (serverConfig.auth) {
        console.log(`   Auth type: ${serverConfig.auth.type}`);
        console.log(`   PKCE: ${serverConfig.auth.pkce}`);
      }
    }

    console.log('\n✅ Configuration validation test completed successfully!');
    console.log('\nThe configuration should now work with the updated validation logic.');

  } catch (error) {
    console.error('❌ Configuration test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testCurrentConfig();
