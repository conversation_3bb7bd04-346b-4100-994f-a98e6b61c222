# OAuth Server Discovery and MCP Configuration Implementation Checklist

## Overview
This checklist ensures complete implementation of OAuth Server Discovery and MCP Configuration for HTTP/SSE Remote Services in Alpine Intellect, following RFC 8414 and established OAuth + HTTP/SSE patterns.

## Phase 1: OAuth Discovery Infrastructure

### 1.1 OAuth Authorization Server Metadata (RFC 8414)
- [ ] **Implement OAuth Discovery Client**
  - [ ] Create `src/mcp/discovery/oauth-discovery-client.ts`
  - [ ] Support `/.well-known/oauth-authorization-server` endpoint discovery
  - [ ] Implement metadata validation (required fields, PKCE support)
  - [ ] Add discovery timeout and error handling
  - [ ] Implement discovery metadata caching with TTL

- [ ] **Create Discovery Cache System**
  - [ ] Create `src/mcp/discovery/discovery-cache.ts`
  - [ ] Implement in-memory cache with configurable TTL
  - [ ] Add cache invalidation mechanisms
  - [ ] Support cache persistence for offline scenarios

- [ ] **Validate Discovery Responses**
  - [ ] Verify required fields: `issuer`, `authorization_endpoint`, `token_endpoint`
  - [ ] Validate PKCE support: `code_challenge_methods_supported` includes "S256"
  - [ ] Check supported grant types include "authorization_code"
  - [ ] Validate MCP-specific extensions in metadata

### 1.2 OAuth Protected Resource Metadata (RFC 8707)
- [ ] **Implement Protected Resource Discovery**
  - [ ] Support `/.well-known/oauth-protected-resource` endpoint
  - [ ] Discover resource-specific OAuth requirements
  - [ ] Extract MCP tool scope requirements
  - [ ] Validate authorization server references

- [ ] **Tool Scope Mapping**
  - [ ] Map MCP tools to required OAuth scopes
  - [ ] Validate user permissions against tool requirements
  - [ ] Implement scope-based tool filtering
  - [ ] Support dynamic tool discovery based on scopes

## Phase 2: MCP Configuration System

### 2.1 Configuration Schema Implementation
- [ ] **Create MCP Configuration Schema**
  - [ ] Define TypeScript interfaces for configuration structure
  - [ ] Create JSON schema for validation
  - [ ] Support environment variable substitution (`${ENV:VAR_NAME}`)
  - [ ] Implement configuration validation

- [ ] **Configuration Manager Implementation**
  - [ ] Create `src/mcp/config/mcp-configuration-manager.ts`
  - [ ] Implement configuration loading and parsing
  - [ ] Add environment variable substitution
  - [ ] Support configuration hot-reloading
  - [ ] Implement configuration validation against schema

- [ ] **Security Considerations**
  - [ ] Ensure no secrets in configuration files
  - [ ] Validate environment variable requirements
  - [ ] Implement secure configuration storage
  - [ ] Add configuration access logging

### 2.2 Service Configuration Integration
- [ ] **GitHub Service Configuration**
  - [ ] Add GitHub configuration to `mcp-config.json`
  - [ ] Configure discovery endpoint: `https://api.github.com/mcp/.well-known/oauth-authorization-server`
  - [ ] Set OAuth parameters: client_id, redirect_uri, scopes
  - [ ] Configure HTTP/SSE endpoints
  - [ ] Add connection and cache settings

- [ ] **Figma Service Configuration**
  - [ ] Add Figma configuration to `mcp-config.json`
  - [ ] Configure discovery endpoint: `https://api.figma.com/mcp/.well-known/oauth-authorization-server`
  - [ ] Set OAuth parameters: client_id, redirect_uri, scopes
  - [ ] Configure HTTP/SSE endpoints
  - [ ] Add connection and cache settings

## Phase 3: Integration with Existing Architecture

### 3.1 Enhanced Service Registry
- [ ] **Update Service Registry**
  - [ ] Modify `src/mcp/auth/oauth-service-registry.ts` to support configuration-driven initialization
  - [ ] Implement automatic service registration from configuration
  - [ ] Add discovery integration to service creation
  - [ ] Support dynamic service enable/disable

- [ ] **Configuration-Driven Service Creation**
  - [ ] Create base `ConfigurationDrivenService` class
  - [ ] Implement automatic OAuth discovery in service initialization
  - [ ] Add fallback to static configuration if discovery fails
  - [ ] Support service-specific configuration validation

### 3.2 MCP Host Integration
- [ ] **Update MCP Host**
  - [ ] Modify `src/mcp/host/desktop-mcp-host.ts` to load configuration
  - [ ] Add discovery client initialization
  - [ ] Implement configuration-driven client initialization
  - [ ] Add discovery health monitoring

- [ ] **Configuration Loading**
  - [ ] Add configuration path parameter to host initialization
  - [ ] Implement configuration validation on startup
  - [ ] Add configuration reload capabilities
  - [ ] Support configuration change notifications

### 3.3 OAuth Service Updates
- [ ] **GitHub OAuth Service**
  - [ ] Update `src/mcp/auth/github-oauth-service.ts` to use discovery
  - [ ] Integrate with configuration manager
  - [ ] Add discovered endpoint validation
  - [ ] Implement fallback to static configuration

- [ ] **Figma OAuth Service**
  - [ ] Update `src/mcp/auth/figma-oauth-service.ts` to use discovery
  - [ ] Follow GitHub pattern for discovery integration
  - [ ] Reuse discovery client and configuration manager
  - [ ] Implement consistent error handling

## Phase 4: Testing and Validation

### 4.1 Discovery Endpoint Testing
- [ ] **GitHub Discovery Testing**
  - [ ] Test `https://api.github.com/mcp/.well-known/oauth-authorization-server`
  - [ ] Validate response format and required fields
  - [ ] Test PKCE capability detection
  - [ ] Verify MCP-specific extensions

- [ ] **Figma Discovery Testing**
  - [ ] Test `https://api.figma.com/mcp/.well-known/oauth-authorization-server`
  - [ ] Validate response format and required fields
  - [ ] Test PKCE capability detection
  - [ ] Verify MCP-specific extensions

### 4.2 Configuration Testing
- [ ] **Configuration Validation**
  - [ ] Test valid configuration loading
  - [ ] Test invalid configuration rejection
  - [ ] Test environment variable substitution
  - [ ] Test missing environment variable handling

- [ ] **Discovery Integration Testing**
  - [ ] Test successful discovery scenarios
  - [ ] Test discovery failure fallback
  - [ ] Test discovery timeout handling
  - [ ] Test cache functionality

### 4.3 End-to-End Testing
- [ ] **GitHub Integration Testing**
  - [ ] Test complete OAuth flow with discovery
  - [ ] Test tool execution with discovered endpoints
  - [ ] Test error scenarios and recovery
  - [ ] Test configuration changes and reloading

- [ ] **Figma Integration Testing**
  - [ ] Test complete OAuth flow with discovery
  - [ ] Test tool execution with discovered endpoints
  - [ ] Test error scenarios and recovery
  - [ ] Test configuration changes and reloading

## Phase 5: Documentation and Future Extensibility

### 5.1 Documentation Updates
- [ ] **Update Implementation Guides**
  - [ ] Update GitHub OAuth implementation guide with discovery
  - [ ] Update Figma OAuth implementation guide with discovery
  - [ ] Update OAuth + HTTP/SSE template pattern with discovery
  - [ ] Create discovery troubleshooting guide

- [ ] **Configuration Documentation**
  - [ ] Document MCP configuration schema
  - [ ] Create configuration examples for new services
  - [ ] Document environment variable requirements
  - [ ] Create configuration migration guide

### 5.2 Future Service Template
- [ ] **Template Documentation**
  - [ ] Create step-by-step guide for adding new services
  - [ ] Document discovery endpoint requirements
  - [ ] Create configuration template for new services
  - [ ] Document testing procedures for new services

- [ ] **Example Implementation**
  - [ ] Create example Slack service configuration
  - [ ] Document Slack discovery endpoint requirements
  - [ ] Create Slack service implementation example
  - [ ] Test template with example service

## Security Validation Checklist

### Configuration Security
- [ ] No client secrets in configuration files
- [ ] Environment variables used for sensitive data
- [ ] Configuration file permissions properly set
- [ ] Configuration validation prevents injection attacks

### Discovery Security
- [ ] TLS certificate validation for discovery endpoints
- [ ] Discovery timeout prevents hanging requests
- [ ] Discovered metadata validated against expected values
- [ ] Discovery cache secured with appropriate TTL

### OAuth Security
- [ ] PKCE required for all OAuth flows
- [ ] State parameter validation implemented
- [ ] Redirect URI validation enforced
- [ ] Token storage uses secure keytar service

## Success Criteria

### Functional Requirements
- [ ] OAuth discovery works for GitHub and Figma
- [ ] Configuration-driven service initialization
- [ ] Fallback to static configuration when discovery fails
- [ ] Dynamic service enable/disable via configuration
- [ ] Hot-reload of configuration changes

### Performance Requirements
- [ ] Discovery completes within 10 seconds
- [ ] Discovery cache reduces repeated requests
- [ ] Configuration loading completes within 5 seconds
- [ ] No performance degradation from discovery integration

### Security Requirements
- [ ] All discovery requests use HTTPS
- [ ] No sensitive data in configuration files
- [ ] Discovered metadata validated before use
- [ ] Discovery failures don't compromise security

### Maintainability Requirements
- [ ] Clear separation between discovery and static configuration
- [ ] Consistent error handling across all services
- [ ] Comprehensive logging for troubleshooting
- [ ] Easy addition of new services following template

This checklist ensures comprehensive implementation of OAuth Server Discovery and MCP Configuration while maintaining security, performance, and maintainability standards.
