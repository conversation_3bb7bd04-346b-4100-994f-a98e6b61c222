# Scalable OAuth Architecture for Alpine Intellect

## Overview
This document outlines the scalable OAuth architecture that preserves existing infrastructure while enabling seamless integration of multiple OAuth providers (GitHub, Figma, Slack, Discord, etc.).

## Core Design Principles

### 1. **Preserve Existing Infrastructure**
- ✅ **No modification** to existing Alpine auth system
- ✅ **No changes** to current `handleDeepLink` function logic
- ✅ **Reuse** existing `thealpinecode.alpineintellect://` protocol handler
- ✅ **Maintain** all current authentication flows

### 2. **Extend, Don't Replace**
- ✅ **Build upon** existing `handleDeepLink` function
- ✅ **Add** service detection without altering current logic
- ✅ **Route** new OAuth callbacks to appropriate processors
- ✅ **Preserve** all existing callback handling

### 3. **Independent Scalability**
- ✅ **Enable/disable** each OAuth provider independently
- ✅ **Add** new providers without modifying existing ones
- ✅ **Isolate** provider-specific logic and configuration
- ✅ **Share** common OAuth infrastructure

## Architecture Components

### 1. OAuth Service Registry
**File**: `src/mcp/auth/oauth-service-registry.ts`

```typescript
interface IOAuthService {
  readonly serviceId: string;
  readonly displayName: string;
  readonly callbackPath: string;
  
  initializeOAuth(): Promise<string>; // Returns auth URL
  handleCallback(params: URLSearchParams): Promise<OAuthResult>;
  refreshTokens(): Promise<void>;
  revokeAccess(): Promise<void>;
  getAuthStatus(): Promise<AuthStatus>;
}

class OAuthServiceRegistry {
  private services = new Map<string, IOAuthService>();
  
  registerService(service: IOAuthService): void;
  getService(serviceId: string): IOAuthService | undefined;
  getServiceByCallbackPath(path: string): IOAuthService | undefined;
  getAllServices(): IOAuthService[];
}
```

### 2. Callback Router
**File**: `src/mcp/auth/oauth-callback-router.ts`

```typescript
class OAuthCallbackRouter {
  constructor(private registry: OAuthServiceRegistry) {}
  
  routeCallback(url: string): Promise<OAuthResult> {
    const parsedUrl = new URL(url);
    const service = this.registry.getServiceByCallbackPath(parsedUrl.pathname);
    
    if (service) {
      return service.handleCallback(parsedUrl.searchParams);
    }
    
    throw new Error(`No OAuth service found for callback: ${url}`);
  }
}
```

### 3. Enhanced Deep Link Handler
**File**: `src/electron/main.ts` (Extension, not modification)

```typescript
// EXISTING function remains unchanged
function handleDeepLink(url: string) {
  try {
    const parsedUrl = new URL(url);
    
    // EXISTING Alpine auth logic (unchanged)
    const token = parsedUrl.searchParams.get("token");
    if (token) {
      // ... existing Alpine auth handling
      return;
    }
    
    // NEW: OAuth callback routing (added without modifying existing logic)
    if (parsedUrl.pathname.startsWith('/auth-callback/')) {
      handleOAuthCallback(url);
      return;
    }
    
  } catch (err) {
    console.error("Failed to parse auth callback URL:", url, err);
  }
}

// NEW function for OAuth callbacks
function handleOAuthCallback(url: string) {
  const router = new OAuthCallbackRouter(oauthRegistry);
  router.routeCallback(url)
    .then(result => {
      if (mainWindow) {
        mainWindow.webContents.send("oauth-callback-result", result);
      }
    })
    .catch(error => {
      console.error("OAuth callback error:", error);
      if (mainWindow) {
        mainWindow.webContents.send("oauth-callback-error", error.message);
      }
    });
}
```

## Service Implementation Pattern

### GitHub OAuth Service
**File**: `src/mcp/auth/github-oauth-service.ts`

```typescript
class GitHubOAuthService implements IOAuthService {
  readonly serviceId = 'github';
  readonly displayName = 'GitHub';
  readonly callbackPath = '/auth-callback/github';
  
  async initializeOAuth(): Promise<string> {
    // GitHub-specific OAuth initialization
    const authUrl = `https://github.com/login/oauth/authorize?...`;
    return authUrl;
  }
  
  async handleCallback(params: URLSearchParams): Promise<OAuthResult> {
    // GitHub-specific callback handling
  }
  
  // ... other methods
}
```

### Figma OAuth Service
**File**: `src/mcp/auth/figma-oauth-service.ts`

```typescript
class FigmaOAuthService implements IOAuthService {
  readonly serviceId = 'figma';
  readonly displayName = 'Figma';
  readonly callbackPath = '/auth-callback/figma';
  
  async initializeOAuth(): Promise<string> {
    // Figma-specific OAuth initialization
    const authUrl = `https://www.figma.com/oauth?...`;
    return authUrl;
  }
  
  async handleCallback(params: URLSearchParams): Promise<OAuthResult> {
    // Figma-specific callback handling
  }
  
  // ... other methods
}
```

## URL Patterns and Routing

### Callback URL Structure
```
thealpinecode.alpineintellect://auth-callback/{service}?code=...&state=...
```

### Service-Specific URLs
- **Alpine Auth**: `thealpinecode.alpineintellect://?token=...` (unchanged)
- **GitHub OAuth**: `thealpinecode.alpineintellect://auth-callback/github?code=...&state=...`
- **Figma OAuth**: `thealpinecode.alpineintellect://auth-callback/figma?code=...&state=...`
- **Future Services**: `thealpinecode.alpineintellect://auth-callback/{service}?...`

## Token Storage Strategy

### Keytar Integration
All services use the same keytar service name: `"alpine-app"`

```typescript
// Service-specific token keys
const TOKEN_KEYS = {
  github: {
    accessToken: 'github-access-token',
    metadata: 'github-token-metadata'
  },
  figma: {
    accessToken: 'figma-access-token',
    refreshToken: 'figma-refresh-token',
    expiresAt: 'figma-token-expires-at',
    metadata: 'figma-token-metadata'
  }
};
```

## Adding New OAuth Providers

### Step-by-Step Process
1. **Create Service Class**: Implement `IOAuthService` interface
2. **Register Service**: Add to OAuth service registry
3. **Add UI Components**: Create service-specific UI components
4. **Configure Callback**: Service automatically handled by router
5. **Test Integration**: Verify independent operation

### Example: Adding Slack OAuth
```typescript
class SlackOAuthService implements IOAuthService {
  readonly serviceId = 'slack';
  readonly displayName = 'Slack';
  readonly callbackPath = '/auth-callback/slack';
  
  // Implement interface methods...
}

// Registration
oauthRegistry.registerService(new SlackOAuthService());
```

## Benefits of This Architecture

1. **Zero Breaking Changes**: Existing systems continue to work unchanged
2. **Independent Services**: Each OAuth provider can be enabled/disabled independently
3. **Scalable Design**: Adding new providers requires minimal code
4. **Consistent Patterns**: All OAuth providers follow the same patterns
5. **Maintainable Code**: Clear separation of concerns and responsibilities
6. **Future-Proof**: Architecture supports unlimited OAuth providers

## Implementation Checklist

- [ ] Create OAuth service registry and interface
- [ ] Implement callback router
- [ ] Extend deep link handler (without modification)
- [ ] Create GitHub OAuth service
- [ ] Create Figma OAuth service
- [ ] Add service detection logic
- [ ] Implement UI components for each service
- [ ] Test independent enable/disable functionality
- [ ] Verify backward compatibility
- [ ] Document patterns for future providers
