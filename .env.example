# Alpine Intellect MCP Configuration
# Copy this file to .env and fill in your actual values

# GitHub OAuth Configuration
# Get these from: https://github.com/settings/applications/new
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=2ad5c31e6d83182d97330bdcd512d0e6158419c5

# Figma OAuth Configuration  
# Get these from: https://www.figma.com/developers/api#oauth2
FIGMA_CLIENT_ID=your_figma_client_id_here
FIGMA_CLIENT_SECRET=your_figma_client_secret_here

# Optional: Development settings
NODE_ENV=development
DEBUG=mcp:*
